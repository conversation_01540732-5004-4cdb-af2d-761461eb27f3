import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface SidebarState {
  isCollapsed: boolean
  isMobileOpen: boolean
  setCollapsed: (collapsed: boolean) => void
  setMobileOpen: (open: boolean) => void
  toggleSidebar: () => void
}

export const useSidebarStore = create<SidebarState>()(
  persist(
    (set, get) => ({
      isCollapsed: false,
      isMobileOpen: false,
      setCollapsed: (collapsed: boolean) => set({ isCollapsed: collapsed }),
      setMobileOpen: (open: boolean) => set({ isMobileOpen: open }),
      toggleSidebar: () => {
        // Mobile vs desktop behavior
        if (typeof window !== 'undefined' && window.innerWidth < 768) {
          set({ isMobileOpen: !get().isMobileOpen })
        } else {
          set({ isCollapsed: !get().isCollapsed })
        }
      },
    }),
    {
      name: 'sidebar-storage',
      partialize: (state: SidebarState) => ({ isCollapsed: state.isCollapsed }), // Only persist collapse state
    }
  )
)

// Add a listener for window resize to close mobile sidebar on desktop
if (typeof window !== 'undefined') {
  window.addEventListener('resize', () => {
    const { isMobileOpen, setMobileOpen } = useSidebarStore.getState()
    if (window.innerWidth >= 768 && isMobileOpen) {
      setMobileOpen(false)
    }
  })
}
