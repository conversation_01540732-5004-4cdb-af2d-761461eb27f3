'use client'

import React from 'react'
import Link from 'next/link'
import { usePara<PERSON>, usePathname } from 'next/navigation'
import { useSidebarStore } from '@/store/sidebar-store'
import { IconChevronDown, IconChevronRight } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { cn } from '@/lib/utils'

import { useTranslation } from '@/utils/i18n'

import { LanguageSwitch } from '@/components/language-switch'
import { Logo } from '@/components/logo-icon'
import { navLinks, type NavLink } from '@/components/nav-links'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tooltip } from '@/components/ui/tooltip'

// NavLink Item Component
const NavLinkItem = ({ item, isCollapsed }: { item: NavLink; isCollapsed: boolean }) => {
  // Handle regular link
  if (item.type === 'link') {
    if (isCollapsed) {
      return (
        <Tooltip label={item.title} contentOptions={{ side: 'right' }}>
          <Link
            href={item.href}
            className={cn(
              'focus-ring flex cursor-pointer justify-center rounded-md px-3 py-2',
              item.active ? 'bg-ac/5 text-ac' : 'hover:bg-ac/5'
            )}
          >
            {item.icon &&
              React.cloneElement(item.icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}
            {item.iconCmdk &&
              React.cloneElement(item.iconCmdk as React.ReactElement<{ className?: string }>, { className: 'size-5' })}
          </Link>
        </Tooltip>
      )
    }

    return (
      <Link
        href={item.href}
        className={cn(
          'focus-ring flex cursor-pointer items-center gap-2 rounded-md px-3 py-2',
          item.active ? 'bg-ac/5 text-ac' : 'hover:bg-ac/5',
          isCollapsed ? 'justify-center' : ''
        )}
      >
        {item.icon &&
          React.cloneElement(item.icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}
        {item.iconCmdk &&
          React.cloneElement(item.iconCmdk as React.ReactElement<{ className?: string }>, { className: 'size-5' })}
        {!isCollapsed && <span>{item.title}</span>}
      </Link>
    )
  }

  // Handle menu item
  if (isCollapsed) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger className='focus-ring w-full rounded-md'>
          <Button
            variant='link'
            className={cn(
              'w-full justify-center rounded-md px-3 py-2',
              item.active ? 'bg-ac/10 text-ac' : 'hover:bg-ac/10'
            )}
          >
            {item.icon &&
              React.cloneElement(item.icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='start' className='w-60'>
          {item.links?.map(subItem => (
            <DropdownMenuItem key={subItem.link} asChild>
              <a
                href={subItem.link}
                target={subItem.newTab ? '_blank' : undefined}
                rel={subItem.newTab ? 'noreferrer' : undefined}
                className='flex cursor-pointer flex-col gap-1'
              >
                <span className='font-medium'>{subItem.label}</span>
                {subItem.desc && <span className='text-fg/60 text-sm font-normal'>{subItem.desc}</span>}
              </a>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className='focus-ring w-full rounded-md'>
        <div
          className={cn(
            'flex w-full items-center justify-start gap-2 rounded-md px-3 py-2',
            item.active ? 'bg-ac/5 text-ac' : 'hover:bg-ac/5'
          )}
        >
          {item.icon &&
            React.cloneElement(item.icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}
          <span>{item.title}</span>
          <IconChevronDown className='ml-auto h-4 w-4' />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start' className='w-60'>
        {item.links?.map(subItem => (
          <DropdownMenuItem key={subItem.link} asChild>
            <a
              href={subItem.link}
              target={subItem.newTab ? '_blank' : undefined}
              rel={subItem.newTab ? 'noreferrer' : undefined}
              className='flex cursor-pointer flex-col gap-1'
            >
              <span className='font-medium'>{subItem.label}</span>
              <span className='text-fg/60 text-sm font-normal'>{subItem.desc}</span>
            </a>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default function SidebarNav() {
  const pathname = usePathname()
  const params = useParams<{ lang: Locale }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''
  const { isCollapsed, isMobileOpen, setCollapsed, setMobileOpen } = useSidebarStore()
  const { t } = useTranslation()

  const links = navLinks(resolvedBase, pathname, params?.lang || 'en')

  return (
    <>
      {/* Mobile backdrop */}
      {isMobileOpen && (
        <div className='fixed inset-0 z-30 bg-black/50 md:hidden' onClick={() => setMobileOpen(false)} />
      )}

      <div
        className={cn(
          'bg-bg md:shadow-border-r fixed z-50 flex h-screen flex-col md:relative',
          isCollapsed ? 'w-16' : 'w-64',
          // Mobile visibility based on isMobileOpen state
          isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        )}
      >
        <div className='flex h-12 items-center justify-between p-3'>
          {/* Logo in sidebar */}
          <Link
            href={`${resolvedBase}/`}
            className={cn(
              'flex items-center gap-2 transition-opacity hover:opacity-80',
              isCollapsed ? 'w-full justify-center' : ''
            )}
          >
            <Logo className='h-7 w-7' />
            {!isCollapsed && <span className='text-lg font-bold'>{t.common.title}</span>}
          </Link>
        </div>

        <ScrollArea className='flex-1'>
          <div className='space-y-0.5 px-2 pb-2'>
            {links.map((item, idx) => (
              <React.Fragment key={idx}>
                {/* Mobile only links */}
                {item.mobileOnly ? (
                  <div className='md:hidden'>
                    <NavLinkItem item={item} isCollapsed={isCollapsed} />
                  </div>
                ) : (
                  <NavLinkItem item={item} isCollapsed={isCollapsed} />
                )}
              </React.Fragment>
            ))}
          </div>
        </ScrollArea>

        <div className={cn('flex border-t p-3', isCollapsed ? 'flex-col items-center gap-3 py-4' : 'items-center')}>
          {isCollapsed ? (
            <>
              <LanguageSwitch isCollapsed={isCollapsed} />
              <ThemeSwitch isCollapsed={isCollapsed} />
              <Button variant='link' size='icon' onClick={() => setCollapsed(false)} className='h-8 w-8'>
                <IconChevronRight className='h-4 w-4' />
                <span className='sr-only'>Expand sidebar</span>
              </Button>
            </>
          ) : (
            <div className='flex items-center gap-2'>
              <LanguageSwitch isCollapsed={isCollapsed} />
              <ThemeSwitch isCollapsed={isCollapsed} />
            </div>
          )}
        </div>
      </div>
    </>
  )
}
