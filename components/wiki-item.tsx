import Link from 'next/link'
import { useParams } from 'next/navigation'
import uniqolor from 'uniqolor'
import { IconNotes } from '@tabler/icons-react'

import type { HyperApiPost } from '@/types'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'

import PageBody from '@/components/page-body'
import Timestamp from '@/components/timestamp'
import { Card, CardContent } from '@/components/ui/card'

export default function WikiItem({ data }: { data: HyperApiPost }) {
  const params = useParams<{ lang: string }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  const baseColor = uniqolor(elementToString(data.title.rendered), {
    lightness: 60,
  }).color

  return (
    <Link href={`${resolvedBase}/wiki/${data.id}`} className='block'>
      <Card
        className='w-full transition-shadow hover:shadow-xl'
        style={{
          color: `color-mix(in oklab, ${baseColor} 50%, var(--color-fg))`,
          backgroundColor: `color-mix(in oklab, ${baseColor} 10%, transparent)`,
          borderColor: baseColor,
        }}
      >
        <CardContent className='space-y-1 px-3 py-2'>
          <h3 className='text-base'>
            <div className='line-clamp-1 flex items-center gap-1'>
              <IconNotes size='1.25em' className='shrink-0' />
              <div className='line-clamp-1'>{encodeTitle(data.title.rendered)}</div>
            </div>
          </h3>

          <div className='mt-1'>
            <PageBody content={data.excerpt.rendered} clamp={2} />
          </div>

          <div className='text-fg/60 flex items-center text-xs'>
            <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
