'use client'

import React from 'react'
import { Map, Marker } from 'pigeon-maps'

import { WORKERS_BASE } from '@/lib/constants'

export default function MapContainer({ lat, lng }: { lat: number; lng: number }) {
  function tileProvider(x: number, y: number, z: number) {
    // const hires = dpr && dpr >= 2 ? '@2x' : ''
    // const mapTiler = `https://api.maptiler.com/maps/sattlate/${z}/${x}/${y}${hires}.jpg?key=EtvzZgFxfGtMlze89Htl`
    // const osm = `https://tile.openstreetmap.org/${z}/${x}/${y}.png`
    // const slpy = `https://api.slpy.com/v1/app/aerial-topo/${z}/${x}/${y}.png?key=6eac494280be59bfde45ebc65`
    // const mapbox = `https://api.mapbox.com/v4/mapbox.mapbox-streets-v8,mapbox.mapbox-terrain-v2,mapbox.mapbox-bathymetry-v2/${z}/${x}/${y}${hires}.png?access_token=pk.eyJ1Ijoic3BhcmFub2lkIiwiYSI6ImNsdXV3dmJzbzBmaTcya24xaXRsNGg0d3UifQ.lvSwdvZgNOFCVX1hkpvteQ`
    // const stadia = `https://tiles.stadiamaps.com/data/stamen_watercolor_v1/${z}/${x}/${y}${hires}.jpg?api_key=de2f9995-5385-4266-91ab-21adc86fcefb`
    const stadia = `${WORKERS_BASE}/api/experiments/stadiamaps/stamen_watercolor_v1/${z}/${x}/${y}.jpg?api_key=de2f9995-5385-4266-91ab-21adc86fcefb`

    return stadia
  }

  return (
    <div className='flex overflow-hidden rounded-lg'>
      <Map
        provider={tileProvider}
        height={300}
        defaultCenter={[lat, lng]}
        defaultZoom={10}
        minZoom={2}
        maxZoom={12}
        attribution={false}
        attributionPrefix={false}
      >
        <Marker width={50} anchor={[lat, lng]} color={`hsl(350deg 90% 60%)`} />
      </Map>
    </div>
  )
}
