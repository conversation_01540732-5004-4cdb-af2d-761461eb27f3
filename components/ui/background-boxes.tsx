'use client'

import React from 'react'

import { cn } from '@/lib/utils'

export const BoxesCore = ({ className, ...rest }: { className?: string }) => {
  const rows = new Array(52).fill(1)
  const cols = new Array(30).fill(1)
  // let colors = ['#93c5fd', '#f9a8d4', '#86efac', '#fde047', '#fca5a5', '#d8b4fe', '#93c5fd', '#a5b4fc', '#c4b5fd']
  const colors = [
    'color-mix(in oklch, var(--color-red-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-blue-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-green-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-yellow-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-purple-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-pink-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-orange-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-teal-400) 60%, transparent)',
    'color-mix(in oklch, var(--color-cyan-400) 60%, transparent)',
  ]
  const getRandomColor = () => {
    return colors[Math.floor(Math.random() * colors.length)]
  }

  return (
    <div
      style={{
        transform: `translate(-10%,-60%) skewX(-48deg) skewY(14deg) scale(0.675) rotate(0deg) translateZ(0)`,
      }}
      className={cn('absolute -top-1/2 left-1/2 z-0 flex h-full w-full -translate-x-1/2 -translate-y-1/2', className)}
      {...rest}
    >
      {rows.map((_, i) => (
        <div key={`row` + i} className='border-fg/10 relative h-8 w-16 border-l'>
          {cols.map((_, j) => (
            <div
              key={`col` + j}
              className='border-fg/10 relative h-8 w-16 border-t border-r transition-colors duration-2000 hover:bg-[var(--random-color)] hover:duration-0'
              style={
                {
                  '--random-color': getRandomColor(),
                } as React.CSSProperties
              }
            >
              {j % 2 === 0 && i % 2 === 0 ? (
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  strokeWidth='1.5'
                  stroke='currentColor'
                  className='text-ac/20 pointer-events-none absolute -top-[14px] -left-[22px] h-6 w-10 stroke-[1px]'
                >
                  <path strokeLinecap='round' strokeLinejoin='round' d='M12 6v12m6-6H6' />
                </svg>
              ) : null}
            </div>
          ))}
        </div>
      ))}
    </div>
  )
}

export const BackgroundBoxes = React.memo(BoxesCore)
