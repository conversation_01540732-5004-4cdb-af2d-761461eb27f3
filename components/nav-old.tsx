'use client'

import React from 'react'
import Link from 'next/link'
import { useParams, usePathname } from 'next/navigation'
import { IconChevronDown } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { cn } from '@/lib/utils'

import { Command } from '@/components/command-algolia'
import { LanguageSwitch } from '@/components/language-switch'
import LoginLinks from '@/components/login-links'
import { Logo } from '@/components/logo-icon'
import { navLinks } from '@/components/nav-links'
import { ThemeSwitch } from '@/components/theme-switch'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown'
import { ScrollArea } from '@/components/ui/scroll-area'

export default function NavOld() {
  const pathname = usePathname()
  const params = useParams<{ lang: Locale }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  const links = navLinks(resolvedBase, pathname, params?.lang || 'en')

  return (
    <nav className='bg-bg shadow-border-b sticky top-0 z-10'>
      <div className='mx-auto max-w-[1540px] px-4'>
        <div className='flex h-16 items-center justify-between'>
          <div className='flex min-w-0 items-center'>
            <Link href={`${resolvedBase}/`} className='-ml-1 flex items-center'>
              <Logo className='h-10 w-10' />
            </Link>

            <ScrollArea
              className='h-full max-w-full'
              viewportProps={{
                className:
                  '[mask-image:linear-gradient(to_right,transparent,black_1rem,black_calc(100%-1rem),transparent)]',
              }}
            >
              <div className='flex'>
                {links.map((item, idx) => {
                  if (item.mobileOnly) {
                    return (
                      <div key={idx} className='md:hidden'>
                        <Link
                          href={item.href}
                          className={cn(
                            'focus-ring hover:text-ac flex items-center gap-1 px-3 py-2 font-medium',
                            item.active ? 'text-ac' : 'text-fg'
                          )}
                        >
                          {item.icon}
                          <span className='whitespace-nowrap'>{item.title}</span>
                        </Link>
                      </div>
                    )
                  }

                  if (item.type === 'menu') {
                    return (
                      <DropdownMenu key={idx}>
                        <DropdownMenuTrigger className='focus-ring hover:text-ac flex items-center gap-1 px-3 py-2 font-medium'>
                          {item.icon}
                          <span className='whitespace-nowrap'>{item.title}</span>
                          <IconChevronDown className='h-4 w-4' />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='start' className='max-w-80'>
                          {item.links?.map(subItem => (
                            <DropdownMenuItem key={subItem.link} asChild>
                              <a
                                href={subItem.link}
                                target={subItem.newTab ? '_blank' : undefined}
                                rel={subItem.newTab ? 'noreferrer' : undefined}
                                className='flex flex-col gap-1'
                              >
                                <span className='font-medium'>{subItem.label}</span>
                                <span className='text-fg/60 text-sm font-normal'>{subItem.desc}</span>
                              </a>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )
                  }

                  return (
                    <Link
                      key={idx}
                      href={item.href}
                      className={cn(
                        'focus-ring hover:text-ac flex items-center gap-1 px-3 py-2 font-medium',
                        item.active ? 'text-ac' : 'text-fg'
                      )}
                    >
                      {item.icon}
                      <span className='whitespace-nowrap'>{item.title}</span>
                    </Link>
                  )
                })}
              </div>
            </ScrollArea>
          </div>

          <div className='flex items-center gap-2'>
            {/* <div className='hidden md:block'>
              <AlgoliaSearchWrapper />
            </div> */}
            <Command />
            <ThemeSwitch />
            <LanguageSwitch />
            <LoginLinks />
          </div>
        </div>
      </div>
    </nav>
  )
}
