import { useRef, useState } from 'react'
import clsx from 'clsx'
import {
  SearchBox,
  useHierarchicalMenu,
  useHits,
  useInstantSearch,
  usePagination,
  useRefinementList,
  useSearchBox,
  type UseHierarchicalMenuProps,
  type UseHitsProps,
  type UsePaginationProps,
  type UseRefinementListProps,
  type UseSearchBoxProps,
} from 'react-instantsearch'
import { IconCategory2 } from '@tabler/icons-react'

import type { HyperPostAlgoliaHit, HyperTag } from '@/types'

import { Pagination } from '@/components/ui/pagination'
import { ScrollArea } from '@/components/ui/scroll-area'

import { GeneralItem } from './general-item'
import classes from './nav.module.css'
import Tag from './tag'

export function AlgoliaHit({ hardLink, hit }: { hit: HyperPostAlgoliaHit; hardLink?: boolean }) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`hit ${hit.post_type}`, hit)
  }

  // Transform into HyperApiPost
  // News post
  // if (hit.post_type === 'post') {
  //   const data = {
  //     // Force `post_modified` since algolia sorted items by post_modified by default
  //     date: new Date(hit.post_modified * 1000).toString(),
  //     date_gmt: new Date(hit.post_modified * 1000).toString(),
  //     guid: {
  //       rendered: hit.permalink,
  //     },
  //     id: hit.post_id,
  //     link: hit.permalink,
  //     modified: new Date(hit.post_modified * 1000).toString(),
  //     modified_gmt: new Date(hit.post_modified * 1000).toString(),
  //     slug: hit.post_id.toString(),
  //     status: 'publish',
  //     type: hit.post_type,
  //     title: {
  //       rendered: <Highlight attribute='post_title' hit={hit} />,
  //     },
  //     content: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     author: hit.post_author.user_id,
  //     excerpt: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     comment_status: 'closed',
  //     ping_status: 'closed',
  //     meta: [],
  //     _links: {
  //       self: [
  //         {
  //           href: hit.permalink,
  //         },
  //       ],
  //     },
  //     _embedded: {
  //       'wp:featuredmedia': [
  //         {
  //           media_details: {
  //             sizes: {
  //               large: {
  //                 source_url: hit.images.thumbnail?.url,
  //               },
  //             },
  //           },
  //           source_url: hit.images.thumbnail?.url,
  //         },
  //       ],
  //     },
  //   } as HyperApiPost

  //   return (
  //     <div className='masonryItemWrap'>
  //       <NewsItem data={data} hardLink simple />
  //     </div>
  //   )
  // }

  // Tutorials
  // if (hit.post_type === 'tutorials') {
  //   const data = {
  //     date: new Date(hit.post_modified * 1000).toString(),
  //     date_gmt: new Date(hit.post_modified * 1000).toString(),
  //     guid: {
  //       rendered: hit.permalink,
  //     },
  //     id: hit.post_id,
  //     link: hit.permalink,
  //     modified: new Date(hit.post_modified * 1000).toString(),
  //     modified_gmt: new Date(hit.post_modified * 1000).toString(),
  //     slug: hit.post_id.toString(),
  //     status: 'publish',
  //     type: hit.post_type,
  //     title: {
  //       rendered: <Highlight attribute='post_title' hit={hit} />,
  //     },
  //     content: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     author: hit.post_author.user_id,
  //     excerpt: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     comment_status: 'closed',
  //     ping_status: 'closed',
  //     meta: [],
  //     _links: {
  //       self: [
  //         {
  //           href: hit.permalink,
  //         },
  //       ],
  //     },
  //     // _embedded: {
  //     //   'wp:featuredmedia': [
  //     //     {
  //     //       source_url: hit.images.thumbnail.url,
  //     //     }
  //     //   ]
  //     // }
  //   } as HyperApiPost

  //   return (
  //     <div className='masonryItemWrap'>
  //       {/* {hit.post_type}
  //       {hit.post_title}
  //       <Highlight attribute="post_title" hit={hit} /> */}
  //       <TutorialItem data={data} />
  //     </div>
  //   )
  // }

  // Wiki
  // if (hit.post_type === 'wiki') {
  //   const data = {
  //     date: new Date(hit.post_modified * 1000).toString(),
  //     date_gmt: new Date(hit.post_modified * 1000).toString(),
  //     guid: {
  //       rendered: hit.permalink,
  //     },
  //     id: hit.post_id,
  //     link: hit.permalink,
  //     modified: new Date(hit.post_modified * 1000).toString(),
  //     modified_gmt: new Date(hit.post_modified * 1000).toString(),
  //     slug: hit.post_id.toString(),
  //     status: 'publish',
  //     type: hit.post_type,
  //     title: {
  //       rendered: <Highlight attribute='post_title' hit={hit} />,
  //     },
  //     content: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     author: hit.post_author.user_id,
  //     excerpt: {
  //       rendered: hit.post_excerpt,
  //       protected: false,
  //     },
  //     comment_status: 'closed',
  //     ping_status: 'closed',
  //     meta: [],
  //     _links: {
  //       self: [
  //         {
  //           href: hit.permalink,
  //         },
  //       ],
  //     },
  //     // _embedded: {
  //     //   'wp:featuredmedia': [
  //     //     {
  //     //       source_url: hit.images.thumbnail.url,
  //     //     }
  //     //   ]
  //     // }
  //   } as HyperApiPost

  //   return (
  //     <div className='masonryItemWrap'>
  //       {/* {hit.post_type}
  //       {hit.post_title}
  //       <Highlight attribute="post_title" hit={hit} /> */}
  //       <WikiItem data={data} />
  //     </div>
  //   )
  // }

  // Datasets
  // if (hit.post_type === 'datasets') {
  //   const data = {
  //     date: new Date(hit.post_modified * 1000).toString(),
  //     date_gmt: new Date(hit.post_modified * 1000).toString(),
  //     guid: {
  //       rendered: hit.permalink,
  //     },
  //     id: hit.post_id,
  //     link: hit.permalink,
  //     modified: new Date(hit.post_modified * 1000).toString(),
  //     modified_gmt: new Date(hit.post_modified * 1000).toString(),
  //     slug: hit.post_id.toString(),
  //     status: 'publish',
  //     type: hit.post_type,
  //     title: {
  //       rendered: <Highlight attribute='post_title' hit={hit} />,
  //     },
  //     content: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     author: hit.post_author.user_id,
  //     excerpt: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     comment_status: 'closed',
  //     ping_status: 'closed',
  //     meta: [],
  //     _links: {
  //       self: [
  //         {
  //           href: hit.permalink,
  //         },
  //       ],
  //     },
  //     // _embedded: {
  //     //   'wp:featuredmedia': [
  //     //     {
  //     //       source_url: hit.images.thumbnail.url,
  //     //     }
  //     //   ]
  //     // }
  //   } as HyperApiPost

  //   return (
  //     <div className='masonryItemWrap'>
  //       {/* {hit.post_type}
  //       {hit.post_title}
  //       <Highlight attribute="post_title" hit={hit} /> */}
  //       <DatasetItem data={data} />
  //     </div>
  //   )
  // }

  // Events
  // if (hit.post_type === 'events') {
  //   const data = {
  //     date: new Date(hit.post_modified * 1000).toString(),
  //     date_gmt: new Date(hit.post_modified * 1000).toString(),
  //     guid: {
  //       rendered: hit.permalink,
  //     },
  //     id: hit.post_id,
  //     link: hit.permalink,
  //     modified: new Date(hit.post_modified * 1000).toString(),
  //     modified_gmt: new Date(hit.post_modified * 1000).toString(),
  //     slug: hit.post_id.toString(),
  //     status: 'publish',
  //     type: hit.post_type,
  //     title: {
  //       rendered: <Highlight attribute='post_title' hit={hit} />,
  //     },
  //     content: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     author: hit.post_author.user_id,
  //     excerpt: {
  //       rendered: hit.content,
  //       protected: false,
  //     },
  //     comment_status: 'closed',
  //     ping_status: 'closed',
  //     meta: [],
  //     _links: {
  //       self: [
  //         {
  //           href: hit.permalink,
  //         },
  //       ],
  //     },
  //     // _embedded: {
  //     //   'wp:featuredmedia': [
  //     //     {
  //     //       source_url: hit.images.thumbnail.url,
  //     //     }
  //     //   ]
  //     // }
  //   } as HyperApiPost

  //   return (
  //     <div className='masonryItemWrap'>
  //       {/* {hit.post_type}
  //       {hit.post_title}
  //       <Highlight attribute="post_title" hit={hit} /> */}
  //       <EventItem data={data} />
  //     </div>
  //   )
  // }

  // Fallback component
  return (
    <div>
      <GeneralItem data={hit} hardLink={hardLink} />
    </div>
  )
}

export function AlgoliaHits(props: UseHitsProps) {
  // TODO: need better design
  const { hits } = useHits(props) as unknown as { hits: HyperPostAlgoliaHit[] }

  return (
    <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
      {hits.map(hit => (
        <AlgoliaHit key={hit.objectID} hit={hit} />
      ))}
    </div>
  )
}

export function AlgoliaHitsNav({ ...props }: UseHitsProps) {
  const { query } = useSearchBox()

  // TODO: need better design
  const { hits } = useHits(props) as unknown as { hits: HyperPostAlgoliaHit[] }

  return query ? (
    <div className={`${classes.algoliaResult}`}>
      <ScrollArea className='bg-bg h-[calc(80vh-60px)]'>
        <div className='mx-auto max-w-[1510px] py-4'>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-3 lg:grid-cols-4'>
            {hits.map(hit => (
              <AlgoliaHit key={hit.objectID} hit={hit} hardLink />
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  ) : (
    <></>
  )
}

export function AlgoliaSearchBox() {
  return <SearchBox className='searchBox' placeholder='全站搜索…' />
}

export function AlgoliaNavSearchBox(props: UseSearchBoxProps) {
  const { query, refine } = useSearchBox(props)
  const { status } = useInstantSearch()
  const [inputValue, setInputValue] = useState(query)
  const inputRef = useRef<HTMLInputElement>(null)

  const isSearchStalled = status === 'stalled'

  function setQuery(newQuery: string) {
    setInputValue(newQuery)

    refine(newQuery)
  }

  return (
    <div>
      <form
        action=''
        role='search'
        noValidate
        onSubmit={event => {
          event.preventDefault()
          event.stopPropagation()

          if (inputRef.current) {
            inputRef.current.blur()
          }
        }}
        onReset={event => {
          event.preventDefault()
          event.stopPropagation()

          setQuery('')

          if (inputRef.current) {
            inputRef.current.focus()
          }
        }}
      >
        <input
          ref={inputRef}
          autoComplete='off'
          autoCorrect='off'
          autoCapitalize='off'
          placeholder='全站搜索'
          spellCheck={false}
          maxLength={512}
          type='search'
          value={inputValue}
          onChange={event => {
            setQuery(event.currentTarget.value)
          }}
          autoFocus
        />
        <span hidden={!isSearchStalled}>正在搜索…</span>
      </form>
    </div>
  )
}

// https://www.algolia.com/doc/guides/building-search-ui/what-is-instantsearch/react/#using-hooks
export function AlgoliaRefinementList({
  title,
  ...props
}: UseRefinementListProps & {
  title?: React.ReactNode
}) {
  // Retrieves the refinement `items` and the `refine` function to update the
  // refinement
  const { items, refine } = useRefinementList(props)

  return (
    <>
      {title && <h3 className='mt-6 mb-2 text-lg font-medium'>{title}</h3>}
      <div className='relative'>
        <div className='flex flex-wrap gap-2'>
          {items.map(item => {
            // Transform data into HyperTag
            const data = {
              count: item.count,
              id: 0,
              name: item.label,
              slug: item.value,
              taxonomy: 'post_tag',
            } as HyperTag

            return (
              <div key={item.value} className={clsx('cursor-pointer')} onClick={() => refine(item.value)}>
                <Tag data={data} active={item.isRefined} showCount />
              </div>
            )
          })}
        </div>
      </div>
    </>
  )
}

export function AlgoliaHierarchicalMenu({
  title,
  ...props
}: UseHierarchicalMenuProps & {
  title?: React.ReactNode
}) {
  // Retrieves the refinement `items` and the `refine` function to update the
  // refinement
  const { items, refine } = useHierarchicalMenu(props)

  return (
    <>
      {title && <h3 className='mt-6 mb-2 text-lg font-medium'>{title}</h3>}

      <div className='relative'>
        <div className='flex flex-wrap gap-2'>
          {items.map(item => {
            // Transform data into HyperTag
            const data = {
              count: item.count,
              id: 0,
              name: item.label,
              slug: item.value,
              taxonomy: 'post_tag',
            } as HyperTag

            return (
              <div key={item.value} className='cursor-pointer' onClick={() => refine(item.value)}>
                <Tag data={data} active={item.isRefined} showCount icon={<IconCategory2 size={'1em'} />} />
              </div>
            )
          })}
        </div>
      </div>
    </>
  )
}

export function AlgoliaPagination(props: UsePaginationProps) {
  const { currentRefinement, nbPages, refine } = usePagination(props)

  return (
    <div className='mt-4 flex justify-center'>
      <Pagination value={currentRefinement + 1} onPageChange={value => refine(value - 1)} total={nbPages} />
    </div>
  )
}
