import Link from 'next/link'
import { useParams } from 'next/navigation'
import uniqolor from 'uniqolor'
import { IconDatabase } from '@tabler/icons-react'

import type { HyperApiPost } from '@/types'

import { encodeTitle } from '@/utils/encodeTitle'

import { Card, CardContent } from '@/components/ui/card'

import Organization from './organization'
import Timestamp from './timestamp'

export default function DatasetItem({ data }: { data: HyperApiPost }) {
  const params = useParams<{ lang: string }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  const cateStr =
    data.hyperai_categories && data.hyperai_categories.length > 0
      ? data.hyperai_categories
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(cate => cate.name)
          .join(',')
      : '未知分类'

  const textColor = uniqolor(cateStr, {
    lightness: 60,
  }).color

  return (
    <Link href={`${resolvedBase}/datasets/${data.id}`} className='block'>
      <Card className='w-full transition-shadow hover:shadow-xl'>
        <CardContent className='space-y-1 p-2'>
          <h3 className='text-base leading-none'>
            <div className='line-clamp-1 flex items-center gap-1'>
              <IconDatabase size='1.25em' style={{ color: textColor }} />
              <div className='line-clamp-1'>{encodeTitle(data.title.rendered)}</div>
            </div>
          </h3>

          <div className='text-fg/60 flex flex-wrap items-center justify-between gap-1 text-xs'>
            <div className='flex flex-wrap items-center gap-1'>
              {data.hyperai_organizations && data.hyperai_organizations.length > 0 && (
                <div className='flex gap-1'>
                  {data.hyperai_organizations.map((org, idx) => {
                    return <Organization key={idx} data={org} />
                  })}
                </div>
              )}

              {data.hyperai_categories &&
                data.hyperai_categories.length > 0 &&
                data.hyperai_categories
                  .filter(term => term.taxonomy === 'datasets-category')
                  .map((term, idx) => {
                    return <span key={idx}>{term.name}</span>
                  })}
            </div>

            <div>
              <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
