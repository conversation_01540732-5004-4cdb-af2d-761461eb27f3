'use client'

import Script from 'next/script'
import { IconAt, IconMail, IconNews } from '@tabler/icons-react'

import { S3_ASSETS_BASE } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'

import Headlines from '@/components/headlines'
import LiveBanner from '@/components/sidebar-livestream'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function Sidebar() {
  const { t, translate, locale } = useTranslation()

  return (
    <>
      <Script async src='https://wwads.cn/js/ads.js' />

      <aside>
        <section className={'border-ac bg-bg relative mt-4 flex items-center rounded-lg border-4 p-6'}>
          <div>
            <h1 className={'text-ac bg-bg absolute left-4 -mt-[54px] flex items-center px-2 py-4 text-xl leading-none'}>
              <IconNews className='shrink-0' />
              Hyper Newsletters
            </h1>
            <div className='text-lg font-bold'>{t.sidebar?.newsletter?.title}</div>
            <div className='text-fg/80 mt-2 text-lg'>
              {t.sidebar?.newsletter?.description} <b>{t.sidebar?.newsletter?.time}</b>{' '}
              {t.sidebar?.newsletter?.delivery}
            </div>

            <form
              action='https://openbayes.us18.list-manage.com/subscribe/post?u=1e1ca1e93070c21ac47fca58a&id=fdefb6b723'
              method='post'
            >
              <div className='mt-4 flex'>
                <div className='relative flex-1'>
                  <IconAt className='text-fg/60 absolute top-1/2 left-3 size-4 -translate-y-1/2' />
                  <Input
                    id='subscribe-field'
                    name='EMAIL'
                    required
                    type='email'
                    placeholder={t.sidebar?.newsletter?.emailPlaceholder}
                    className='rounded-r-none border-r-0 pl-9'
                  />
                </div>
                <Button variant={'outline'} tint={'accent'} type='submit' className='rounded-l-none'>
                  {t.sidebar?.newsletter?.submitButton || '订阅'}
                </Button>
              </div>
            </form>
          </div>
        </section>

        <div className='mt-2 text-sm'>
          <div className='flex flex-wrap items-center gap-1'>
            {translate('sidebar.newsletter.provider', {
              provider: (
                <a
                  href='http://eepurl.com/dsgDpX'
                  target='_blank'
                  rel='noopener noreferer'
                  className='text-ac flex items-center gap-1 hover:underline'
                >
                  <IconMail /> MailChimp
                </a>
              ),
            })}
          </div>
        </div>

        <Headlines />

        {locale === 'cn' && (
          <>
            <LiveBanner />

            <div className='mt-6'>
              <a
                href='https://openbayes.com/?utm_source=hyperai&utm_medium=sidebar-banner&utm_campaign=hyperai-sidebar'
                target='_blank'
                rel='noopenner'
                className='flex'
              >
                {/* eslint-disable @next/next/no-img-element */}
                <img
                  width='100%'
                  className='rounded-lg'
                  src={`${S3_ASSETS_BASE}/2023/12/qy7froj.png`}
                  alt={t.sidebar?.openbayes?.alt}
                />
              </a>
            </div>

            <div className='mt-6 rounded-lg border-2 border-[#006]'>
              <a
                href='https://tvm.hyper.ai/?utm_source=hyperai&utm_medium=sidebar-banner&utm_campaign=hyperai-sidebar'
                target='_blank'
                rel='noopenner'
                className='flex'
              >
                {/* eslint-disable @next/next/no-img-element */}
                <img
                  width='100%'
                  className='rounded-lg'
                  src={`${S3_ASSETS_BASE}/2023/09/8finbz0.png`}
                  alt={t.sidebar?.tvm?.alt}
                />
              </a>
            </div>

            <div className='wwads-cn wwads-horizontal' data-id='59' />
          </>
        )}
      </aside>
    </>
  )
}
