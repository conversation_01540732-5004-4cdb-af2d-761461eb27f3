import React from 'react'

import type { HyperApiPost, PostType } from '@/types'

import { cn } from '@/lib/cn'

import { useTranslation } from '@/utils/i18n'

import { Skeleton } from '@/components/ui/skeleton'

import Post from './post-item'

interface RecentPostsProps {
  posts: HyperApiPost[]
  type?: PostType
  name?: string
}

export function RecentPostsSkelton() {
  return (
    <div className='space-y-3'>
      <Skeleton className='h-[38.4px] w-[16ex]' />
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
        {[...new Array(6)].map((_, idx) => {
          return <Skeleton className='h-[180px]' key={idx} />
        })}
      </div>
    </div>
  )
}

export default function RecentPosts({
  posts,
  name = '文章',
  className,
}: RecentPostsProps & React.ComponentProps<'div'>) {
  const { translate } = useTranslation()

  if (posts && posts.length > 0) {
    return (
      <div className={cn('space-y-3', className)}>
        <h3>{translate('common.relatedContent', { type: name })}</h3>

        <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
          {posts.map(item => {
            return <Post key={item.id} data={item} />
          })}
        </div>
      </div>
    )
  } else {
    return <div></div>
  }
}
