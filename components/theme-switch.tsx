'use client'

// https://github.com/pacocoursey/next-themes#avoid-hydration-mismatch
import { useEffect, useState } from 'react'
import { useTheme } from 'next-themes'
import { IconDeviceDesktop, IconMoon, IconSun } from '@tabler/icons-react'

import { cn } from '@/lib/cn'

import { useTranslation } from '@/utils/i18n'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown'

export const colorSchemes: {
  name: string
  key: string
  theme?: 'light' | 'dark'
}[] = [
  { name: 'system', key: 'theme.system', theme: undefined },
  { name: 'dark', key: 'theme.dark', theme: 'dark' },
  { name: 'light', key: 'theme.light', theme: 'light' },
]

export interface ThemeSwitchProps {
  isCollapsed?: boolean
}

export function ThemeSwitch({ isCollapsed = false }: ThemeSwitchProps) {
  const { theme, setTheme } = useTheme()
  const { translate } = useTranslation()
  const [mounted, setMounted] = useState(false)

  // After mounting, we have access to the theme
  useEffect(() => setMounted(true), [])

  if (!mounted) {
    // Return a placeholder with the same dimensions to avoid layout shift
    return <div className='h-5 w-20' />
  }

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <IconSun className='size-4' />
      case 'dark':
        return <IconMoon className='size-4' />
      default:
        return <IconDeviceDesktop className='size-4' />
    }
  }

  const getCurrentThemeName = () => {
    return translate(`theme.${theme || 'system'}`, {}, theme || 'system')
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className='flex cursor-pointer items-center gap-1.5'>
          {getThemeIcon()}
          {!isCollapsed && <span className='text-sm font-medium'>{getCurrentThemeName()}</span>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {colorSchemes.map(scheme => (
          <DropdownMenuItem
            key={scheme.name}
            className={cn(scheme.name === theme && 'text-ac')}
            onClick={() => setTheme(scheme.name)}
          >
            {translate(scheme.key, {}, scheme.name)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
