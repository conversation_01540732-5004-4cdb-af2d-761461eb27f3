'use client'

import { IconFile, IconFolderFilled } from '@tabler/icons-react'

import { formatBytes } from '@/utils/formatBytes'

import { ScrollArea } from '@/components/ui/scroll-area'

interface TorrentFileItem {
  startpiece: number
  offset: number
  size: number
  endpiece: number
}

interface TorrentFileListMap {
  [fileName: string]: TorrentFileItem
}

interface OutputList {
  name: string
  type: string
  size?: number
  files?: Record<string, OutputList> | OutputList[]
}

export default function Filelist({ data }: { data: TorrentFileListMap }) {
  type JsonInput = Record<string, { size: number }>

  function convertJson(data: JsonInput) {
    const result: OutputList[] = []
    const folderMap: Record<string, OutputList> = {}

    for (const key in data) {
      const parts = key.split('/')
      const size = data[key]?.size
      let currentFolder = folderMap

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i]
        if (part && !currentFolder[part]) {
          if (i !== parts.length - 1) {
            currentFolder[part] = { name: part, type: 'folder', files: {} }
          } else {
            currentFolder[part] = { name: part, type: 'file', size: size }
          }
        }

        if (
          part &&
          currentFolder[part]?.files &&
          typeof currentFolder[part].files === 'object' &&
          !(currentFolder[part].files instanceof Array)
        ) {
          currentFolder = currentFolder[part].files as Record<string, OutputList>
        }
      }
    }

    function convertFolderToArr(folder: OutputList): OutputList {
      const files: OutputList[] = []
      if (folder.files && typeof folder.files === 'object' && !(folder.files instanceof Array)) {
        for (const key in folder.files) {
          if (folder.files[key]?.type === 'folder') {
            convertFolderToArr(folder.files[key])
          }
          files.push(folder.files[key]!)
        }
      }
      folder.files = files
      return folder
    }

    for (const key in folderMap) {
      result.push(convertFolderToArr(folderMap[key]!))
    }

    return result
  }

  function FileTree({ data }: { data: OutputList[] }) {
    function renderFileTree(file: OutputList) {
      if (file.type === 'folder') {
        return (
          <ul key={file.name}>
            <li>
              <div className='flex items-center gap-1'>
                <IconFolderFilled />
                <div>
                  {file.name}
                  <span className='text-fg/40'>/</span>
                </div>
              </div>
              <ul className='list-none'>{file.files && Array.isArray(file.files) && file.files.map(renderFileTree)}</ul>
            </li>
          </ul>
        )
      } else {
        return (
          <li key={file.name} className='pl-4'>
            <div className='flex justify-between gap-4'>
              <div className='flex items-center gap-1'>
                <IconFile />
                <div>{file.name}</div>
              </div>
              <div className='text-fg/60'>{formatBytes(file.size || 0)}</div>
            </div>
          </li>
        )
      }
    }

    return (
      <ScrollArea className='h-[300px]'>
        <div className='bg-fg/5 rounded-md p-3'>{data.map(renderFileTree)}</div>
      </ScrollArea>
    )
  }

  const json = convertJson(data)

  return <FileTree data={json} />
}
