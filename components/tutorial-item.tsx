'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import uniqolor from 'uniqolor'
import { IconNotebook } from '@tabler/icons-react'

import type { HyperApiPost } from '@/types'

import { encodeTitle } from '@/utils/encodeTitle'

import { Card, CardContent } from '@/components/ui/card'

import Category from './category'
import PageBody from './page-body'
import Tag from './tag'
import Timestamp from './timestamp'

export default function TutorialItem({ data }: { data: HyperApiPost }) {
  const params = useParams<{ lang: string }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  const cateStr =
    data.hyperai_categories && data.hyperai_categories.length > 0
      ? data.hyperai_categories
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(cate => cate.name)
          .join(',')
      : '未知分类'

  const textColor = uniqolor(cateStr, {
    lightness: 60,
  }).color

  return (
    <Link href={`${resolvedBase}/tutorials/${data.id}`} className='block'>
      <Card className='w-full transition-shadow hover:shadow-xl'>
        <CardContent className='space-y-1 p-2'>
          <h3 className='text-base leading-none'>
            <div className='line-clamp-1 flex items-center gap-1'>
              <IconNotebook size='1.25em' style={{ color: textColor }} />
              <div className='line-clamp-1'>{encodeTitle(data.title.rendered)}</div>
            </div>
          </h3>

          <div className='mt-1'>
            <PageBody content={data.excerpt.rendered} clamp={1} />
          </div>

          <div className='text-fg/60 flex flex-wrap items-center justify-between gap-1 text-xs'>
            <div className='flex flex-wrap items-center gap-1'>
              <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />

              {data.hyperai_categories &&
                data.hyperai_categories.map((cate, idx) => {
                  return <Category data={cate} key={idx} />
                })}

              {data.hyperai_tags &&
                data.hyperai_tags.map((tag, idx) => {
                  return <Tag data={tag} key={idx} />
                })}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
