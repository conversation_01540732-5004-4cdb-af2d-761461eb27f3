import parse, { Text, type HTMLReactParserOptions } from 'html-react-parser'

import { cn } from '@/lib/cn'

export default function PageBody({
  content,
  clamp,
  className,
  ...props
}: React.ComponentProps<'div'> & {
  content: React.ReactNode
  /**
   * 是否之显示一行内容
   */
  clamp?: number
}) {
  const options: HTMLReactParserOptions = {
    replace(domNode) {
      if (domNode instanceof Text && domNode.type === 'text') {
        const str = domNode.data
        const regex = /(\$latex\s(\{.*?\})\$)/g
        const matched = str.match(regex)

        if (matched) {
          // replace matched equations with external service to plain string
          const replacedStr = str.replace(regex, (match, p1, p2) => {
            const encoded = encodeURIComponent(p2)
            return `<img src="https://equation.vrp.moe/?tex=${encoded}" class="latex" />`
          })

          return <>{parse(replacedStr)}</>
        }
      }
    },
  }

  const parsedContent = typeof content === 'string' ? parse(content, options) : content

  return (
    <div className={cn(clamp && `line-clamp-${clamp}`, className)} {...props}>
      {/* WordPress already santitize the HTML for us, so it's safe */}
      {/* <Text
        dangerouslySetInnerHTML={{ __html: content }}
        className={styles.content}
        {...rest}
      /> */}
      {parsedContent}
    </div>
  )
}
