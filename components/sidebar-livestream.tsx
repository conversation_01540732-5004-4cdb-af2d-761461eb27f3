'use client'

import Link from 'next/link'
import clsx from 'clsx'

import { useTranslation } from '@/utils/i18n'
import useLiveState from '@/utils/useLiveState'

export default function LiveBanner() {
  const LIVE_ROOM = 26483094
  const { data, isLoading } = useLiveState(LIVE_ROOM)
  const { t } = useTranslation()

  if (isLoading) {
    return (
      <div className={clsx('relative my-4 aspect-16/9 w-full rounded-lg bg-gray-800 font-bold')}>
        <div
          className={clsx(
            'text-bg absolute bottom-0 left-0 flex h-[100px] w-full items-end rounded-lg bg-gradient-to-b from-transparent to-black'
          )}
        >
          <div className={clsx('flex items-center gap-1 px-3 py-2')}>{t.sidebar?.live?.loading}</div>
        </div>
      </div>
    )
  }

  if (data?.data && data?.data?.room_info?.live_status) {
    return (
      <div className={clsx('relative my-4 aspect-16/9 w-full rounded-lg bg-gray-800 font-bold')}>
        <a
          href={`https://live.bilibili.com/${LIVE_ROOM}`}
          target='_blank'
          rel='noopenner'
          className='flex transition-opacity hover:opacity-90'
        >
          {/* eslint-disable @next/next/no-img-element */}
          <img
            width='100%'
            className='rounded-lg'
            src={data.data.room_info?.cover}
            onMouseOver={e => (e.currentTarget.src = data.data?.room_info?.keyframe || '')}
            onMouseOut={e => (e.currentTarget.src = data.data?.room_info?.cover || '')}
            referrerPolicy='no-referrer'
            alt={data.data.room_info.title}
          />
          <div
            className={clsx(
              'absolute bottom-0 left-0 flex h-[100px] w-full items-end rounded-lg bg-gradient-to-b from-transparent to-black text-white'
            )}
          >
            <div className={clsx('flex items-center gap-1 px-3 py-2')}>
              <span className={'relative block h-3 w-3 rounded-full bg-red-500'}>
                <span className='block h-full w-full animate-ping rounded-full bg-red-500'></span>
              </span>
              {t.sidebar?.live?.streaming} {data.data.room_info.title}
            </div>
          </div>
        </a>
      </div>
    )
  }

  if (data?.data?.room_info?.live_status === 0) {
    return (
      <div className={clsx('relative my-4 aspect-16/9 w-full rounded-lg bg-gray-800 font-bold')}>
        <Link href={'https://space.bilibili.com/386835083'} target='_blank'>
          {/* eslint-disable @next/next/no-img-element */}
          <img
            width='100%'
            className='rounded-lg opacity-50'
            src={data.data.room_info.cover}
            referrerPolicy='no-referrer'
            alt={data.data.room_info.title}
          />
          <div
            className={clsx(
              'absolute bottom-0 left-0 flex h-[100px] w-full items-end rounded-lg bg-gradient-to-b from-transparent to-black text-white'
            )}
          >
            <div className={clsx('flex items-center gap-1 px-3 py-2')}>{t.sidebar?.live?.offline}</div>
          </div>
        </Link>
      </div>
    )
  }

  return null
}
