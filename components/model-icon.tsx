import Image from 'next/image'
import clsx from 'clsx'

import { modelMap } from '@/lib/ai/model-meta'

import { Tooltip } from '@/components/ui/tooltip'

export function ModelIcon({ model, size = 18, tooltip = false }: { model: string; size?: number; tooltip?: boolean }) {
  for (const key in modelMap) {
    if (model.toLocaleLowerCase().startsWith(key) && modelMap[key]) {
      if (tooltip) {
        return (
          <Tooltip label={model}>
            <Image
              src={`/ai-providers/${modelMap[key].logo}`}
              alt={'logo'}
              width={size}
              height={size}
              loading='lazy'
              className={clsx('rounded-sm object-contain object-center', modelMap[key]?.autoDark && 'filter-auto-dark')}
            />
          </Tooltip>
        )
      } else {
        return (
          <Image
            src={`/ai-providers/${modelMap[key].logo}`}
            alt={'logo'}
            width={size}
            height={size}
            loading='lazy'
            className={clsx('rounded-sm object-contain object-center', modelMap[key]?.autoDark && 'filter-auto-dark')}
          />
        )
      }
    }
  }
  // 当不需要 tooltip 时，不需要返回文本，只作为纯图标展示，否则会出现模型图标变成文本 + 原本的模型文本，显示两遍的情况
  if (tooltip) {
    return model
  } else {
    return null
  }
}
