'use client'

import { IconBorderAll, IconCalendarDue, IconConfetti, IconFlag } from '@tabler/icons-react'

import type { HyperApiPostEvent } from '@/types'

import { cn } from '@/lib/utils'

import { formatEventDate } from '@/utils/formatEventDate'
import { useTranslation } from '@/utils/i18n'

export function EventTimeline({ event }: { event: HyperApiPostEvent }) {
  const { t } = useTranslation()
  let currentStep = 1

  const deadlineDate = event.acf.end_date
  const publishDate = event.acf.publish_date
  const startDate = event.acf.start_date
  const endDate = event.acf.event_end_date

  if (deadlineDate && new Date(deadlineDate).getTime() - Date.now() <= 0) {
    currentStep = currentStep + 1
  }

  if (publishDate && new Date(publishDate).getTime() - Date.now() <= 0) {
    currentStep = currentStep + 1
  }

  if (startDate && new Date(startDate).getTime() - Date.now() <= 0) {
    currentStep = currentStep + 1
  }

  if (endDate && new Date(endDate).getTime() - Date.now() <= 0) {
    currentStep = currentStep + 1
  }

  return (
    <div className='space-y-0'>
      {deadlineDate && (
        <TimelineItem
          active={currentStep > 1}
          icon={<IconCalendarDue size={20} />}
          title={t.events.timeline.deadline}
          isFirst={true}
        >
          <p className='text-fg/60'>{t.events.timeline.deadlineDesc}</p>
          <p className='text-sm'>{formatEventDate(deadlineDate)}</p>
        </TimelineItem>
      )}

      {publishDate && (
        <TimelineItem active={currentStep > 2} icon={<IconFlag size={20} />} title={t.events.timeline.results}>
          <p className='text-fg/60'>{t.events.timeline.resultsDesc}</p>
          <p className='text-sm'>{formatEventDate(publishDate)}</p>
        </TimelineItem>
      )}

      {startDate && (
        <TimelineItem active={currentStep > 3} icon={<IconBorderAll size={20} />} title={t.events.timeline.eventStart}>
          <p className='text-fg/60'>{t.events.timeline.eventStartDesc}</p>
          <p className='text-sm'>{formatEventDate(startDate)}</p>
        </TimelineItem>
      )}

      {endDate && (
        <TimelineItem
          active={currentStep > 4}
          icon={<IconConfetti size={20} />}
          title={t.events.timeline.eventEnd}
          isLast={true}
        >
          <p className='text-fg/60'>{t.events.timeline.eventEndDesc}</p>
          <p className='text-sm'>{formatEventDate(endDate)}</p>
        </TimelineItem>
      )}
    </div>
  )
}

type TimelineItemProps = {
  active: boolean
  icon: React.ReactNode
  title: string
  children: React.ReactNode
  isFirst?: boolean
  isLast?: boolean
}

function TimelineItem({ active, icon, title, children, isLast = false }: TimelineItemProps) {
  return (
    <div className='relative pt-1 pb-8 pl-11'>
      {/* Line */}
      {!isLast && <div className={cn('absolute top-9 bottom-0 left-4 w-1', active ? 'bg-ac' : 'bg-fg/20')} />}

      {/* Bullet */}
      <div
        className={cn(
          'absolute top-0 left-0 flex h-9 w-9 items-center justify-center rounded-full border-2',
          active ? 'bg-ac text-bg border-ac' : 'bg-bg border-fg/20 text-fg/40'
        )}
      >
        {icon}
      </div>

      {/* Content */}
      <div className='space-y-1'>
        <h3 className='text-lg font-medium'>{title}</h3>
        <div className='space-y-1'>{children}</div>
      </div>
    </div>
  )
}
