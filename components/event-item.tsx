import Link from 'next/link'
import { useParams } from 'next/navigation'

import type { HyperApiPostEvent } from '@/types'

import { encodeTitle } from '@/utils/encodeTitle'

import Category from '@/components/category'
import EventTimestamp from '@/components/event-timestamp'
import { getFeaturedMediaUrl } from '@/components/featured-media'
import Location from '@/components/location'
import Tag from '@/components/tag'
import { Card, CardContent } from '@/components/ui/card'

export default function EventItem({ data, orderBy }: { data: HyperApiPostEvent; orderBy?: string }) {
  const params = useParams<{ lang: string }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  // const cateStr =
  //   data.hyperai_categories && data.hyperai_categories.length > 0
  //     ? data.hyperai_categories
  //         .sort((a, b) => a.name.localeCompare(b.name))
  //         .map(cate => cate.name)
  //         .join(',')
  //     : '未知分类'

  // const textColor = uniqolor(cateStr, {
  //   lightness: 60,
  // }).color

  const featuredImage =
    data._embedded && data._embedded['wp:featuredmedia'] && getFeaturedMediaUrl(data._embedded['wp:featuredmedia'][0])

  return (
    <Link href={`${resolvedBase}/events/${data.id}`} className='block'>
      <Card className='w-full overflow-hidden transition-shadow hover:shadow-xl'>
        {featuredImage ? (
          <picture>
            <img src={featuredImage} className='aspect-video w-full object-cover' alt='thumbnail' />
          </picture>
        ) : (
          <div className='bg-fg/10 aspect-video w-full' />
        )}

        <CardContent className='mt-1 space-y-2 p-2'>
          <h3 className='line-clamp-2 text-base leading-tight'>{encodeTitle(data.title.rendered)}</h3>

          <div className='text-fg/60 flex flex-wrap items-center gap-1 text-xs'>
            <EventTimestamp event={data} orderBy={orderBy} />
            <Location venue={data.acf.venue} />

            {data.hyperai_categories &&
              data.hyperai_categories.map((cate, idx) => {
                return <Category data={cate} key={idx} />
              })}

            {data.hyperai_tags &&
              data.hyperai_tags.map((tag, idx) => {
                return <Tag data={tag} key={idx} />
              })}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
