'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import useS<PERSON>, { type Fetcher } from 'swr'
import type { LogtoContext } from '@logto/next/edge'
import { IconLogout, IconUser } from '@tabler/icons-react'

import { useTranslation } from '@/utils/i18n'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuIcon,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown'

const fetcher: Fetcher<LogtoContext> = async (...args: Parameters<typeof fetch>) => {
  const resp = await fetch(...args)
  if (!resp.ok) {
    throw new Error('An error occurred while fetching the user data')
  }
  return resp.json()
}

export default function LoginLinks() {
  const { push } = useRouter()
  const { data, isLoading } = useSWR('/api/auth/user', fetcher)
  const userEmail = data?.claims?.email
  const { t } = useTranslation()

  if (isLoading) {
    return (
      <div className='flex items-center'>
        <div className='bg-fg/10 size-9 animate-pulse rounded-full' />
      </div>
    )
  }

  if (data?.isAuthenticated) {
    return (
      <div className='flex items-center'>
        <DropdownMenu>
          <DropdownMenuTrigger className='focus-ring rounded-full'>
            <Avatar className='size-9'>
              <AvatarImage src={data.claims?.picture || ''} alt={userEmail || 'User avatar'} />
              <AvatarFallback>{userEmail?.charAt(0).toUpperCase() || 'U'}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>

          <DropdownMenuContent className='w-56' align='end' forceMount>
            <DropdownMenuLabel className='font-normal'>{userEmail}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href='/profile' className='flex items-center'>
                  <DropdownMenuIcon>
                    <IconUser className='size-4' />
                  </DropdownMenuIcon>
                  <span>{t.common.accountInfo}</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => push('/api/auth/sign-out')}>
                <DropdownMenuIcon>
                  <IconLogout className='size-4' />
                </DropdownMenuIcon>
                <span>{t.common.logout}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  return (
    <div className='flex items-center'>
      <Button tint={'accent'} variant={'solid'} onClick={() => push('/api/auth/sign-in')}>
        {t.common.login}
      </Button>
    </div>
  )
}
