'use client'

// import { defaultLocale } from '@/middleware'
import type { PostType } from '@/types'

import useRelatedPosts from '@/utils/useRelatedPosts'

import RecentPosts, { RecentPostsSkelton } from '@/components/recent-posts'

// import { useParams } from 'next/navigation'

export default function RelatedPosts({ postId, type, name }: { postId: number; type: PostType; name: string }) {
  const { data, isLoading } = useRelatedPosts(postId)
  // const params = useParams<{ lang: string }>()
  // const resolvedApiPrefix = params?.lang && params.lang !== defaultLocale ? `/${params.lang}` : ''

  if (isLoading) {
    return <RecentPostsSkelton />
  }

  return <RecentPosts posts={data?.json || []} type={type} name={name} />
}
