'use client'

import { IconClock } from '@tabler/icons-react'

import { formatDate } from '@/utils/formatDate'
import { useTranslation } from '@/utils/i18n'
import { timeFromNow } from '@/utils/timeFromNow'
import useParamLang from '@/utils/useParamLang'

import { Tooltip } from '@/components/ui/tooltip'

export default function Timestamp({ date, modified, ...props }: { date: string | number; modified?: string }) {
  const { locale } = useParamLang()
  const { t } = useTranslation()

  // Automatically detect if date is a unix timestamp (number) or date string
  const dateToUse =
    typeof date === 'number'
      ? date.toString().length <= 10
        ? new Date(date * 1000) // Unix timestamp in seconds (10 digits or less)
        : new Date(date) // Unix timestamp in milliseconds (13 digits)
      : new Date(date) // Date string

  return (
    <Tooltip
      label={
        <>
          {t.common.publishedAt}{' '}
          {formatDate(dateToUse, {
            locale,
            format: {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            },
          })}
          {/* Do not show modified date for privacy */}
          {modified
            ? // <>
              //   <br />
              //   修改于{' '}
              //   {formatDate(new Date(modified), {
              //     format: {
              //       year: 'numeric',
              //       month: '2-digit',
              //       day: '2-digit',
              //       hour: '2-digit',
              //       minute: '2-digit',
              //       second: '2-digit',
              //     },
              //   })}
              // </>
              null
            : null}
        </>
      }
      {...props}
    >
      <div className='flex items-center gap-1'>
        <IconClock size={'1.25em'} />
        {timeFromNow(+dateToUse, {
          locale,
        })}
      </div>
    </Tooltip>
  )
}
