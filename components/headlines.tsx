'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { Flipped, Flipper } from 'react-flip-toolkit'
import useSWR from 'swr'
import { IconNews, IconNewsOff, IconRefresh } from '@tabler/icons-react'

import type { Locale } from '@/types'
import type { components } from '@/types/scraper'

import { fetchNews } from '@/lib/scraper-api'

import { useTranslation } from '@/utils/i18n'

import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

import HeadlineItem from './headline-item'

type NewsTopicBase = components['schemas']['NewsTopicBase']

// SWR fetcher that uses our fetchNews API
const headlinesFetcher = async (params: [string, string, number, number, number]): Promise<NewsTopicBase[]> => {
  const [, locale, pageNum, pageSize, stickyNum] = params
  const response = await fetchNews({
    locale,
    page_num: pageNum,
    page_size: pageSize,
    sticky_num: stickyNum,
  })
  return response.data
}

export default function Headlines() {
  const params = useParams<{ lang: Locale }>()
  const { t } = useTranslation()
  const locale = params?.lang || 'en'

  // Using SWR to fetch the headlines with auto-revalidation
  const {
    data: headlines,
    error,
    isLoading,
    mutate,
  } = useSWR<NewsTopicBase[]>(['headlines', locale, 1, 5, 1], headlinesFetcher, {
    refreshInterval: 60 * 1000, // Refresh every minute
    revalidateOnFocus: true,
  })

  const filteredHeadlines = headlines?.filter(headline => !headline.is_hidden) || []

  return (
    <section className='border-border bg-card mt-6 space-y-2 rounded-lg border p-4'>
      <h2 className='flex items-center justify-between gap-2 text-xl'>
        <div className='flex items-center gap-2'>
          <IconNews className='text-muted-foreground' />
          {t.nav.headlines.title}
        </div>

        <div className='flex items-center justify-end'>
          <Link href={`/${locale}/headlines`} className='flex items-center'>
            <Button variant='outline' size='sm'>
              {t.nav.headlines.viewAll}
            </Button>
          </Link>
        </div>
      </h2>

      {isLoading ? (
        <div className='space-y-3'>
          {[...Array(5)].map((_, i) => (
            <div key={i} className='space-y-1'>
              <Skeleton className='h-12 w-full' />
              <Skeleton className='h-5 w-20' />
            </div>
          ))}
        </div>
      ) : error ? (
        <div className='flex flex-col items-center justify-center py-4 text-center'>
          <IconNewsOff className='text-muted-foreground mb-2 h-10 w-10' />
          <p className='text-muted-foreground text-sm'>Failed to load headlines</p>
          <Button variant='outline' size='sm' onClick={() => mutate()} className='mt-2'>
            <IconRefresh className='mr-1 h-4 w-4' />
            Retry
          </Button>
        </div>
      ) : (
        <Flipper flipKey={filteredHeadlines.map(h => h.topic_id).join('-')} spring={{ stiffness: 1000, damping: 100 }}>
          <div className='space-y-3'>
            {filteredHeadlines.map((headline: NewsTopicBase) => (
              <Flipped key={headline.topic_id} flipId={headline.topic_id.toString()}>
                {flippedProps => (
                  <div {...flippedProps}>
                    <HeadlineItem headline={headline} locale={locale} variant='compact' />
                  </div>
                )}
              </Flipped>
            ))}
          </div>
        </Flipper>
      )}
    </section>
  )
}
