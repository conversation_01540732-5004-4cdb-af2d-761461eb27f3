import { IconCategory2 } from '@tabler/icons-react'

import type { HyperCategory } from '@/types'

import Tag, { type TagProps } from './tag'

export default function Category({
  data,
  ...props
}: {
  data: HyperCategory
} & TagProps) {
  // Simply copy Tag component at the moment
  return <Tag data={data} {...props} icon={<IconCategory2 size={'1em'} />} />

  // return (
  //   <Group gap={'0.25em'}>
  //     <IconCategory2 color={theme.colors.gray[6]} size={'1.25em'} />
  //     <Text c={'gray'} key={data.id}>
  //       {data.name}
  //     </Text>
  //   </Group>
  // )
}
