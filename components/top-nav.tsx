'use client'

import React from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useSidebarStore } from '@/store/sidebar-store'
import { IconMenu2 } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { cn } from '@/lib/utils'

import { useTranslation } from '@/utils/i18n'

import { Command } from '@/components/command-elasticsearch'
import LoginLinks from '@/components/login-links'
import { Logo } from '@/components/logo-icon'
import { Button } from '@/components/ui/button'

interface TopNavProps {
  className?: string
}

export default function TopNav({ className }: TopNavProps) {
  const params = useParams<{ lang: Locale }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''
  const { toggleSidebar } = useSidebarStore()
  const { t } = useTranslation()

  return (
    <div className={cn('bg-bg shadow-border-b sticky top-0 right-0 left-0 z-40 h-12 px-4', className)}>
      <div className='flex h-full items-center'>
        {/* Left section: Logo and toggle */}
        <div className='flex items-center gap-1'>
          {/* Logo - visible only on mobile */}
          <Link
            href={`${resolvedBase}/`}
            className='flex items-center gap-2 transition-opacity hover:opacity-80 md:hidden'
          >
            <Logo className='h-7 w-7' />
            <span className='text-lg font-bold'>{t.common.title}</span>
          </Link>

          <Button onClick={toggleSidebar} variant='link' size='icon' aria-label='Toggle sidebar'>
            <IconMenu2 className='size-5' />
            <span className='sr-only'>Toggle sidebar</span>
          </Button>
        </div>

        {/* Spacer for desktop that pushes command to center */}
        {/* <div className='hidden md:block flex-1'></div> */}

        {/* Command component */}
        <div className='flex flex-1 items-center justify-end md:mx-4 md:flex-none md:justify-center'>
          <Command />
        </div>

        {/* Right section: User links */}
        <div className='hidden flex-1 items-center justify-end md:flex'>
          <LoginLinks />
        </div>

        {/* Mobile-only login links (shown below Command) */}
        <div className='ml-2 flex items-center md:hidden'>
          <LoginLinks />
        </div>
      </div>
    </div>
  )
}
