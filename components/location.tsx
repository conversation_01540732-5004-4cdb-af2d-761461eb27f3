'use client'

import { IconMapPin } from '@tabler/icons-react'

import type { Venue } from '@/types'

import { Tooltip } from '@/components/ui/tooltip'

export default function Location({ venue, ...props }: { venue: Venue | null }) {
  if (venue && 'address' in venue) {
    const addr = venue?.address
    const city = venue?.city
    const country = venue?.country
    // const countryCode = venue?.country_short
    const state = venue?.state
    // const stateCode = venue?.state_short

    return (
      <Tooltip
        label={
          <div className='text-sm'>
            {addr ? addr : '待定'}
            {city ? (
              <>
                <br />
                {city}
              </>
            ) : null}
            {state ? (
              <>
                <br />
                {state}
              </>
            ) : null}
            {country ? (
              <>
                <br />
                {country}
              </>
            ) : null}
          </div>
        }
      >
        <div className='text-fg/60 flex items-center gap-1' {...props}>
          <IconMapPin size='1.25em' />
          <span>{city ? city : state ? state : addr ? addr : country ? country : '地点待定'}</span>
        </div>
      </Tooltip>
    )
  }

  return null
}
