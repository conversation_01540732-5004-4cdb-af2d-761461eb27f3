'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'

import type { EmbeddedUser, HyperApiPost } from '@/types'

import { encodeTitle } from '@/utils/encodeTitle'

import Author from './author'
import { FeaturedMedia, getFeaturedMediaUrl } from './featured-media'
import PageBody from './page-body'
import Tag from './tag'
import Timestamp from './timestamp'

export function NewsItem({ data, simple, hardLink }: { data: HyperApiPost; simple?: boolean; hardLink?: boolean }) {
  const params = useParams<{ lang: string }>()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''
  const LinkTag = hardLink ? 'a' : Link
  const featuredImage =
    data._embedded && data._embedded['wp:featuredmedia'] && getFeaturedMediaUrl(data._embedded['wp:featuredmedia'][0])

  return simple ? (
    <div>
      <Link href={`${resolvedBase}/news/${data.id}`}>
        <FeaturedMedia media={featuredImage} showEmpty />

        <div className='py-1'>
          <h4 className='line-clamp-2 text-base'>{encodeTitle(data.title.rendered)}</h4>
        </div>
      </Link>
    </div>
  ) : (
    <div className='flex w-full flex-col gap-4 md:flex-row'>
      <div className='flex-1'>
        <div className='mb-2 flex justify-between'>
          <Link href={`${resolvedBase}/news/${data.id}`} className='link'>
            <h3 className='text-fg hover:text-ac line-clamp-2 text-2xl'>{encodeTitle(data.title.rendered)}</h3>
          </Link>
          {/* <Badge color="pink" variant="light">
            On Sale
          </Badge> */}
        </div>

        <PageBody content={data.excerpt.rendered} clamp={2} className='text-fg/60' />

        <div className='mt-2 flex flex-wrap items-center gap-1'>
          <div className='text-fg/60'>
            <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />
          </div>

          {/* NOTE: categories are not used for most recent posts */}
          {/* {data.hyperai_categories && data.hyperai_categories.map((cate, idx) => {

            return (
              <Category data={cate} key={idx} />
            )
          })} */}

          {data.hyperai_tags &&
            data.hyperai_tags.map((tag, idx) => {
              return <Tag data={tag} key={idx} enableLink='tags' />
            })}

          {data?._embedded?.author &&
            data._embedded.author.length > 0 &&
            data._embedded.author.map((author: EmbeddedUser, idx) => {
              return <Author data={author} key={idx} />
            })}
        </div>

        {/* <Button variant="light" fullWidth mt="md" radius="md">
          Book classic tour now
        </Button> */}
      </div>

      <LinkTag href={`${resolvedBase}/news/${data.id}`} className={'order-first sm:w-full md:order-last md:w-1/4'}>
        <FeaturedMedia media={featuredImage} showEmpty />
      </LinkTag>
    </div>
  )
}
