import type { HyperApiPost, HyperApiPostEvent } from '@/types'

import DatasetItem from './dataset-item'
import EventItem from './event-item'
import { NewsItem } from './news-item'
import TutorialItem from './tutorial-item'
import WikiItem from './wiki-item'

/**
 * 通用的 WP Post，根据类型判断后返回特定渲染
 * @param param0
 * @returns
 */
export default function Post({ data }: { data: HyperApiPost }) {
  if (data.type === 'post') {
    return <NewsItem data={data} simple />
  }

  if (data.type === 'tutorials') {
    return <TutorialItem data={data} />
  }

  if (data.type === 'datasets') {
    return <DatasetItem data={data} />
  }

  if (data.type === 'wiki') {
    return <WikiItem data={data} />
  }

  if (data.type === 'events') {
    return <EventItem data={data as HyperApiPostEvent} />
  }
}
