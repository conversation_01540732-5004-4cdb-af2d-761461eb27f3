'use client'

import { IconClock } from '@tabler/icons-react'

import type { HyperApiPostEvent } from '@/types'

import { formatEventDate } from '@/utils/formatEventDate'
import { useTranslation } from '@/utils/i18n'
import { timeCountdown } from '@/utils/timeCountdown'

import { Tooltip } from '@/components/ui/tooltip'

export default function EventTimestamp({ event, orderBy, ...props }: { event: HyperApiPostEvent; orderBy?: string }) {
  const deadlineDate = event.acf?.end_date
  const publishDate = event.acf?.publish_date
  const startDate = event.acf?.start_date
  const endDate = event.acf?.event_end_date
  const { t } = useTranslation()

  let resolvedTime = deadlineDate
  let placeholderText = t.events.timeline.deadline

  if (orderBy === 'publish_date' && publishDate) {
    resolvedTime = publishDate
    placeholderText = t.events.timeline.results
  } else if (orderBy === 'start_date' && startDate) {
    resolvedTime = startDate

    if (startDate && endDate) {
      if (+new Date(startDate) < Date.now() || +new Date(endDate) < Date.now()) {
        placeholderText = t.time.ended
      }

      if (+new Date(startDate) < Date.now() && +new Date(endDate) > Date.now()) {
        placeholderText = t.events.ongoing
      }
    }
  } else if (orderBy === 'end_date' && deadlineDate) {
    resolvedTime = deadlineDate
    placeholderText = t.events.timeline.deadline
  } else if (orderBy === 'event_end_date' && endDate) {
    resolvedTime = endDate
    placeholderText = t.events.timeline.eventEnd
  }

  return (
    <Tooltip
      label={
        <div className='text-sm'>
          {deadlineDate ? (
            <div>
              {t.events.controls.deadline} {formatEventDate(deadlineDate)}
            </div>
          ) : null}
          {publishDate ? (
            <div>
              {t.events.controls.results} {formatEventDate(publishDate)}
            </div>
          ) : null}
          <div>
            {t.events.controls.eventDate} {startDate ? formatEventDate(startDate) : t.events.tbd}
          </div>
          {endDate ? (
            <div>
              {t.events.timeline.eventEnd} {formatEventDate(endDate)}
            </div>
          ) : null}
        </div>
      }
    >
      <div className='text-fg/60 flex items-center gap-1' {...props}>
        <IconClock size='1.25em' />
        <span>{resolvedTime ? timeCountdown(+new Date(resolvedTime), placeholderText) : t.events.tbd}</span>
      </div>
    </Tooltip>
  )
}
