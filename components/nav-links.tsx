'use client'

import React from 'react'
import {
  IconArticle,
  IconCertificate,
  IconChartBar,
  IconCpu,
  IconDatabase,
  IconDeviceDesktop,
  IconHome,
  IconInfoCircle,
  IconMenu4,
  IconNews,
  IconNotebook,
  IconNotes,
  IconSearch,
} from '@tabler/icons-react'

import type { Locale } from '@/types'

import { getClientDictionary } from '@/lib/dictionaries-client'

export interface NavLink {
  title: string
  desc: string
  href: string
  type: 'link' | 'menu'
  icon?: React.ReactNode
  iconCmdk?: React.ReactNode
  active?: boolean
  mobileOnly?: boolean
  links?: {
    link: string
    label: string
    desc: string
    newTab?: boolean
  }[]
}

export const navLinks = (resolvedBase: string, pathname: string | null, lang: Locale): NavLink[] => {
  // Use the translation function directly in the navLinks function
  const t = getClientDictionary(lang)

  const links: NavLink[] = [
    {
      title: t.nav.home.title,
      desc: t.nav.home.desc,
      href: `${resolvedBase}`,
      type: `link`,
      iconCmdk: <IconHome className='size-5 opacity-50' />,
      active: pathname === `${resolvedBase}`,
    },
    {
      title: t.nav.news.title,
      desc: t.nav.news.desc,
      href: `${resolvedBase}/news`,
      type: `link`,
      icon: <IconNews className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/news`),
    },
    {
      title: t.sota.papers.title,
      desc: t.sota.papers.description,
      href: `${resolvedBase}/papers`,
      type: `link`,
      icon: <IconArticle className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/papers`),
    },
    {
      title: t.nav.tutorials.title,
      desc: t.nav.tutorials.desc,
      href: `${resolvedBase}/tutorials`,
      type: `link`,
      icon: <IconNotebook className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/tutorials`),
    },
    {
      title: t.nav.datasets.title,
      desc: t.nav.datasets.desc,
      href: `${resolvedBase}/datasets`,
      type: `link`,
      icon: <IconDatabase className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/datasets`),
    },
    {
      title: t.nav.wiki.title,
      desc: t.nav.wiki.desc,
      href: `${resolvedBase}/wiki`,
      type: `link`,
      icon: <IconNotes className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/wiki`),
    },
    {
      title: t.nav.sota.title,
      desc: t.nav.sota.desc,
      href: `${resolvedBase}/sota`,
      type: `link`,
      icon: <IconChartBar className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/sota`),
    },
    {
      title: t.nav.llmModels.title,
      desc: t.nav.llmModels.desc,
      href: `${resolvedBase}/llm-models`,
      type: `link`,
      icon: <IconCpu className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/llm-models`),
    },
    {
      title: t.nav.gpuLeaderboard.title,
      desc: t.nav.gpuLeaderboard.desc,
      href: `${resolvedBase}/gpu-leaderboard`,
      type: `link`,
      icon: <IconDeviceDesktop className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/gpu-leaderboard`),
    },
    {
      title: t.nav.events.title,
      desc: t.nav.events.desc,
      href: `${resolvedBase}/events`,
      type: `link`,
      icon: <IconCertificate className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/events`),
    },
  ]

  // Only add the projects menu for Chinese locale
  if (lang === 'cn') {
    links.push({
      title: t.nav.projects.title,
      desc: t.nav.projects.desc,
      type: `menu`,
      href: `#`,
      icon: <IconMenu4 className='size-5 opacity-50' />,
      links: [
        {
          link: 'https://tvm.hyper.ai',
          label: t.nav.projects.tvm.label,
          desc: t.nav.projects.tvm.desc,
          newTab: true,
        },
        {
          link: 'https://triton.hyper.ai',
          label: t.nav.projects.triton.label,
          desc: t.nav.projects.triton.desc,
          newTab: true,
        },
        {
          link: 'https://vllm.hyper.ai',
          label: t.nav.projects.vllm.label,
          desc: t.nav.projects.vllm.desc,
          newTab: true,
        },
      ],
    })
  }

  // Add the remaining navigation items
  links.push(
    {
      title: t.nav.search.title,
      desc: t.nav.search.desc,
      href: `${resolvedBase}/search`,
      type: `link`,
      icon: <IconSearch className='size-5 opacity-50' />,
      active: pathname?.startsWith(`${resolvedBase}/search`),
      mobileOnly: true,
    },
    {
      title: t.nav.about.title,
      desc: t.nav.about.desc,
      href: `${resolvedBase}/about`,
      type: `link`,
      active: pathname?.startsWith(`${resolvedBase}/about`),
      iconCmdk: <IconInfoCircle className='size-5 opacity-50' />,
    }
  )

  return links
}
