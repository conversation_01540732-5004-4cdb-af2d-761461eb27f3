// https://www.algolia.com/doc/api-reference/widgets/autocomplete/react/
import connectAutocomplete from 'instantsearch.js/es/connectors/autocomplete/connectAutocomplete'
import type {
  AutocompleteConnectorParams,
  AutocompleteWidgetDescription,
} from 'instantsearch.js/es/connectors/autocomplete/connectAutocomplete'
import { useConnector } from 'react-instantsearch'

export type UseAutocompleteProps = AutocompleteConnectorParams

export function useAutocomplete(props?: UseAutocompleteProps) {
  return useConnector<AutocompleteConnectorParams, AutocompleteWidgetDescription>(connectAutocomplete, props)
}

export function Autocomplete(props: UseAutocompleteProps) {
  const { indices } = useAutocomplete(props)

  return (
    <>
      {indices.map(index => {
        return (
          <div key={index.indexName}>
            {index.hits.map(hit => {
              return (
                <div key={hit.objectID}>
                  {/* {hit.} */}
                  {JSON.stringify(hit)}
                </div>
              )
            })}
          </div>
        )
      })}
    </>
  )
}
