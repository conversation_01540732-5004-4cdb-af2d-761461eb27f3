import React from 'react'
import Image from 'next/image'
import clsx from 'clsx'

import type { FeaturedMediaItem } from '@/types'

interface FeaturedMediaProps extends React.HTMLAttributes<HTMLDivElement> {
  media: FeaturedMediaItem | string | undefined
  size?: string
  showEmpty?: boolean
  autoHeight?: boolean
  edgeLess?: boolean
}

export function getFeaturedMediaUrl(media: FeaturedMediaItem | string | undefined, fullSize?: boolean) {
  if (!media) {
    return undefined
  }

  if (typeof media === 'string') {
    return media
  }

  const imageUrl =
    (fullSize && media.source_url) ||
    media.media_details?.sizes.large?.source_url ||
    media.media_details?.sizes.medium_large?.source_url ||
    media.media_details?.sizes.medium?.source_url ||
    media.media_details?.sizes.thumbnail?.source_url

  return imageUrl
}

export function FeaturedMedia({
  media,
  size = '',
  showEmpty = false,
  autoHeight = false,
  edgeLess,
  ...rest
}: FeaturedMediaProps) {
  const imageUrl = getFeaturedMediaUrl(media)

  if (imageUrl) {
    return (
      <div className={size} {...rest}>
        <Image
          alt={`特色图像`}
          src={imageUrl}
          // fill={true}
          width={1000}
          height={1000}
          className={clsx(
            `bg-fg/10 w-full ${autoHeight ? `h-auto` : `max-h-[160px]`} aspect-video rounded-md object-cover`,
            edgeLess && 'rounded-b-none'
          )}
          quality={80}
          unoptimized
        />
      </div>
    )
  }

  if (!imageUrl && showEmpty) {
    return (
      <div className={size} {...rest}>
        <div className={clsx('bg-fg/10 min-h-[160px] w-full rounded-md', edgeLess && 'rounded-b-none')}></div>
      </div>
    )
  } else {
    return <></>
  }
}
