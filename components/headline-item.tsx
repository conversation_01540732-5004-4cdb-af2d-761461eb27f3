'use client'

import Link from 'next/link'
import { IconFlameFilled } from '@tabler/icons-react'

import type { components } from '@/types/scraper'

import Timestamp from '@/components/timestamp'

type NewsTopicBase = components['schemas']['NewsTopicBase']

interface HeadlineItemProps {
  headline: NewsTopicBase
  locale: string
  variant?: 'compact' | 'default'
}

export default function HeadlineItem({ headline, locale, variant = 'default' }: HeadlineItemProps) {
  return (
    <div className={`group space-y-1 ${variant === 'compact' ? '' : 'border-border border-b pb-4 last:border-none'}`}>
      <Link href={`/${locale}/headlines/${headline.topic_id}`} className='hover:text-ac block'>
        <h3 className={`line-clamp-4 font-bold ${variant === 'compact' ? 'text-lg' : 'text-2xl'}`}>
          {headline.sticky && <IconFlameFilled className='mr-1 inline-block size-6 text-orange-500' />}
          {headline.title}
        </h3>
      </Link>
      <div className={`text-fg/60 flex items-center gap-2 ${variant === 'compact' ? 'text-sm' : 'text-base'}`}>
        <Timestamp date={headline.created_timestamp || headline.create_at} />
      </div>
    </div>
  )
}
