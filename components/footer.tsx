'use client'

import Image from 'next/image'
import Link from 'next/link'
import { config } from '@/data/config'
import { IconBrandBilibili, IconBrandX } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { useTranslation } from '@/utils/i18n'

import { LanguageSwitch } from '@/components/language-switch'
import { Logo } from '@/components/logo-icon'
import { Button } from '@/components/ui/button'

const utmTags = '?utm_source=hyperai&utm_medium=footer&utm_campaign=sponsor'

export default function Footer({ lang }: { lang: Locale }) {
  const { t } = useTranslation()

  const data = [
    {
      title: t.footer.about.title,
      links: [
        { label: t.footer.about.aboutUs, link: '/about' },
        { label: t.footer.about.datasetHelp, link: '/datasets/about' },
      ],
    },
    {
      title: t.footer.products.title,
      links: [
        { label: t.nav.news.title, link: '/news' },
        { label: t.nav.tutorials.title, link: '/tutorials' },
        { label: t.nav.datasets.title, link: '/datasets' },
        { label: t.nav.wiki.title, link: '/wiki' },
      ],
    },
    {
      title: t.footer.links.title,
      links: [
        { label: t.nav.projects.tvm.label, link: `https://tvm.hyper.ai/${utmTags}` },
        { label: 'Apache TVM', link: `https://tvm.apache.org/${utmTags}` },
        { label: 'OpenBayes', link: `https://openbayes.com/${utmTags}` },
      ],
    },
  ]

  const groups = data.map(group => {
    const links = group.links.map((link, index) => (
      <Link
        key={index}
        href={link.link}
        target={link.link.startsWith('http') ? '_blank' : ''}
        className='text-fg/60 hover:text-fg mt-1 block text-sm transition-colors'
      >
        {link.label}
      </Link>
    ))

    return (
      <div className='py-2' key={group.title}>
        <h3 className='mb-2 text-base font-medium'>{group.title}</h3>
        {links}
      </div>
    )
  })

  return (
    <footer className='bg-fg/5 mt-16'>
      <div className='mx-auto max-w-[1510px] px-4 py-10 md:px-8'>
        <div className='grid grid-cols-1 gap-8 md:grid-cols-4'>
          <div className='space-y-3'>
            <Logo width={40} />
            <p className='text text-fg/60 text-balance'>{t.common.slogan}</p>
            <div className='flex'>
              <LanguageSwitch />
            </div>
          </div>
          <div className='col-span-3 grid grid-cols-3 gap-8'>{groups}</div>
        </div>
      </div>
      <div className='border-fg/10 mx-auto flex max-w-[1510px] flex-col items-center justify-between gap-4 border-t px-4 py-4 md:flex-row md:px-8'>
        <div className='text-center md:text-left'>
          <p className='text-fg/60 text-sm'>© {t.common.title}</p>

          {lang === 'cn' && (
            <div className='flex flex-wrap justify-center gap-2 md:justify-start'>
              <a href='http://beian.miit.gov.cn/' className='hover:underline' target='_blank'>
                <span className='text-fg/60 text-xs'>{config.icp}</span>
              </a>
              <a
                href={`http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=${config.icpGonganNo}`}
                className='flex items-center gap-1 hover:underline'
                target='_blank'
              >
                <Image src={'/static/icons/psb.png'} width={14} height={14} alt={config.icpGongan} />
                <span className='text-fg/60 text-xs'>{config.icpGongan}</span>
              </a>
            </div>
          )}
        </div>

        <div className='flex gap-2'>
          <Button size='icon' variant='outline' asChild>
            <a href='https://twitter.com/HyperAI_News' target='_blank'>
              <IconBrandX className='size-5' />
              <span className='sr-only'>Twitter</span>
            </a>
          </Button>

          <Button size='icon' variant='outline' asChild>
            <a href='https://space.bilibili.com/386835083' target='_blank'>
              <IconBrandBilibili className='size-5' />
              <span className='sr-only'>Bilibili</span>
            </a>
          </Button>
        </div>
      </div>
    </footer>
  )
}
