import Link from 'next/link'
import uniqolor from 'uniqolor'

import type { components } from '@/types/scraper'

import { Card, CardContent } from '@/components/ui/card'

interface SotaCategoryItemProps {
  category: components['schemas']['SotaAllCategory']
  lang: string
  taskCountText: string
}

export function SotaCategoryItem({ category, lang, taskCountText }: SotaCategoryItemProps) {
  const baseColor = uniqolor(category.area_id, {
    lightness: 60,
  }).color

  return (
    <Link href={`/${lang}/sota/category/${category.area_id}`} className='-mx-[1px] -mt-[1px]'>
      <Card
        className='transition-shadow hover:shadow-xl'
        style={{
          color: `color-mix(in oklab, ${baseColor} 50%, var(--color-fg))`,
          backgroundColor: `color-mix(in oklab, ${baseColor} 10%, transparent)`,
          borderColor: baseColor,
        }}
      >
        <CardContent className='space-y-1 px-3 py-2'>
          <h3 className='text-lg'>{category.area_name}</h3>
          <div className='text-fg/60 text-sm'>
            {category.task_all_num} {taskCountText}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
