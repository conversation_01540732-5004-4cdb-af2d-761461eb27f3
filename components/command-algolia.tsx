'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { usePara<PERSON>, usePathname, useRouter } from 'next/navigation'
import { liteClient as algoliasearch } from 'algoliasearch/lite'
import { Highlight, InstantSearch, useHits, useSearchBox } from 'react-instantsearch'
import type { MultipleQueriesQuery } from '@algolia/autocomplete-shared/dist/esm/preset-algolia/algoliasearch'
import { useProgress } from '@bprogress/next'
import { IconSearch } from '@tabler/icons-react'

import type { HyperPostAlgoliaHit, Locale } from '@/types'

import { ALGOLIA_PROXY_BASE, INSTANT_SEARCH_INDEX_NAME } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'

import { navLinks, type NavLink } from '@/components/nav-links'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { ScrollArea } from '@/components/ui/scroll-area'

const algoliaClient = algoliasearch('STWAK05FUV', '********************************', {
  hosts: [
    {
      url: ALGOLIA_PROXY_BASE,
      accept: 'read',
      protocol: 'https',
    },
  ],
})

// Avoid empty initial search, this can save the bill
const searchClient = {
  ...algoliaClient,
  search(requests: MultipleQueriesQuery[]) {
    if (requests.every(({ params }) => params && !params.query)) {
      return Promise.resolve({
        results: requests.map(() => ({
          hits: [],
          nbHits: 0,
          nbPages: 0,
          page: 0,
          processingTimeMS: 0,
          hitsPerPage: 0,
          exhaustiveNbHits: false,
          query: '',
          params: '',
        })),
      })
    }

    return algoliaClient.search(requests)
  },
}

function AlgoliaSearchResults({ setOpen }: { setOpen: React.Dispatch<React.SetStateAction<boolean>> }) {
  const params = useParams<{ lang: Locale }>()
  const pathname = usePathname()
  const router = useRouter()
  const resolvedBase = params?.lang ? `/${params.lang}` : ''

  const { query } = useSearchBox()
  const { hits } = useHits() as unknown as { hits: HyperPostAlgoliaHit[] }
  const { start } = useProgress()
  const { t } = useTranslation()

  // Get navigation links from nav-links.tsx
  const links = navLinks(resolvedBase, pathname, params?.lang || 'en')
  const resolvedLinks = links.filter(link => link.type === 'link')

  // Filter navigation links if there's a search query
  const filteredNavLinks = query
    ? resolvedLinks.filter(
        link =>
          link.title.toLowerCase().includes(query.toLowerCase()) ||
          link.href.toLowerCase().includes(query.toLowerCase()) ||
          link.desc.toLowerCase().includes(query.toLowerCase())
      )
    : resolvedLinks

  // Helper function to render a nav link
  const renderNavLink = (link: NavLink, index: number) => {
    return (
      <CommandItem
        key={`nav-${index}`}
        value={link.title}
        onSelect={() => {
          setOpen(false)
          start()
          // Navigate programmatically when Enter is pressed
          router.push(link.href)
        }}
      >
        <Link href={link.type === 'link' ? link.href : '#'} className='flex w-full'>
          <div className='flex gap-2'>
            {link.icon && <span className='mt-1'>{link.icon}</span>}
            {link.iconCmdk && <span className='mt-1'>{link.iconCmdk}</span>}
            <div>
              <div className='font-medium'>{link.title}</div>
              {link.desc && <div className='text-fg/70 text-xs'>{link.desc}</div>}
            </div>
          </div>
        </Link>
      </CommandItem>
    )
  }

  if (!query) {
    return (
      <CommandGroup heading={t.command.nav}>
        <ScrollArea>{resolvedLinks.map((link, index) => renderNavLink(link, index))}</ScrollArea>
      </CommandGroup>
    )
  }

  if (query && hits.length === 0 && filteredNavLinks.length === 0) {
    return <CommandEmpty>No results found.</CommandEmpty>
  }

  return (
    <>
      {filteredNavLinks.length > 0 && (
        <CommandGroup heading={t.command.nav}>
          <ScrollArea>{filteredNavLinks.map((link, index) => renderNavLink(link, index))}</ScrollArea>
        </CommandGroup>
      )}

      {hits.length > 0 && (
        <CommandGroup heading={t.command.searchResult}>
          <ScrollArea>
            {hits.map(hit => {
              const postType = hit.post_type
              const baseUrlMap: { [key: string]: string } = {
                post: 'news',
              }
              const resolvedPostType = postType in baseUrlMap ? baseUrlMap[postType] : postType
              const hitUrl = `${resolvedBase}/${resolvedPostType}/${hit.post_id}`

              return (
                <CommandItem
                  key={hit.objectID}
                  value={hit.post_title}
                  onSelect={() => {
                    setOpen(false)
                    start()
                    // Navigate programmatically when Enter is pressed
                    router.push(hitUrl)
                  }}
                >
                  <Link href={hitUrl} className='flex w-full'>
                    <div className='space-y-1'>
                      <div className='font-medium'>
                        <Highlight attribute='post_title' hit={hit} />
                      </div>
                      <div className='text-fg/80 line-clamp-2 text-sm'>
                        <Highlight attribute='content' hit={hit} />
                      </div>
                      <Badge>{hit.post_type_label}</Badge>
                    </div>
                  </Link>
                </CommandItem>
              )
            })}
          </ScrollArea>
        </CommandGroup>
      )}
    </>
  )
}

function AlgoliaCommandInput() {
  const { refine } = useSearchBox()
  const [inputValue, setInputValue] = useState('')
  const { t } = useTranslation()
  const handleChange = (value: string) => {
    setInputValue(value)
    refine(value)
  }

  return <CommandInput placeholder={t.command.placeholder} value={inputValue} onValueChange={handleChange} autoFocus />
}

export function Command() {
  const [open, setOpen] = useState(false)
  const { t } = useTranslation()

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen(open => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  return (
    <div>
      <Button onClick={() => setOpen(true)} variant='outline' className='rounded-full'>
        <IconSearch className='size-4' />
        <span className='text-fg/40 mr-2 hidden min-w-40 text-left sm:block'>{t.command.label}</span>
        <kbd className='bg-muted pointer-events-none hidden h-5 items-center gap-1 rounded border px-1.5 font-mono text-xs opacity-100 select-none sm:flex'>
          <span className='text-xs'>⌘</span>K
        </kbd>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <InstantSearch
          indexName={INSTANT_SEARCH_INDEX_NAME}
          // @ts-expect-error blame the library for not enforcing types
          searchClient={searchClient}
          future={{
            preserveSharedStateOnUnmount: true,
          }}
        >
          <AlgoliaCommandInput />
          <CommandList className='max-h-[calc(100dvh-6rem)]'>
            <AlgoliaSearchResults setOpen={setOpen} />
          </CommandList>
        </InstantSearch>
      </CommandDialog>
    </div>
  )
}
