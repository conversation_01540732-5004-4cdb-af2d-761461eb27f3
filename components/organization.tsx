import Image from 'next/image'
import Link from 'next/link'
import { IconBuildingBank } from '@tabler/icons-react'

import type { HyperOrgnization } from '@/types'

import { Tooltip } from '@/components/ui/tooltip'

export default function Organization({
  data,
  showLabel,
  enableLink,
}: {
  data: HyperOrgnization
  showLabel?: boolean
  enableLink?: boolean
}) {
  const orgContent = (
    <Tooltip label={`${data.name}${data.name_full_cn ? `（${data.name_full_cn}）` : ''}`}>
      <div className='flex items-center gap-1'>
        {data?.logo_image && data.logo_image[0] ? (
          <Image
            src={data.logo_image[0]}
            width={16}
            height={16}
            alt={data.name}
            className={data?.logo_options && data.logo_options.length > 0 ? data.logo_options.join(' ') : ''}
            unoptimized
          />
        ) : (
          <IconBuildingBank size={'1.25em'} />
        )}
        {showLabel && (
          <div key={data.id} className='text-fg/60'>
            {data.name_full_cn || data.name}
          </div>
        )}
      </div>
    </Tooltip>
  )

  return enableLink ? <Link href={`/search?orgs=${data.name}`}>{orgContent}</Link> : orgContent
}
