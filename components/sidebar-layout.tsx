import React from 'react'

import type { Locale } from '@/types'

import Footer from '@/components/footer'
import SidebarNav from '@/components/sidebar-nav'
import TopNav from '@/components/top-nav'

interface SidebarLayoutProps {
  lang: Locale
  children: React.ReactNode
}

export default function SidebarLayout({ lang, children }: SidebarLayoutProps) {
  return (
    <div className='flex h-screen overflow-hidden'>
      <SidebarNav />
      <main className='relative flex-1 overflow-y-auto'>
        <TopNav />
        <div className='mx-auto max-w-[1510px] p-4 pt-4 md:p-8 md:pt-5'>{children}</div>
        <Footer lang={lang} />
      </main>
    </div>
  )
}
