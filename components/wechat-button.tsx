import Image from 'next/image'
import { IconHelpCircleFilled } from '@tabler/icons-react'

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

export function WeChatButton() {
  return (
    <div className='fixed right-3 bottom-3 rounded-full bg-white'>
      <Popover>
        <PopoverTrigger asChild>
          <IconHelpCircleFilled size='2.5rem' className='text-ac cursor-help' />
        </PopoverTrigger>
        <PopoverContent side='top' align='end' className='w-[200px]'>
          <div className='space-y-2'>
            <div>遇到问题？可添加微信客服获得技术支持</div>
            <div>
              <Image
                src={'https://hyperai.s3.cn-north-1.amazonaws.com.cn/media/2020/07/pts6de92.svg'}
                width={180}
                height={180}
                className='rounded-sm'
                alt='WeChat QR Code'
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
