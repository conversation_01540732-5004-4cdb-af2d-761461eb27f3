'use client'

import React from 'react'
import { liteClient as algoliasearch } from 'algoliasearch/lite'
import { InstantSearch } from 'react-instantsearch'
import type { MultipleQueriesQuery } from '@algolia/autocomplete-shared/dist/esm/preset-algolia/algoliasearch'

import { ALGOLIA_PROXY_BASE, INSTANT_SEARCH_INDEX_NAME } from '@/lib/constants'

import { AlgoliaHitsNav, AlgoliaSearchBox } from './algolia'

const algoliaClient = algoliasearch('STWAK05FUV', '********************************', {
  hosts: [
    {
      url: ALGOLIA_PROXY_BASE,
      accept: 'read',
      protocol: 'https',
    },
  ],
})

// https://www.algolia.com/doc/guides/building-search-ui/going-further/conditional-requests/react/#detecting-empty-search-requests
const searchClient = {
  ...algoliaClient,

  // Avoid empty initial search, this can save the bill
  search(requests: MultipleQueriesQuery[]) {
    if (requests.every(({ params }) => params && !params.query)) {
      return Promise.resolve({
        results: requests.map(() => ({
          hits: [],
          nbHits: 0,
          nbPages: 0,
          page: 0,
          processingTimeMS: 0,
          hitsPerPage: 0,
          exhaustiveNbHits: false,
          query: '',
          params: '',
        })),
      })
    }

    return algoliaClient.search(requests)
  },
}

export default function AlgoliaSearchWrapper() {
  return (
    <InstantSearch
      indexName={INSTANT_SEARCH_INDEX_NAME}
      // @ts-expect-error blame the library for not enforcing types
      searchClient={searchClient}
      future={{
        preserveSharedStateOnUnmount: true,
      }}
    >
      {/* <Autocomplete
        searchClient={searchClient}
        placeholder='全站搜索'
        detachedMediaQuery='none'
        openOnFocus
      /> */}
      <AlgoliaSearchBox />
      <AlgoliaHitsNav />
    </InstantSearch>
  )
}
