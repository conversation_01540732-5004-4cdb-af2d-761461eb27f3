import { IconExternalLink } from '@tabler/icons-react'

import type { SearchResult } from '@/types/elasticsearch'

import { formatDate } from '@/utils/formatDate'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface SearchResultItemProps {
  result: SearchResult
  locale: string
  labels: {
    section: string
    createTime: string
    tags: string
    tagsFacts: string
    noTitle: string
    uncategorized: string
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

export function SearchResultItem({ result, locale, labels }: SearchResultItemProps) {
  const linkUrl = result.link?.raw
  const titleContent = result.title?.snippet || result.title?.raw || labels.noTitle
  const validLink = linkUrl && isValidUrl(linkUrl)

  return (
    <Card className='transition-shadow hover:shadow-md'>
      <CardHeader>
        <CardTitle className='text-lg'>
          {validLink ? (
            <a
              href={linkUrl}
              target='_blank'
              rel='noopener noreferrer'
              className='inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              <span dangerouslySetInnerHTML={{ __html: titleContent }} />
              <IconExternalLink className='h-3 w-3 opacity-70' />
            </a>
          ) : (
            <span className='cursor-default' dangerouslySetInnerHTML={{ __html: titleContent }} />
          )}
        </CardTitle>
        <CardDescription className='flex items-center gap-4 text-sm'>
          <span>
            {labels.section}: {result.section?.raw || labels.uncategorized}
          </span>
          {result.createtime?.raw && (
            <>
              <span className='text-muted-foreground'>•</span>
              <span>
                {labels.createTime}: {formatDate(result.createtime.raw, { locale })}
              </span>
            </>
          )}
        </CardDescription>
      </CardHeader>

      {(result.abstract?.snippet || result.tags?.raw || result.tags_facts?.raw) && (
        <CardContent className='space-y-3'>
          {result.abstract?.snippet && (
            <div
              className='text-muted-foreground line-clamp-3 text-sm'
              dangerouslySetInnerHTML={{ __html: result.abstract.snippet }}
            />
          )}

          {result.tags?.raw && (
            <div className='flex items-center gap-2 text-sm'>
              <span className='font-medium'>{labels.tags}:</span>
              <span className='text-muted-foreground'>
                {Array.isArray(result.tags.raw) ? result.tags.raw.join(', ') : result.tags.raw}
              </span>
            </div>
          )}

          {result.tags_facts?.raw && (
            <div className='flex items-start gap-2'>
              <span className='shrink-0 text-sm font-medium text-blue-600 dark:text-blue-400'>{labels.tagsFacts}:</span>
              <div className='flex flex-wrap gap-1.5'>
                {(Array.isArray(result.tags_facts.raw) ? result.tags_facts.raw : [result.tags_facts.raw]).map(
                  (tag, index) => (
                    <Badge key={index} tint='blue' size='sm'>
                      {tag}
                    </Badge>
                  )
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
