import type { ReactNode } from 'react'

import { cn } from '@/lib/cn'

interface SearchLayoutProps {
  header?: ReactNode
  sideContent?: ReactNode
  bodyContent?: ReactNode
  bodyHeader?: ReactNode
  bodyFooter?: ReactNode
  className?: string
}

export function SearchLayout({
  header,
  sideContent,
  bodyContent,
  bodyHeader,
  bodyFooter,
  className,
}: SearchLayoutProps) {
  return (
    <div className={cn('w-full', className)}>
      {/* Search Header */}
      {header && <div className='mb-6'>{header}</div>}

      {/* Main Content Area */}
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-4'>
        {/* Sidebar */}
        {sideContent && (
          <aside className='lg:col-span-1'>
            <div className='sticky top-4 space-y-4'>{sideContent}</div>
          </aside>
        )}

        {/* Body */}
        <main className={cn('', sideContent ? 'lg:col-span-3' : 'lg:col-span-4')}>
          {bodyHeader && <div className='mb-4'>{bodyHeader}</div>}

          {bodyContent && <div className='space-y-4'>{bodyContent}</div>}

          {bodyFooter && <div className='mt-6'>{bodyFooter}</div>}
        </main>
      </div>
    </div>
  )
}
