'use client'

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import Link from 'next/link'
import { useParams, usePathname, useRouter } from 'next/navigation'
import { useProgress } from '@bprogress/next'
import { IconSearch } from '@tabler/icons-react'

import type { Locale } from '@/types'
import type { SearchResponse, SearchResultHit } from '@/types/elasticsearch'

import { debounce } from '@/utils/debounce'
import { useTranslation } from '@/utils/i18n'

import { navLinks, type NavLink } from '@/components/nav-links'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { ScrollArea } from '@/components/ui/scroll-area'

// Search function using API route
async function searchElasticsearch(query: string, locale: Locale, signal?: AbortSignal): Promise<SearchResponse> {
  if (!query.trim()) return { hits: [] }

  try {
    const response = await fetch('/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        locale,
        size: 10,
      }),
      signal, // Add abort signal to fetch
    })

    if (!response.ok) {
      console.error('Search API failed:', response.statusText)
      return { hits: [] }
    }

    const data: SearchResponse = await response.json()
    return data
  } catch (error) {
    // Ignore abort errors
    if (error instanceof Error && error.name === 'AbortError') {
      return { hits: [] }
    }
    console.error('Search API error:', error)
    return { hits: [] }
  }
}

export function Command() {
  const [open, setOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResultHit[]>([])
  const [isSearching, setIsSearching] = useState(false)

  const params = useParams<{ lang: Locale }>()
  const pathname = usePathname()
  const router = useRouter()
  const { start } = useProgress()
  const { t } = useTranslation()

  // Use ref to store abort controller
  const abortControllerRef = useRef<AbortController | null>(null)

  const resolvedBase = params?.lang ? `/${params.lang}` : ''
  const locale = params?.lang || 'cn'

  // Get navigation links
  const links = navLinks(resolvedBase, pathname, locale)
  const resolvedLinks = links.filter(link => link.type === 'link')

  // Filter navigation links based on query
  const filteredNavLinks = query
    ? resolvedLinks.filter(
        link =>
          link.title.toLowerCase().includes(query.toLowerCase()) ||
          link.href.toLowerCase().includes(query.toLowerCase()) ||
          link.desc.toLowerCase().includes(query.toLowerCase())
      )
    : resolvedLinks

  // Create debounced search function
  const performSearch = useCallback(
    async (searchQuery: string) => {
      // Cancel any existing search request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      if (!searchQuery.trim()) {
        setSearchResults([])
        setIsSearching(false)
        return
      }

      // Create new abort controller for this request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      setIsSearching(true)

      try {
        const results = await searchElasticsearch(searchQuery, locale, abortController.signal)

        // Only update results if this request wasn't aborted
        if (!abortController.signal.aborted) {
          setSearchResults(results.hits)
          setIsSearching(false)
        }
      } catch {
        // Only set searching to false if this request wasn't aborted
        if (!abortController.signal.aborted) {
          setIsSearching(false)
        }
      }
    },
    [locale]
  )

  // Memoize the debounced function
  const debouncedSearch = useMemo(() => debounce(performSearch, 300), [performSearch])

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setQuery(value)
    debouncedSearch(value)
  }

  // Helper function to render a nav link
  const renderNavLink = (link: NavLink, index: number) => {
    return (
      <CommandItem
        key={`nav-${index}`}
        value={link.title}
        onSelect={() => {
          setOpen(false)
          start()
          router.push(link.href)
        }}
      >
        <Link href={link.type === 'link' ? link.href : '#'} className='flex w-full'>
          <div className='flex gap-2'>
            {link.icon && <span className='mt-1'>{link.icon}</span>}
            {link.iconCmdk && <span className='mt-1'>{link.iconCmdk}</span>}
            <div>
              <div className='font-medium'>{link.title}</div>
              {link.desc && <div className='text-fg/70 text-xs'>{link.desc}</div>}
            </div>
          </div>
        </Link>
      </CommandItem>
    )
  }

  // Keyboard shortcut
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen(open => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  // Reset search when dialog closes
  useEffect(() => {
    if (!open) {
      // Cancel any pending search request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
        abortControllerRef.current = null
      }
      setQuery('')
      setSearchResults([])
    }
  }, [open])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return (
    <div>
      <Button onClick={() => setOpen(true)} variant='outline' className='rounded-full'>
        <IconSearch className='size-4' />
        <span className='text-fg/40 mr-2 hidden min-w-40 text-left sm:block'>{t.command.label}</span>
        <kbd className='bg-muted pointer-events-none hidden h-5 items-center gap-1 rounded border px-1.5 font-mono text-xs opacity-100 select-none sm:flex'>
          <span className='text-xs'>⌘</span>K
        </kbd>
      </Button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder={t.command.placeholder} value={query} onValueChange={handleSearchChange} autoFocus />
        <CommandList className='max-h-[calc(100dvh-6rem)]'>
          {!query && (
            <CommandGroup heading={t.command.nav}>
              <ScrollArea>{resolvedLinks.map((link, index) => renderNavLink(link, index))}</ScrollArea>
            </CommandGroup>
          )}

          {query && !isSearching && searchResults.length === 0 && filteredNavLinks.length === 0 && (
            <CommandEmpty>No results found.</CommandEmpty>
          )}

          {query && filteredNavLinks.length > 0 && (
            <CommandGroup heading={t.command.nav}>
              <ScrollArea>{filteredNavLinks.map((link, index) => renderNavLink(link, index))}</ScrollArea>
            </CommandGroup>
          )}

          {searchResults.length > 0 && (
            <CommandGroup heading={t.command.searchResult} forceMount>
              <ScrollArea>
                {searchResults.map(hit => {
                  const postType = hit.post_type
                  const baseUrlMap: { [key: string]: string } = {
                    post: 'news',
                  }
                  const resolvedPostType = postType in baseUrlMap ? baseUrlMap[postType] : postType
                  const hitUrl = hit.link || `${resolvedBase}/${resolvedPostType}/${hit.post_id}`

                  return (
                    <CommandItem
                      key={hit.objectID}
                      value={hit.post_title}
                      onSelect={() => {
                        setOpen(false)
                        start()
                        router.push(hitUrl)
                      }}
                    >
                      <Link href={hitUrl} className='flex w-full'>
                        <div className='space-y-1'>
                          <div
                            className='font-medium [&_em]:bg-yellow-200/30 [&_em]:font-semibold [&_em]:not-italic'
                            dangerouslySetInnerHTML={{ __html: hit.post_title }}
                          />
                          {hit.abstract && (
                            <div
                              className='text-fg/80 line-clamp-2 text-sm [&_em]:bg-yellow-200/30 [&_em]:font-semibold [&_em]:not-italic'
                              dangerouslySetInnerHTML={{ __html: hit.abstract }}
                            />
                          )}
                          <Badge>{hit.post_type_label}</Badge>
                        </div>
                      </Link>
                    </CommandItem>
                  )
                })}
              </ScrollArea>
            </CommandGroup>
          )}

          {isSearching && <div className='text-muted-foreground py-6 text-center text-sm'>Searching...</div>}
        </CommandList>
      </CommandDialog>
    </div>
  )
}
