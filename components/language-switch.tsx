'use client'

import { use<PERSON><PERSON><PERSON>, usePathname, useRouter } from 'next/navigation'

import 'country-flag-icons/react/3x2'

// Import flag components
import {
  AE as AEFlag,
  CN as CNFlag,
  DE as DEFlag,
  FR as FRFlag,
  JP as JPFlag,
  KR as KRFlag,
  US as USFlag,
} from 'country-flag-icons/react/3x2'
import { IconWorld } from '@tabler/icons-react'

import { cn } from '@/lib/cn'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown'

// Language options matching those in middleware.ts
const languages = [
  {
    code: 'en',
    name: 'English',
    name_en: 'English',
    icon: <USFlag className='h-4! w-6! rounded-xs' title='United States' />,
  },
  {
    code: 'cn',
    name: '中文',
    name_en: 'Chinese',
    icon: <CNFlag className='h-4! w-6! rounded-xs' title='China' />,
  },
  {
    code: 'ja',
    name: '日本語',
    name_en: 'Japanese',
    icon: <JPFlag className='h-4! w-6! rounded-xs border' title='Japan' />,
  },
  {
    code: 'kr',
    name: '한국어',
    name_en: 'Korean',
    icon: <KRFlag className='h-4! w-6! rounded-xs border' title='Korea' />,
  },
  {
    code: 'ar',
    name: 'العربية',
    name_en: 'Arabic',
    icon: <AEFlag className='h-4! w-6! rounded-xs' title='United Arab Emirates' />,
  },
  {
    code: 'fr',
    name: 'Français',
    name_en: 'French',
    icon: <FRFlag className='h-4! w-6! rounded-xs' title='France' />,
  },
  {
    code: 'de',
    name: 'Deutsch',
    name_en: 'German',
    icon: <DEFlag className='h-4! w-6! rounded-xs' title='Germany' />,
  },
]

export interface LanguageSwitchProps {
  isCollapsed?: boolean
}

export function LanguageSwitch({ isCollapsed = false }: LanguageSwitchProps) {
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams<{ lang: string }>()
  const currentLang = params?.lang || 'cn'

  const handleLanguageChange = (langCode: string) => {
    // Get the path without the language prefix
    const pathWithoutLang = pathname ? pathname.replace(`/${currentLang}`, '') : ''

    // Navigate to the same path but with the new language
    router.push(`/${langCode}${pathWithoutLang}`)
  }

  const currentLanguage = languages.find(lang => lang.code === currentLang)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className='flex cursor-pointer items-center gap-1.5'>
          {/* {currentLanguage?.icon} */}
          <IconWorld className='size-4' />
          {!isCollapsed && <span className='text-sm font-medium'>{currentLanguage?.name}</span>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {languages.map(lang => (
          <DropdownMenuItem
            key={lang.code}
            className={cn('flex items-center', lang.code === currentLang && 'text-ac')}
            onClick={() => handleLanguageChange(lang.code)}
          >
            {/* {lang.icon} */}
            {lang.name}
            {lang.name !== lang.name_en && <span className='text-fg/60'>({lang.name_en})</span>}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
