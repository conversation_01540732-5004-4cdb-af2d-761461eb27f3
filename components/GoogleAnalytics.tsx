'use client'

// https://stackoverflow.com/a/76760064/412385
import Script from 'next/script'

import * as gtag from '@/lib/gtag'

/**
 * @deprecated use `@next/third-parties`
 * @link https://nextjs.org/docs/app/building-your-application/optimizing/third-party-libraries#google-analytics
 */
export default function GoogleAnalytics() {
  gtag.useGtag()

  return (
    <>
      {/* Global Site Tag (gtag.js) - Google Analytics */}
      <Script strategy='afterInteractive' src={`https://www.googletagmanager.com/gtag/js?id=G-YY2E0ZQRP8`} />
      <Script id='gtag-init' strategy='afterInteractive'>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-YY2E0ZQRP8', {
            page_path: window.location.pathname,
          });
        `}
      </Script>
    </>
  )
}
