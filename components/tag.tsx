'use client'

import { cloneElement, type JSX } from 'react'
import { useTheme } from 'next-themes'
import Link from 'next/link'
import uniqolor from 'uniqolor'
import {
  IconBrain,
  IconCar,
  IconCpu,
  IconDna2,
  IconEye,
  IconEyeSearch,
  IconFish,
  IconFlask,
  IconLockBolt,
  IconMedicalCross,
  IconPhotoOff,
  IconSchool,
  IconSunWind,
  IconTag,
  IconTextCaption,
  IconUserSquareRounded,
  IconVideo,
  IconWood,
  IconZoomCheck,
} from '@tabler/icons-react'

import type { AlgoliaAvailableRoutingParams, HyperCustomTag, HyperTag } from '@/types'

import { cn } from '@/lib/cn'

export interface TagProps {
  data: HyperTag | HyperCustomTag
  active?: boolean
  showCount?: boolean
  icon?: JSX.Element
  /**
   * 是否开启链接支持
   */
  enableLink?: AlgoliaAvailableRoutingParams
}

const tagIconMap: {
  [key: string]: JSX.Element
} = {
  '化学': <IconFlask />,
  '生物学': <IconDna2 />,
  '医学': <IconMedicalCross />,
  '人物志': <IconUserSquareRounded />,
  '材料': <IconWood />,
  '气象学': <IconSunWind />,
  '隐私安全': <IconLockBolt />,
  'AI for Science': <IconCpu />,
  '海洋学': <IconFish />,
  '神经网络': <IconBrain />,
  '国外高校': <IconSchool />,
  '机器视觉': <IconEye />,
  '目标检测': <IconEyeSearch />,
  '图像分类': <IconPhotoOff />,
  'NLP': <IconTextCaption />,
  '自动驾驶': <IconCar />,
  '超分辨率': <IconZoomCheck />,
  '视频处理': <IconVideo />,
}

function DynamicIcon({ name, ...props }: { name: string }) {
  return tagIconMap[name] ? cloneElement(tagIconMap[name], props) : <IconTag {...props} />
}

export default function Tag({ data, active, showCount, icon, enableLink }: TagProps) {
  const { resolvedTheme } = useTheme()

  const baseColor = uniqolor(data.name, {
    lightness: resolvedTheme === 'light' ? 50 : 60,
  }).color

  const iconProps = {
    // color: active ? theme.colors.red[6] : theme.colors.gray[6],
    color: active ? 'var(--color-bg)' : baseColor,
    size: '1em',
  }

  const tagContent = (
    <div
      className={cn(
        'flex items-center gap-1 overflow-hidden rounded-md border pr-2 text-sm',
        enableLink && !active && 'group-hover:border-current!'
      )}
      style={{
        // used to inherit border color
        color: active ? '#fff' : baseColor,
        borderColor: active ? baseColor : `color-mix(in oklab, ${baseColor} 20%, transparent)`,
        backgroundColor: active ? baseColor : 'transparent',
      }}
    >
      {/* Icon */}
      <div
        className={cn('flex items-center p-1')}
        style={{
          backgroundColor: active ? baseColor : `color-mix(in oklab, ${baseColor} 10%, transparent)`,
        }}
      >
        {icon ? cloneElement(icon, iconProps) : <DynamicIcon name={data.name} {...iconProps} />}
      </div>
      <div
        style={{
          color: active ? 'var(--color-bg)' : `color-mix(in oklab, ${baseColor} 50%, var(--color-fg))`,
        }}
      >
        {data.name}
      </div>
      {data.count && showCount ? (
        <div className={cn(active ? 'text-bg' : 'text-fg/60', 'opacity-50')}>{data.count}</div>
      ) : null}
    </div>
  )

  return enableLink ? (
    <Link href={`/search?${enableLink}=${data.name}`} className='group'>
      {tagContent}
    </Link>
  ) : (
    tagContent
  )
}
