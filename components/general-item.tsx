import Link from 'next/link'
import { Highlight } from 'react-instantsearch'

import type { HyperPostAlgoliaHit } from '@/types'

import Timestamp from '@/components/timestamp'
import { Card, CardContent } from '@/components/ui/card'

import PageBody from './page-body'

export function GeneralItem({ data, hardLink }: { data: HyperPostAlgoliaHit; hardLink?: boolean }) {
  const LinkTag = hardLink ? 'a' : Link
  const baseUrlMap: { [key: string]: string } = {
    post: 'news',
  }

  const postType = data.post_type
  const resolvedPostType = postType in baseUrlMap ? baseUrlMap[postType] : postType
  console.log(`resolvedPostType`, resolvedPostType)

  return (
    <LinkTag href={`/${resolvedPostType}/${data.post_id}`} className={hardLink ? '' : 'block'}>
      <Card className='w-full transition-shadow hover:shadow-xl'>
        <CardContent className='space-y-2 p-4'>
          <div>
            <h3 className='line-clamp-1 text-lg font-medium'>
              <Highlight attribute='post_title' hit={data} />
            </h3>

            <div className='text-fg/60 text-xs'>
              <Timestamp date={new Date(data.post_date * 1000).toString()} />
            </div>
          </div>

          <PageBody content={data.content} clamp={2} />
        </CardContent>
      </Card>
    </LinkTag>
  )
}
