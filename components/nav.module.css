.navLinkWrap {
  overflow-y: hidden;
  overflow-x: hidden;
  white-space: nowrap;
  position: relative;

  @media (max-width: 767px) {
    &::after {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      bottom: 0;
      width: 2em;
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
      pointer-events: none;
    }
  }
}

.navLink {
  font-weight: bold;
  color: var(--mantine-color-black);
  padding: 0.25rem 0.4rem;

  &:hover,
  &:focus,
  &.active {
    color: var(--mantine-color-red-6);
  }
}

.wrap {
  border-bottom: 1px solid var(--mantine-color-gray-2);
  background-color: var(--mantine-color-white);
  position: sticky;
  z-index: 100;
  top: 0;
}

.algoliaResult {
  position: fixed;
  top: 61px;
  left: 0;
  right: 0;
  background-color: var(--mantine-color-white);
  z-index: 100;
  box-shadow: var(--mantine-shadow-sm), var(--mantine-shadow-xl);
}
