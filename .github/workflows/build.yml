name: Build

on:
  push:
    branches:
      - 'master'
    tags:
      - 'v*'
  pull_request:
    branches:
      - 'master'
  repository_dispatch:
    types:
      - webhook
  workflow_dispatch:

env:
  DOCKERHUB_SLUG: openbayes/hyperai-next
  GHCR_SLUG: ghcr.io/signcl/hyperai-next
  VRP_SLUG: r.vrp.moe/signcl/hyperai-next

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run unit tests (Vitest)
        run: pnpm test:run
        env:
          CI: true

  build:
    needs: test
    runs-on: ubuntu-latest-8c-32g

    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}

      - name: Log in to Docker Hub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}

      - name: Login to GitHub Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to VRP Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: r.vrp.moe
          username: ${{ secrets.VRP_USER }}
          password: ${{ secrets.VRP_PASS }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.DOCKERHUB_SLUG }}
            ${{ env.GHCR_SLUG }}
          tags: |
            type=edge
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha

      - name: Build and push
        uses: docker/bake-action@v6
        with:
          files: |
            ./docker-bake.hcl
            ${{ steps.meta.outputs.bake-file }}
          source: .
          targets: build-all
          push: ${{ github.event_name != 'pull_request' }}
          set: |
            *.cache-from=type=gha
            *.cache-to=type=gha,mode=max
