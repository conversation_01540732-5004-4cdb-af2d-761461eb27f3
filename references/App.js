import React, { useState, useEffect } from "react";
import ElasticsearchAPIConnector from "@elastic/search-ui-elasticsearch-connector";

import {
  ErrorBoundary,
  Facet,
  SearchProvider,
  SearchBox,
  Results,
  PagingInfo,
  ResultsPerPage,
  Paging,
  Sorting,
  WithSearch
} from "@elastic/react-search-ui";
import { Layout } from "@elastic/react-search-ui-views";
import "@elastic/react-search-ui-views/lib/styles/styles.css";

// 获取URL参数的函数
const getUrlParameter = (name) => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
};

// 定义索引配置 - 修改为基础索引名
const INDEX_CONFIGS = {
  chinese: {
    baseIndex: "search_hyperai_cn", // 基础索引名
    locale: "zh-CN",
    direction: "ltr",
    labels: {
      searchPlaceholder: "搜索标题、内容、标签...",
      sectionTitle: "相关文档",
      sortLabel: "排序方式",
      relevance: "相关性",
      timeNewest: "时间（最新）",
      timeOldest: "时间（最早）",
      sectionLabel: "栏目分类",
      tagsLabel: "标签",
      tagsFactsLabel: "展示标签", // 添加展示标签
      createTimeLabel: "创建时间",
      noTitle: "无标题",
      uncategorized: "未分类",
      section: "栏目",
      createTime: "创建时间",
      tags: "标签",
      tagsFacts: "展示标签", // 添加展示标签
      // 时间范围标签
      last7Days: "最近7天",
      last7To30Days: "7-30天前",
      last30To90Days: "30-90天前",
      last90DaysTo1Year: "90天-1年前",
      over1Year: "1年前"
    }
  },
  english: {
    baseIndex: "search_hyperai_en",
    locale: "en-US",
    direction: "ltr",
    labels: {
      searchPlaceholder: "Search titles, content, tags...",
      sectionTitle: "Related Documents",
      sortLabel: "Sort by",
      relevance: "Relevance",
      timeNewest: "Time (Newest)",
      timeOldest: "Time (Oldest)",
      sectionLabel: "Section Category",
      tagsLabel: "Tags",
      tagsFactsLabel: "Display Tags", // 添加展示标签
      createTimeLabel: "Create Time",
      noTitle: "No Title",
      uncategorized: "Uncategorized",
      section: "Section",
      createTime: "Create Time",
      tags: "Tags",
      tagsFacts: "Display Tags", // 添加展示标签
      // 时间范围标签
      last7Days: "Last 7 days",
      last7To30Days: "7-30 days ago",
      last30To90Days: "30-90 days ago",
      last90DaysTo1Year: "90 days - 1 year ago",
      over1Year: "Over 1 year ago"
    }
  },
  japanese: {
    baseIndex: "search_hyperai_ja",
    locale: "ja-JP",
    direction: "ltr",
    labels: {
      searchPlaceholder: "タイトル、内容、タグを検索...",
      sectionTitle: "関連文書",
      sortLabel: "並び順",
      relevance: "関連性",
      timeNewest: "時間（新しい順）",
      timeOldest: "時間（古い順）",
      sectionLabel: "セクション分類",
      tagsLabel: "タグ",
      tagsFactsLabel: "表示タグ", // 添加展示标签
      createTimeLabel: "作成時間",
      noTitle: "タイトルなし",
      uncategorized: "未分類",
      section: "セクション",
      createTime: "作成時間",
      tags: "タグ",
      tagsFacts: "表示タグ", // 添加展示标签
      // 时间范围标签
      last7Days: "過去7日間",
      last7To30Days: "7-30日前",
      last30To90Days: "30-90日前",
      last90DaysTo1Year: "90日-1年前",
      over1Year: "1年以上前"
    }
  },
  french: {
    baseIndex: "search_hyperai_fr",
    locale: "fr-FR",
    direction: "ltr",
    labels: {
      searchPlaceholder: "Rechercher titres, contenu, tags...",
      sectionTitle: "Documents connexes",
      sortLabel: "Trier par",
      relevance: "Pertinence",
      timeNewest: "Temps (Plus récent)",
      timeOldest: "Temps (Plus ancien)",
      sectionLabel: "Catégorie de section",
      tagsLabel: "Tags",
      tagsFactsLabel: "Tags d'affichage", // 添加展示标签
      createTimeLabel: "Heure de création",
      noTitle: "Aucun titre",
      uncategorized: "Non catégorisé",
      section: "Section",
      createTime: "Heure de création",
      tags: "Tags",
      tagsFacts: "Tags d'affichage", // 添加展示标签
      // 时间范围标签
      last7Days: "7 derniers jours",
      last7To30Days: "Il y a 7-30 jours",
      last30To90Days: "Il y a 30-90 jours",
      last90DaysTo1Year: "Il y a 90 jours - 1 an",
      over1Year: "Il y a plus d'un an"
    }
  },
  german: {
    baseIndex: "search_hyperai_de",
    locale: "de-DE",
    direction: "ltr",
    labels: {
      searchPlaceholder: "Titel, Inhalt, Tags durchsuchen...",
      sectionTitle: "Verwandte Dokumente",
      sortLabel: "Sortieren nach",
      relevance: "Relevanz",
      timeNewest: "Zeit (Neueste)",
      timeOldest: "Zeit (Älteste)",
      sectionLabel: "Bereichskategorie",
      tagsLabel: "Tags",
      tagsFactsLabel: "Anzeige-Tags", // 添加展示标签
      createTimeLabel: "Erstellungszeit",
      noTitle: "Kein Titel",
      uncategorized: "Unkategorisiert",
      section: "Bereich",
      createTime: "Erstellungszeit",
      tags: "Tags",
      tagsFacts: "Anzeige-Tags", // 添加展示标签
      // 时间范围标签
      last7Days: "Letzte 7 Tage",
      last7To30Days: "Vor 7-30 Tagen",
      last30To90Days: "Vor 30-90 Tagen",
      last90DaysTo1Year: "Vor 90 Tagen - 1 Jahr",
      over1Year: "Vor über einem Jahr"
    }
  },
  korean: {
    baseIndex: "search_hyperai_kr",
    locale: "ko-KR",
    direction: "ltr",
    labels: {
      searchPlaceholder: "제목, 내용, 태그 검색...",
      sectionTitle: "관련 문서",
      sortLabel: "정렬 기준",
      relevance: "관련성",
      timeNewest: "시간 (최신순)",
      timeOldest: "시간 (오래된순)",
      sectionLabel: "섹션 분류",
      tagsLabel: "태그",
      tagsFactsLabel: "표시 태그", // 添加展示标签
      createTimeLabel: "생성 시간",
      noTitle: "제목 없음",
      uncategorized: "미분류",
      section: "섹션",
      createTime: "생성 시간",
      tags: "태그",
      tagsFacts: "표시 태그", // 添加展示标签
      // 时间范围标签
      last7Days: "최근 7일",
      last7To30Days: "7-30일 전",
      last30To90Days: "30-90일 전",
      last90DaysTo1Year: "90일-1년 전",
      over1Year: "1년 이상 전"
    }
  },
  arabic: {
    baseIndex: "search_hyperai_ar",
    locale: "ar-SA",
    direction: "rtl", // 阿拉伯语从右到左
    labels: {
      searchPlaceholder: "البحث في العناوين والمحتوى والعلامات...",
      sectionTitle: "الوثائق ذات الصلة",
      sortLabel: "ترتيب حسب",
      relevance: "الصلة",
      timeNewest: "الوقت (الأحدث)",
      timeOldest: "الوقت (الأقدم)",
      sectionLabel: "فئة القسم",
      tagsLabel: "العلامات",
      tagsFactsLabel: "علامات العرض", // 添加展示标签
      createTimeLabel: "وقت الإنشاء",
      noTitle: "بلا عنوان",
      uncategorized: "غير مصنف",
      section: "القسم",
      createTime: "وقت الإنشاء",
      tags: "العلامات",
      tagsFacts: "علامات العرض", // 添加展示标签
      // 时间范围标签
      last7Days: "آخر 7 أيام",
      last7To30Days: "منذ 7-30 يوم",
      last30To90Days: "منذ 30-90 يوم",
      last90DaysTo1Year: "منذ 90 يوم - سنة",
      over1Year: "منذ أكثر من سنة"
    }
  }
};

// 构建完整索引名的函数
const buildIndexName = (baseIndex, suffix) => {
  return suffix ? `${baseIndex}_${suffix}` : baseIndex;
};

// 创建连接器的函数
const createConnector = (indexName) => {
  return new ElasticsearchAPIConnector({
    host: "http://************:9200",
    index: indexName,
    apiKey: "N2MyM2FaY0JTTjNIY20tNFRndkw6WlFmZGVNa3g4aDR3VW5xaWZ2Z2Nidw=="
  });
};

// 创建配置的函数
const createConfig = (indexConfig, indexSuffix) => {
  const fullIndexName = buildIndexName(indexConfig.baseIndex, indexSuffix);
  const connector = createConnector(fullIndexName);

  return {
    searchQuery: {
      search_fields: {
        title: {
          weight: 3  // 标题权重最高
        },
        abstract: {
          weight: 2  // 摘要权重次之
        },
        content: {
          weight: 1.5  // 内容权重中等
        },
        tags: {
          weight: 2  // 标签权重较高
        },
        full_text: {
          weight: 1  // 全文权重最低
        }
      },
      result_fields: {
        id: {
          raw: {}
        },
        title: {
          snippet: {
            size: 100,
            fallback: true
          }
        },
        abstract: {
          snippet: {
            size: 200,
            fallback: true
          }
        },
        content: {
          snippet: {
            size: 300,
            fallback: true
          }
        },
        section: {
          raw: {}
        },
        tags: {
          raw: {}
        },
        // 添加 tags_facts 字段
        tags_facts: {
          raw: {}
        },
        createtime: {
          raw: {}
        },
        createstamp: {
          raw: {}
        },
        link: {
          raw: {}
        }
      },
      disjunctiveFacets: ["section", "tags_facts.keyword"],
      facets: {
        "section": {
          type: "value",
          size: 20
        },
        "tags_facts.keyword": {
          type: "value",
          size: 30
        },
        "createtime": {
          type: "range",
          ranges: [
            {
              from: "now-7d",
              name: indexConfig.labels.last7Days
            },
            {
              from: "now-30d",
              to: "now-7d",
              name: indexConfig.labels.last7To30Days
            },
            {
              from: "now-90d",
              to: "now-30d",
              name: indexConfig.labels.last30To90Days
            },
            {
              from: "now-1y",
              to: "now-90d",
              name: indexConfig.labels.last90DaysTo1Year
            },
            {
              to: "now-1y",
              name: indexConfig.labels.over1Year
            }
          ]
        }
      }
    },
    autocompleteQuery: {
      results: {
        resultsPerPage: 5,
        search_fields: {
          "title": {
            weight: 3
          },
          "tags": {
            weight: 2
          }
        },
        result_fields: {
          id: {
            raw: {}
          },
          title: {
            snippet: {
              size: 100,
              fallback: true
            }
          },
          section: {
            raw: {}
          },
          link: {
            raw: {}
          }
        }
      },
      suggestions: {
        types: {
          documents: {
            fields: ["suggest_title"]
          },
          tags: {
            fields: ["suggest_tags"]
          }
        },
        size: 6
      }
    },
    apiConnector: connector,
    alwaysSearchOnInitialLoad: true
  };
};
// 索引信息显示组件
function IndexInfo({ currentLanguage, indexSuffix }) {
  const indexConfig = INDEX_CONFIGS[currentLanguage];
  const fullIndexName = buildIndexName(indexConfig.baseIndex, indexSuffix);

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      left: '20px',
      backgroundColor: '#f5f5f5',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      color: '#666',
      border: '1px solid #ddd',
      zIndex: 1000
    }}>
      当前索引: {fullIndexName}
    </div>
  );
}

// 语言切换组件
function LanguageToggle({ currentLanguage, onLanguageChange, direction }) {
  const languageOptions = [
    { value: "chinese", label: "中文", flag: "🇨🇳" },
    { value: "english", label: "English", flag: "🇺🇸" },
    { value: "japanese", label: "日本語", flag: "🇯🇵" },
    { value: "french", label: "Français", flag: "🇫🇷" },
    { value: "german", label: "Deutsch", flag: "🇩🇪" },
    { value: "korean", label: "한국어", flag: "🇰🇷" },
    { value: "arabic", label: "العربية", flag: "🇸🇦" }
  ];

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      left: direction === 'rtl' ? '20px' : 'auto',
      right: direction === 'rtl' ? 'auto' : '20px',
      zIndex: 1000,
      backgroundColor: 'white',
      padding: '8px',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      border: '1px solid #e0e0e0'
    }}>
      <select
        value={currentLanguage}
        onChange={(e) => onLanguageChange(e.target.value)}
        style={{
          padding: '8px 12px',
          borderRadius: '6px',
          border: '1px solid #ccc',
          backgroundColor: 'white',
          fontSize: '14px',
          minWidth: '150px',
          cursor: 'pointer',
          outline: 'none'
        }}
      >
        {languageOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.flag} {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}

// 自定义结果组件
function CustomResults({ labels, direction }) {
  return (
    <Results
      resultView={({ result, onClickLink }) => {
        const linkUrl = result.link?.raw;
        const titleContent = result.title?.snippet || result.title?.raw || labels.noTitle;

        // 检查是否是有效的URL
        const isValidUrl = (string) => {
          try {
            new URL(string);
            return true;
          } catch (_) {
            return false;
          }
        };

        const validLink = linkUrl && isValidUrl(linkUrl);

        return (
          <div className="sui-result" style={{ direction: direction }}>
            <div className="sui-result__header">
              <h3 className="sui-result__title">
                {validLink ? (
                  <a
                    href={linkUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      color: '#1a73e8',
                      textDecoration: 'none',
                      cursor: 'pointer',
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.textDecoration = 'none';
                    }}
                  >
                    <span
                      dangerouslySetInnerHTML={{
                        __html: titleContent
                      }}
                    />
                    {/* 外链图标 */}
                    <span style={{ fontSize: '12px', opacity: 0.7 }}>🔗</span>
                  </a>
                ) : (
                  <a
                    onClick={onClickLink}
                    href="#"
                    style={{
                      color: '#1a73e8',
                      textDecoration: 'none',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.textDecoration = 'none';
                    }}
                    dangerouslySetInnerHTML={{
                      __html: titleContent
                    }}
                  />
                )}
              </h3>
              <div className="sui-result__meta">
                <span className="sui-result__key">{labels.section}: </span>
                <span className="sui-result__value">{result.section?.raw || labels.uncategorized}</span>
                {result.createtime?.raw && (
                  <>
                    <span className="sui-result__key"> | {labels.createTime}: </span>
                    <span className="sui-result__value">
                      {new Date(result.createtime.raw).toLocaleDateString(
                        INDEX_CONFIGS[Object.keys(INDEX_CONFIGS).find(key =>
                          INDEX_CONFIGS[key].labels === labels
                        )]?.locale || 'en-US'
                      )}
                    </span>
                  </>
                )}
              </div>
            </div>
            <div className="sui-result__body">
              {result.abstract?.snippet && (
                <div
                  className="sui-result__snippet"
                  dangerouslySetInnerHTML={{
                    __html: result.abstract.snippet
                  }}
                />
              )}

              {/* 显示普通标签 */}
              {result.tags?.raw && (
                <div className="sui-result__tags" style={{ marginBottom: '8px' }}>
                  <span className="sui-result__key">{labels.tags}: </span>
                  <span className="sui-result__value">
                    {Array.isArray(result.tags.raw)
                      ? result.tags.raw.join(", ")
                      : result.tags.raw}
                  </span>
                </div>
              )}

              {/* 显示展示标签 (tags_facts) */}
              {result.tags_facts?.raw && (
                <div className="sui-result__tags-facts" style={{ marginBottom: '8px' }}>
                  <span className="sui-result__key" style={{ color: '#1976d2' }}>{labels.tagsFacts}: </span>
                  <span className="sui-result__value">
                    {Array.isArray(result.tags_facts.raw)
                      ? result.tags_facts.raw.map((tag, index) => (
                          <span
                            key={index}
                            style={{
                              display: 'inline-block',
                              backgroundColor: '#e3f2fd',
                              color: '#1976d2',
                              padding: '2px 8px',
                              margin: '2px 4px 2px 0',
                              borderRadius: '12px',
                              fontSize: '12px',
                              border: '1px solid #bbdefb'
                            }}
                          >
                            {tag}
                          </span>
                        ))
                      : (
                          <span
                            style={{
                              display: 'inline-block',
                              backgroundColor: '#e3f2fd',
                              color: '#1976d2',
                              padding: '2px 8px',
                              margin: '2px 4px 2px 0',
                              borderRadius: '12px',
                              fontSize: '12px',
                              border: '1px solid #bbdefb'
                            }}
                          >
                            {result.tags_facts.raw}
                          </span>
                        )}
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      }}
    />
  );
}

// 主应用组件
function SearchApp({ language, indexSuffix }) {
  const indexConfig = INDEX_CONFIGS[language];
  const config = createConfig(indexConfig, indexSuffix);
  const labels = indexConfig.labels;
  const direction = indexConfig.direction;

  return (
    <div style={{ direction: direction, paddingTop: '80px' }}>
      <SearchProvider config={config} key={`${language}-${indexSuffix}`}>
        <WithSearch mapContextToProps={({ wasSearched }) => ({ wasSearched })}>
          {({ wasSearched }) => {
            return (
              <div className="App" style={{ direction: direction }}>
                <ErrorBoundary>
                  <Layout
                    header={
                      <SearchBox
                        autocompleteMinimumCharacters={2}
                        autocompleteResults={{
                          sectionTitle: labels.sectionTitle,
                          titleField: "title",
                          shouldTrackClickThrough: true
                        }}
                        autocompleteSuggestions={true}
                        debounceLength={300}
                        placeholder={labels.searchPlaceholder}
                      />
                    }
                    sideContent={
                      <div>
                        {wasSearched && (
                          <Sorting
                            label={labels.sortLabel}
                            sortOptions={[
                              {
                                name: labels.relevance,
                                value: "",
                                direction: ""
                              },
                              {
                                name: labels.timeNewest,
                                value: "createstamp",
                                direction: "desc"
                              },
                              {
                                name: labels.timeOldest,
                                value: "createstamp",
                                direction: "asc"
                              }
                            ]}
                          />
                        )}
                        <Facet
                          key={"section"}
                          field={"section"}
                          label={labels.sectionLabel}
                          filterType="any"
                          isFilterable={true}
                        />
                        <Facet
                          key={"tags_facts"}
                          field={"tags_facts.keyword"}
                          label={labels.tagsLabel}
                          filterType="any"
                          isFilterable={true}
                        />
                        <Facet
                          key={"createtime"}
                          field={"createtime"}
                          label={labels.createTimeLabel}
                          filterType="any"
                        />
                      </div>
                    }
                    bodyContent={<CustomResults labels={labels} direction={direction} />}
                    bodyHeader={
                      <React.Fragment>
                        {wasSearched && <PagingInfo />}
                        {wasSearched && <ResultsPerPage options={[10, 20, 50]} />}
                      </React.Fragment>
                    }
                    bodyFooter={<Paging />}
                  />
                </ErrorBoundary>
              </div>
            );
          }}
        </WithSearch>
      </SearchProvider>
    </div>
  );
}

// 主应用入口
export default function App() {
  const [currentLanguage, setCurrentLanguage] = useState('chinese');
  const [indexSuffix, setIndexSuffix] = useState('');

  // 从URL获取索引后缀
  useEffect(() => {
    const suffix = getUrlParameter('suffix');
    if (suffix) {
      setIndexSuffix(suffix);
    }
  }, []);

  // 为阿拉伯语添加RTL样式
  const appStyle = {
    position: 'relative',
    direction: INDEX_CONFIGS[currentLanguage]?.direction || 'ltr',
    minHeight: '100vh'
  };

  // 动态添加RTL样式
  React.useEffect(() => {
    const isRTL = INDEX_CONFIGS[currentLanguage]?.direction === 'rtl';
    document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
    document.documentElement.setAttribute('lang', INDEX_CONFIGS[currentLanguage]?.locale?.split('-')[0] || 'en');

    // 为RTL语言添加自定义CSS
    const existingStyle = document.getElementById('rtl-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    if (isRTL) {
      const style = document.createElement('style');
      style.id = 'rtl-styles';
      style.textContent = `
        .sui-search-box__wrapper {
          direction: rtl;
        }
        .sui-search-box__text-input {
          text-align: right;
          direction: rtl;
        }
        .sui-facet {
          direction: rtl;
        }
        .sui-facet__title {
          text-align: right;
        }
        .sui-sorting {
          direction: rtl;
        }
        .sui-paging-info {
          direction: rtl;
        }
        .sui-results-per-page {
          direction: rtl;
        }
        .sui-paging {
          direction: rtl;
        }
        .sui-result__meta {
          direction: rtl;
          text-align: right;
        }
        .sui-result__tags {
          direction: rtl;
          text-align: right;
        }
      `;
      document.head.appendChild(style);
    }
  }, [currentLanguage]);

  return (
    <div style={appStyle}>
      <LanguageToggle
        currentLanguage={currentLanguage}
        onLanguageChange={setCurrentLanguage}
        direction={INDEX_CONFIGS[currentLanguage]?.direction || 'ltr'}
      />
      <IndexInfo
        currentLanguage={currentLanguage}
        indexSuffix={indexSuffix}
      />
      <SearchApp
        language={currentLanguage}
        indexSuffix={indexSuffix}
      />
    </div>
  );
}
