GET /search_hyperai_cn/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "bool": {
            "should": [
              { "match": { "title": { "query": "训练成本人工砍半", "boost": 10,"minimum_should_match": "75%" } } },
              { "match": { "tags": { "query": "训练成本人工砍半", "boost": 3,"minimum_should_match": "95%"  } } },
              { "match": { "abstract": { "query": "训练成本人工砍半", "boost": 8 ,"minimum_should_match": "100%" } } },
              { "match": { "content": { "query": "训练成本人工砍半", "boost": 2 ,"minimum_should_match": "100%" } } },
              { "match": { "full_text": { "query": "训练成本人工砍半", "boost": 1 ,"minimum_should_match": "100%" } } }
            ],
            "minimum_should_match": 1
          }
        }
      ],
      "should": [
        { "match": { "title": { "query": "训练成本人工砍半", "boost": 20 ,"operator": "and"} } },
        { "match": { "tags": { "query": "训练成本人工砍半", "boost": 15,"operator": "and"} } },
        { "match": { "abstract": { "query": "训练成本人工砍半", "boost": 10 ,"operator": "and"} } },
        {
          "function_score": {
            "query": { "match_all": {} },
            "functions": [
              {
                "filter": { "term": { "title.keyword": "训练成本人工砍半" } },
                "weight": 30
              },
              {
                "filter": { "term": { "tags.keyword": "训练成本人工砍半" } },
                "weight": 10
              }
            ],
            "boost_mode": "sum"
          }
        }
      ],
      "minimum_should_match": 0
    }
  },
  "sort": [
    {
      "_script": {
        "type": "number",
        "script": {
          "source": """
            if (doc['section'].size() > 0) {
              String section = doc['section'].value;
              if (section == '数据集') return 8;
              if (section == '教程') return 6;
              if (section == '论文') return 2;
            }
            return 1;
          """,
          "lang": "painless"
        },
        "order": "desc"
      }
    },
    { "_score": "desc" },
    { "createtime": "desc" }
  ],
  "size": 6,
  "aggs": {
    "section_buckets": {
      "terms": {
        "field": "section",
        "order": { "_key": "desc" },
        "size": 4
      },
      "aggs": {
        "top_docs": {
          "top_hits": {
            "size": 6,
            "_source": ["id", "section", "title", "tags", "abstract", "createtime"]
          }
        }
      }
    }
  }
}
