import type { TranslatedTag } from '@/types'

// Generated from https://orion.hyper.ai/en/wp-json/hyperai/v1/tags?post_type=tutorials&per_page=100
export const tagsTutorials: TranslatedTag[] = [
  {
    图像生成: {
      cn: '图像生成',
      en: 'Image Generation',
      ja: '画像生成',
      kr: '이미지 생성',
      ar: 'توليد الصور',
      fr: "Génération d'images",
      de: 'Bilderzeugung',
    },
  },
  {
    机器视觉: {
      cn: '机器视觉',
      en: 'Computer Vision',
      ja: 'コンピュータビジョン',
      kr: '컴퓨터 비전',
      ar: 'رؤية الحاسوب',
      fr: 'Vision par ordinateur',
      de: 'Maschinelles Sehen',
    },
  },
  {
    'AI for Science': {
      cn: 'AI for Science',
      en: 'AI for Science',
      ja: '科学のためのAI',
      kr: '과학을 위한 AI',
      ar: 'الذكاء الاصطناعي للعلوم',
      fr: 'IA pour la science',
      de: 'KI für die Wissenschaft',
    },
  },
  {
    蛋白质工程: {
      cn: '蛋白质工程',
      en: 'Protein Engineering',
      ja: 'タンパク質工学',
      kr: '단백질 공학',
      ar: 'هندسة البروتين',
      fr: 'Ingénierie des protéines',
      de: 'Protein-Engineering',
    },
  },
  {
    Qwen: {
      cn: 'Qwen',
      en: 'Qwen',
      ja: 'Qwen',
      kr: 'Qwen',
      ar: 'Qwen',
      fr: 'Qwen',
      de: 'Qwen',
    },
  },
  {
    多模态: {
      cn: '多模态',
      en: 'Multimodal',
      ja: 'マルチモーダル',
      kr: '멀티모달',
      ar: 'متعدد الوسائط',
      fr: 'Multimodal',
      de: 'Multimodal',
    },
  },
  {
    yolo: {
      cn: 'yolo',
      en: 'YOLO',
      ja: 'YOLO',
      kr: 'YOLO',
      ar: 'YOLO',
      fr: 'YOLO',
      de: 'YOLO',
    },
  },
  {
    图像分割: {
      cn: '图像分割',
      en: 'Image Segmentation',
      ja: '画像分割',
      kr: '이미지 분할',
      ar: 'تجزئة الصور',
      fr: "Segmentation d'image",
      de: 'Bildsegmentierung',
    },
  },
  {
    大模型: {
      cn: '大模型',
      en: 'Large Language Models',
      ja: '大規模言語モデル',
      kr: '대규모 언어 모델',
      ar: 'نماذج اللغة الكبيرة',
      fr: 'Grands modèles de langage',
      de: 'Große Sprachmodelle',
    },
  },
  {
    Matlab: {
      cn: 'Matlab',
      en: 'Matlab',
      ja: 'Matlab',
      kr: 'Matlab',
      ar: 'Matlab',
      fr: 'Matlab',
      de: 'Matlab',
    },
  },
  {
    高性能计算: {
      cn: '高性能计算',
      en: 'High Performance Computing',
      ja: '高性能コンピューティング',
      kr: '고성능 컴퓨팅',
      ar: 'الحوسبة عالية الأداء',
      fr: 'Calcul haute performance',
      de: 'Hochleistungsrechnen',
    },
  },
  {
    '图生 3D': {
      cn: '图生 3D',
      en: 'Image-to-3D',
      ja: '画像から3D',
      kr: '이미지에서 3D로',
      ar: 'تحويل الصور إلى 3D',
      fr: 'Image vers 3D',
      de: 'Bild-zu-3D',
    },
  },
  {
    视频生成: {
      cn: '视频生成',
      en: 'Video Generation',
      ja: '動画生成',
      kr: '비디오 생성',
      ar: 'توليد الفيديو',
      fr: 'Génération de vidéo',
      de: 'Videogenerierung',
    },
  },
  {
    声音克隆: {
      cn: '声音克隆',
      en: 'Voice Cloning',
      ja: 'ボイスクローン',
      kr: '음성 복제',
      ar: 'استنساخ الصوت',
      fr: 'Clonage vocal',
      de: 'Stimmklonen',
    },
  },
  {
    对话生成: {
      cn: '对话生成',
      en: 'Dialogue Generation',
      ja: '対話生成',
      kr: '대화 생성',
      ar: 'توليد الحوار',
      fr: 'Génération de dialogue',
      de: 'Dialoggenerierung',
    },
  },
  {
    音乐生成: {
      cn: '音乐生成',
      en: 'Music Generation',
      ja: '音楽生成',
      kr: '음악 생성',
      ar: 'توليد الموسيقى',
      fr: 'Génération de musique',
      de: 'Musikgenerierung',
    },
  },
  {
    音频分类: {
      cn: '音频分类',
      en: 'Audio Classification',
      ja: '音声分類',
      kr: '오디오 분류',
      ar: 'تصنيف الصوت',
      fr: 'Classification audio',
      de: 'Audioklassifizierung',
    },
  },
  {
    DeepMind: {
      cn: 'DeepMind',
      en: 'DeepMind',
      ja: 'DeepMind',
      kr: 'DeepMind',
      ar: 'DeepMind',
      fr: 'DeepMind',
      de: 'DeepMind',
    },
  },
  {
    Google: {
      cn: 'Google',
      en: 'Google',
      ja: 'Google',
      kr: 'Google',
      ar: 'Google',
      fr: 'Google',
      de: 'Google',
    },
  },
  {
    Agent: {
      cn: 'Agent',
      en: 'Agent',
      ja: 'エージェント',
      kr: '에이전트',
      ar: 'وكيل',
      fr: 'Agent',
      de: 'Agent',
    },
  },
  {
    基因组学: {
      cn: '基因组学',
      en: 'Genomics',
      ja: 'ゲノミクス',
      kr: '게놈학',
      ar: 'علم الجينوم',
      fr: 'Génomique',
      de: 'Genomik',
    },
  },
  {
    VASP: {
      cn: 'VASP',
      en: 'VASP',
      ja: 'VASP',
      kr: 'VASP',
      ar: 'VASP',
      fr: 'VASP',
      de: 'VASP',
    },
  },
  {
    目标检测: {
      cn: '目标检测',
      en: 'Object Detection',
      ja: '物体検出',
      kr: '물체 검출',
      ar: 'كشف الأجسام',
      fr: "Détection d'objets",
      de: 'Objekterkennung',
    },
  },
  {
    图生视频: {
      cn: '图生视频',
      en: 'Image-to-Video',
      ja: '画像から動画',
      kr: '이미지에서 비디오로',
      ar: 'تحويل الصور إلى فيديو',
      fr: 'Image vers vidéo',
      de: 'Bild-zu-Video',
    },
  },
  {
    视频分类: {
      cn: '视频分类',
      en: 'Video Classification',
      ja: '動画分類',
      kr: '비디오 분류',
      ar: 'تصنيف الفيديو',
      fr: 'Classification vidéo',
      de: 'Videoklassifizierung',
    },
  },
  {
    DeepSeek: {
      cn: 'DeepSeek',
      en: 'DeepSeek',
      ja: 'DeepSeek',
      kr: 'DeepSeek',
      ar: 'DeepSeek',
      fr: 'DeepSeek',
      de: 'DeepSeek',
    },
  },
  {
    vLLM: {
      cn: 'vLLM',
      en: 'vLLM',
      ja: 'vLLM',
      kr: 'vLLM',
      ar: 'vLLM',
      fr: 'vLLM',
      de: 'vLLM',
    },
  },
  {
    语音处理: {
      cn: '语音处理',
      en: 'Speech Processing',
      ja: '音声処理',
      kr: '음성 처리',
      ar: 'معالجة الكلام',
      fr: 'Traitement de la parole',
      de: 'Sprachverarbeitung',
    },
  },
  {
    语音识别: {
      cn: '语音识别',
      en: 'Speech Recognition',
      ja: '音声認識',
      kr: '음성 인식',
      ar: 'التعرف على الكلام',
      fr: 'Reconnaissance vocale',
      de: 'Spracherkennung',
    },
  },
  {
    微软: {
      cn: '微软',
      en: 'Microsoft',
      ja: 'マイクロソフト',
      kr: '마이크로소프트',
      ar: 'مايكروسوفت',
      fr: 'Microsoft',
      de: 'Microsoft',
    },
  },
  {
    材料化学: {
      cn: '材料化学',
      en: 'Materials Chemistry',
      ja: '材料化学',
      kr: '재료 화학',
      ar: 'كيمياء المواد',
      fr: 'Chimie des matériaux',
      de: 'Materialchemie',
    },
  },
  {
    音频伪造: {
      cn: '音频伪造',
      en: 'Audio Forgery',
      ja: '音声偽造',
      kr: '음성 위조',
      ar: 'تزوير الصوت',
      fr: 'Contrefaçon audio',
      de: 'Audiofälschung',
    },
  },
  {
    NVIDIA: {
      cn: 'NVIDIA',
      en: 'NVIDIA',
      ja: 'NVIDIA',
      kr: 'NVIDIA',
      ar: 'NVIDIA',
      fr: 'NVIDIA',
      de: 'NVIDIA',
    },
  },
  {
    世界模型: {
      cn: '世界模型',
      en: 'World Model',
      ja: 'ワールドモデル',
      kr: '세계 모델',
      ar: 'نموذج العالم',
      fr: 'Modèle du monde',
      de: 'Weltmodell',
    },
  },
  {
    lammps: {
      cn: 'lammps',
      en: 'LAMMPS',
      ja: 'LAMMPS',
      kr: 'LAMMPS',
      ar: 'LAMMPS',
      fr: 'LAMMPS',
      de: 'LAMMPS',
    },
  },
  {
    视频合成: {
      cn: '视频合成',
      en: 'Video Synthesis',
      ja: '動画合成',
      kr: '비디오 합성',
      ar: 'توليف الفيديو',
      fr: 'Synthèse vidéo',
      de: 'Videosynthese',
    },
  },
  {
    实时生成: {
      cn: '实时生成',
      en: 'Real-time Generation',
      ja: 'リアルタイム生成',
      kr: '실시간 생성',
      ar: 'التوليد في الوقت الفعلي',
      fr: 'Génération en temps réel',
      de: 'Echtzeit-Generierung',
    },
  },
  {
    语音合成: {
      cn: '语音合成',
      en: 'Speech Synthesis',
      ja: '音声合成',
      kr: '음성 합성',
      ar: 'توليف الكلام',
      fr: 'Synthèse vocale',
      de: 'Sprachsynthese',
    },
  },
  {
    视频处理: {
      cn: '视频处理',
      en: 'Video Processing',
      ja: '動画処理',
      kr: '비디오 처리',
      ar: 'معالجة الفيديو',
      fr: 'Traitement vidéo',
      de: 'Videoverarbeitung',
    },
  },
  {
    视频字幕: {
      cn: '视频字幕',
      en: 'Video Captioning',
      ja: '動画字幕',
      kr: '비디오 자막',
      ar: 'ترجمة الفيديو',
      fr: 'Sous-titrage vidéo',
      de: 'Videountertitelung',
    },
  },
  {
    '3D 模型': {
      cn: '3D 模型',
      en: '3D Model',
      ja: '3Dモデル',
      kr: '3D 모델',
      ar: 'نموذج ثلاثي الأبعاد',
      fr: 'Modèle 3D',
      de: '3D-Modell',
    },
  },
  {
    三维姿态估计: {
      cn: '三维姿态估计',
      en: '3D Pose Estimation',
      ja: '3Dポーズ推定',
      kr: '3D 포즈 추정',
      ar: 'تقدير الوضع ثلاثي الأبعاد',
      fr: 'Estimation de pose 3D',
      de: '3D-Posenschätzung',
    },
  },
  {
    文本分类: {
      cn: '文本分类',
      en: 'Text Classification',
      ja: 'テキスト分類',
      kr: '텍스트 분류',
      ar: 'تصنيف النص',
      fr: 'Classification de texte',
      de: 'Textklassifizierung',
    },
  },
  {
    文本处理: {
      cn: '文本处理',
      en: 'Text Processing',
      ja: 'テキスト処理',
      kr: '텍스트 처리',
      ar: 'معالجة النص',
      fr: 'Traitement de texte',
      de: 'Textverarbeitung',
    },
  },
  {
    视频理解: {
      cn: '视频理解',
      en: 'Video Understanding',
      ja: '動画理解',
      kr: '비디오 이해',
      ar: 'فهم الفيديو',
      fr: 'Compréhension vidéo',
      de: 'Videoverständnis',
    },
  },
  {
    视频问答: {
      cn: '视频问答',
      en: 'Video Question Answering',
      ja: '動画質問応答',
      kr: '비디오 질문 답변',
      ar: 'الإجابة على أسئلة الفيديو',
      fr: 'Question-réponse vidéo',
      de: 'Video-Frage-Antwort',
    },
  },
  {
    图像分类: {
      cn: '图像分类',
      en: 'Image Classification',
      ja: '画像分類',
      kr: '이미지 분류',
      ar: 'تصنيف الصور',
      fr: "Classification d'images",
      de: 'Bildklassifizierung',
    },
  },
  {
    图像理解: {
      cn: '图像理解',
      en: 'Image Understanding',
      ja: '画像理解',
      kr: '이미지 이해',
      ar: 'فهم الصور',
      fr: "Compréhension d'images",
      de: 'Bildverständnis',
    },
  },
  {
    自然语言处理: {
      cn: '自然语言处理',
      en: 'Natural Language Processing',
      ja: '自然言語処理',
      kr: '자연어 처리',
      ar: 'معالجة اللغة الطبيعية',
      fr: 'Traitement du langage naturel',
      de: 'Natürliche Sprachverarbeitung',
    },
  },
  {
    Sora: {
      cn: 'Sora',
      en: 'Sora',
      ja: 'Sora',
      kr: 'Sora',
      ar: 'Sora',
      fr: 'Sora',
      de: 'Sora',
    },
  },
  {
    机器学习: {
      cn: '机器学习',
      en: 'Machine Learning',
      ja: '機械学習',
      kr: '기계 학습',
      ar: 'تعلم الآلة',
      fr: 'Apprentissage automatique',
      de: 'Maschinelles Lernen',
    },
  },
  {
    文生视频: {
      cn: '文生视频',
      en: 'Text-to-Video',
      ja: 'テキストから動画',
      kr: '텍스트에서 비디오로',
      ar: 'تحويل النص إلى فيديو',
      fr: 'Texte vers vidéo',
      de: 'Text-zu-Video',
    },
  },
  {
    FLUX: {
      cn: 'FLUX',
      en: 'FLUX',
      ja: 'FLUX',
      kr: 'FLUX',
      ar: 'FLUX',
      fr: 'FLUX',
      de: 'FLUX',
    },
  },
  {
    文生图: {
      cn: '文生图',
      en: 'Text-to-Image',
      ja: 'テキストから画像',
      kr: '텍스트에서 이미지로',
      ar: 'تحويل النص إلى صورة',
      fr: 'Texte vers image',
      de: 'Text-zu-Bild',
    },
  },
  {
    Triton: {
      cn: 'Triton',
      en: 'Triton',
      ja: 'Triton',
      kr: 'Triton',
      ar: 'Triton',
      fr: 'Triton',
      de: 'Triton',
    },
  },
  {
    编译器: {
      cn: '编译器',
      en: 'Compiler',
      ja: 'コンパイラ',
      kr: '컴파일러',
      ar: 'المترجم',
      fr: 'Compilateur',
      de: 'Compiler',
    },
  },
  {
    交互: {
      cn: '交互',
      en: 'Interaction',
      ja: 'インタラクション',
      kr: '상호 작용',
      ar: 'التفاعل',
      fr: 'Interaction',
      de: 'Interaktion',
    },
  },
  {
    GROMACS: {
      cn: 'GROMACS',
      en: 'GROMACS',
      ja: 'GROMACS',
      kr: 'GROMACS',
      ar: 'GROMACS',
      fr: 'GROMACS',
      de: 'GROMACS',
    },
  },
  {
    '3D 建模': {
      cn: '3D 建模',
      en: '3D Modeling',
      ja: '3Dモデリング',
      kr: '3D 모델링',
      ar: 'النمذجة ثلاثية الأبعاد',
      fr: 'Modélisation 3D',
      de: '3D-Modellierung',
    },
  },
  {
    三维重建: {
      cn: '三维重建',
      en: '3D Reconstruction',
      ja: '3D再構築',
      kr: '3D 재구성',
      ar: 'إعادة البناء ثلاثية الأبعاد',
      fr: 'Reconstruction 3D',
      de: '3D-Rekonstruktion',
    },
  },
  {
    图像处理: {
      cn: '图像处理',
      en: 'Image Processing',
      ja: '画像処理',
      kr: '이미지 처리',
      ar: 'معالجة الصور',
      fr: "Traitement d'image",
      de: 'Bildverarbeitung',
    },
  },
  {
    抠图: {
      cn: '抠图',
      en: 'Image Matting',
      ja: '画像マッティング',
      kr: '이미지 마트링',
      ar: 'اقتطاع الصور',
      fr: "Détourage d'image",
      de: 'Bildfreistellen',
    },
  },
  {
    AlphaFold: {
      cn: 'AlphaFold',
      en: 'AlphaFold',
      ja: 'AlphaFold',
      kr: 'AlphaFold',
      ar: 'AlphaFold',
      fr: 'AlphaFold',
      de: 'AlphaFold',
    },
  },
  {
    蛋白质: {
      cn: '蛋白质',
      en: 'Protein',
      ja: 'タンパク質',
      kr: '단백질',
      ar: 'البروتين',
      fr: 'Protéine',
      de: 'Protein',
    },
  },
  {
    蛋白质语言模型: {
      cn: '蛋白质语言模型',
      en: 'Protein Language Model',
      ja: 'タンパク質言語モデル',
      kr: '단백질 언어 모델',
      ar: 'نموذج لغة البروتين',
      fr: 'Modèle de langage protéique',
      de: 'Protein-Sprachmodell',
    },
  },
  {
    'text-to-3D': {
      cn: 'text-to-3D',
      en: 'Text-to-3D',
      ja: 'テキストから3D',
      kr: '텍스트에서 3D로',
      ar: 'تحويل النص إلى 3D',
      fr: 'Texte vers 3D',
      de: 'Text-zu-3D',
    },
  },
  {
    腾讯: {
      cn: '腾讯',
      en: 'Tencent',
      ja: 'テンセント',
      kr: '텐센트',
      ar: 'تنسنت',
      fr: 'Tencent',
      de: 'Tencent',
    },
  },
  {
    DNA: {
      cn: 'DNA',
      en: 'DNA',
      ja: 'DNA',
      kr: 'DNA',
      ar: 'الحمض النووي',
      fr: 'ADN',
      de: 'DNA',
    },
  },
  {
    'AI 编译器': {
      cn: 'AI 编译器',
      en: 'AI Compiler',
      ja: 'AIコンパイラ',
      kr: 'AI 컴파일러',
      ar: 'مترجم الذكاء الاصطناعي',
      fr: 'Compilateur IA',
      de: 'KI-Compiler',
    },
  },
  {
    MistralAI: {
      cn: 'MistralAI',
      en: 'MistralAI',
      ja: 'MistralAI',
      kr: 'MistralAI',
      ar: 'MistralAI',
      fr: 'MistralAI',
      de: 'MistralAI',
    },
  },
  {
    边缘计算: {
      cn: '边缘计算',
      en: 'Edge Computing',
      ja: 'エッジコンピューティング',
      kr: '에지 컴퓨팅',
      ar: 'الحوسبة الطرفية',
      fr: 'Informatique de périphérie',
      de: 'Edge Computing',
    },
  },
  {
    文本检测: {
      cn: '文本检测',
      en: 'Text Detection',
      ja: 'テキスト検出',
      kr: '텍스트 검출',
      ar: 'كشف النص',
      fr: 'Détection de texte',
      de: 'Texterkennung',
    },
  },
  {
    文本生成: {
      cn: '文本生成',
      en: 'Text Generation',
      ja: 'テキスト生成',
      kr: '텍스트 생성',
      ar: 'توليد النص',
      fr: 'Génération de texte',
      de: 'Textgenerierung',
    },
  },
  {
    OCR: {
      cn: 'OCR',
      en: 'OCR',
      ja: 'OCR',
      kr: 'OCR',
      ar: 'التعرف الضوئي على الحروف',
      fr: 'OCR',
      de: 'OCR',
    },
  },
  {
    文本识别: {
      cn: '文本识别',
      en: 'Text Recognition',
      ja: 'テキスト認識',
      kr: '텍스트 인식',
      ar: 'التعرف على النص',
      fr: 'Reconnaissance de texte',
      de: 'Texterkennung',
    },
  },
  {
    合成图像: {
      cn: '合成图像',
      en: 'Synthetic Image',
      ja: '合成画像',
      kr: '합성 이미지',
      ar: 'صورة اصطناعية',
      fr: 'Image synthétique',
      de: 'Synthetisches Bild',
    },
  },
  {
    视觉位置识别: {
      cn: '视觉位置识别',
      en: 'Visual Place Recognition',
      ja: '視覚的場所認識',
      kr: '시각적 장소 인식',
      ar: 'التعرف على المكان البصري',
      fr: 'Reconnaissance visuelle de lieu',
      de: 'Visuelle Ortserkennung',
    },
  },
  {
    'Stable Diffusion': {
      cn: 'Stable Diffusion',
      en: 'Stable Diffusion',
      ja: 'Stable Diffusion',
      kr: 'Stable Diffusion',
      ar: 'Stable Diffusion',
      fr: 'Stable Diffusion',
      de: 'Stable Diffusion',
    },
  },
  {
    单目深度估计: {
      cn: '单目深度估计',
      en: 'Monocular Depth Estimation',
      ja: '単眼深度推定',
      kr: '단안 깊이 추정',
      ar: 'تقدير العمق أحادي العين',
      fr: 'Estimation de profondeur monoculaire',
      de: 'Monokulare Tiefenschätzung',
    },
  },
  {
    深度估计: {
      cn: '深度估计',
      en: 'Depth Estimation',
      ja: '深度推定',
      kr: '깊이 추정',
      ar: 'تقدير العمق',
      fr: 'Estimation de profondeur',
      de: 'Tiefenschätzung',
    },
  },
  {
    图像修复: {
      cn: '图像修复',
      en: 'Image Inpainting',
      ja: '画像修復',
      kr: '이미지 복원',
      ar: 'ترميم الصور',
      fr: "Inpainting d'image",
      de: 'Bildretusche',
    },
  },
  {
    视频修复: {
      cn: '视频修复',
      en: 'Video Inpainting',
      ja: '動画修復',
      kr: '비디오 복원',
      ar: 'ترميم الفيديو',
      fr: 'Inpainting vidéo',
      de: 'Videoretusche',
    },
  },
  {
    医学应用: {
      cn: '医学应用',
      en: 'Medical Applications',
      ja: '医療応用',
      kr: '의료 응용',
      ar: 'تطبيقات طبية',
      fr: 'Applications médicales',
      de: 'Medizinische Anwendungen',
    },
  },
  {
    医学诊断: {
      cn: '医学诊断',
      en: 'Medical Diagnosis',
      ja: '医療診断',
      kr: '의료 진단',
      ar: 'التشخيص الطبي',
      fr: 'Diagnostic médical',
      de: 'Medizinische Diagnose',
    },
  },
  {
    医学: {
      cn: '医学',
      en: 'Medicine',
      ja: '医学',
      kr: '의학',
      ar: 'الطب',
      fr: 'Médecine',
      de: 'Medizin',
    },
  },
  {
    Meta: {
      cn: 'Meta',
      en: 'Meta',
      ja: 'Meta',
      kr: 'Meta',
      ar: 'Meta',
      fr: 'Meta',
      de: 'Meta',
    },
  },
  {
    扩散模型: {
      cn: '扩散模型',
      en: 'Diffusion Model',
      ja: '拡散モデル',
      kr: '확산 모델',
      ar: 'نموذج الانتشار',
      fr: 'Modèle de diffusion',
      de: 'Diffusionsmodell',
    },
  },
  {
    数学: {
      cn: '数学',
      en: 'Mathematics',
      ja: '数学',
      kr: '수학',
      ar: 'الرياضيات',
      fr: 'Mathématiques',
      de: 'Mathematik',
    },
  },
  {
    数学问题: {
      cn: '数学问题',
      en: 'Mathematical Problems',
      ja: '数学の問題',
      kr: '수학적 문제',
      ar: 'المسائل الرياضية',
      fr: 'Problèmes mathématiques',
      de: 'Mathematische Probleme',
    },
  },
  {
    打工人神器: {
      cn: '打工人神器',
      en: "Worker's Essential Tool",
      ja: 'ワーカーの必須ツール',
      kr: '일꾼의 필수 도구',
      ar: 'أداة العمال الأساسية',
      fr: 'Outil essentiel du travailleur',
      de: 'Unverzichtbares Arbeitswerkzeug',
    },
  },
  {
    AI数字人: {
      cn: 'AI数字人',
      en: 'AI Digital Human',
      ja: 'AIデジタルヒューマン',
      kr: 'AI 디지털 인간',
      ar: 'الإنسان الرقمي للذكاء الاصطناعي',
      fr: 'Humain numérique IA',
      de: 'KI-Digitaler Mensch',
    },
  },
  {
    ComfyUI: {
      cn: 'ComfyUI',
      en: 'ComfyUI',
      ja: 'ComfyUI',
      kr: 'ComfyUI',
      ar: 'ComfyUI',
      fr: 'ComfyUI',
      de: 'ComfyUI',
    },
  },
  {
    图像编辑: {
      cn: '图像编辑',
      en: 'Image Editing',
      ja: '画像編集',
      kr: '이미지 편집',
      ar: 'تحرير الصور',
      fr: "Édition d'Image",
      de: 'Bildbearbeitung',
    },
  },
  {
    数学推理: {
      cn: '数学推理',
      en: 'Mathematical Reasoning',
      ja: '数学的推論',
      kr: '수학적 추론',
      ar: 'الاستدلال الرياضي',
      fr: 'Raisonnement Mathématique',
      de: 'Mathematisches Reasoning',
    },
  },
  {
    音频生成: {
      cn: '音频生成',
      en: 'Audio Generation',
      ja: '音声生成',
      kr: '오디오 생성',
      ar: 'توليد الصوت',
      fr: 'Génération Audio',
      de: 'Audiogenerierung',
    },
  },
  {
    TTS: {
      cn: 'TTS',
      en: 'TTS',
      ja: 'TTS',
      kr: 'TTS',
      ar: 'تحويل النص إلى كلام',
      fr: 'TTS',
      de: 'TTS',
    },
  },
  {
    音频识别: {
      cn: '音频识别',
      en: 'Audio Recognition',
      ja: '音声認識',
      kr: '오디오 인식',
      ar: 'التعرف على الصوت',
      fr: 'Reconnaissance Audio',
      de: 'Audioerkennung',
    },
  },
  {
    编码: {
      cn: '编码',
      en: 'Coding',
      ja: 'コーディング',
      kr: '코딩',
      ar: 'البرمجة',
      fr: 'Codage',
      de: 'Programmierung',
    },
  },
  {
    LangChain: {
      cn: 'LangChain',
      en: 'LangChain',
      ja: 'LangChain',
      kr: 'LangChain',
      ar: 'LangChain',
      fr: 'LangChain',
      de: 'LangChain',
    },
  },
  {
    Abaqus: {
      cn: 'Abaqus',
      en: 'Abaqus',
      ja: 'Abaqus',
      kr: 'Abaqus',
      ar: 'Abaqus',
      fr: 'Abaqus',
      de: 'Abaqus',
    },
  },
  {
    超像素图像分类: {
      cn: '超像素图像分类',
      en: 'Superpixel Image Classification',
      ja: '超画素画像分類',
      kr: '슈퍼픽셀 이미지 분류',
      ar: 'تصنيف صور السوبر بكسل',
      fr: "Classification d'Images Superpixel",
      de: 'Superpixel-Bildklassifizierung',
    },
  },
  {
    计算机视觉: {
      cn: '计算机视觉',
      en: 'Computer Vision',
      ja: 'コンピュータビジョン',
      kr: '컴퓨터 비전',
      ar: 'رؤية الكمبيوتر',
      fr: 'Vision par Ordinateur',
      de: 'Computer Vision',
    },
  },
  {
    光学识别: {
      cn: '光学识别',
      en: 'Optical Recognition',
      ja: '光学認識',
      kr: '광학 인식',
      ar: 'التعرف البصري',
      fr: 'Reconnaissance Optique',
      de: 'Optische Erkennung',
    },
  },
  {
    遥感: {
      cn: '遥感',
      en: 'Remote Sensing',
      ja: 'リモートセンシング',
      kr: '원격 감지',
      ar: 'الاستشعار عن بعد',
      fr: 'Télédétection',
      de: 'Fernerkundung',
    },
  },
  {
    地理信息科学: {
      cn: '地理信息科学',
      en: 'Geographic Information Science',
      ja: '地理情報科学',
      kr: '지리 정보 과학',
      ar: 'علوم المعلومات الجغرافية',
      fr: "Science de l'Information Géographique",
      de: 'Geographische Informationswissenschaft',
    },
  },
  {
    推理模型: {
      cn: '推理模型',
      en: 'Inference Model',
      ja: '推論モデル',
      kr: '추론 모델',
      ar: 'نموذج الاستدلال',
      fr: "Modèle d'Inférence",
      de: 'Inferenzmodell',
    },
  },
  {
    字节跳动: {
      cn: '字节跳动',
      en: 'ByteDance',
      ja: 'バイトダンス',
      kr: '바이트댄스',
      ar: 'بايت دانس',
      fr: 'ByteDance',
      de: 'ByteDance',
    },
  },
  {
    RAG: {
      cn: 'RAG',
      en: 'RAG',
      ja: 'RAG',
      kr: 'RAG',
      ar: 'RAG',
      fr: 'RAG',
      de: 'RAG',
    },
  },
  {
    CVPR: {
      cn: 'CVPR',
      en: 'CVPR',
      ja: 'CVPR',
      kr: 'CVPR',
      ar: 'CVPR',
      fr: 'CVPR',
      de: 'CVPR',
    },
  },
  {
    '3D 生成': {
      cn: '3D 生成',
      en: '3D Generation',
      ja: '3D生成',
      kr: '3D 생성',
      ar: 'توليد ثلاثي الأبعاد',
      fr: 'Génération 3D',
      de: '3D-Generierung',
    },
  },
  {
    图像识别: {
      cn: '图像识别',
      en: 'Image Recognition',
      ja: '画像認識',
      kr: '이미지 인식',
      ar: 'التعرف على الصور',
      fr: "Reconnaissance d'image",
      de: 'Bilderkennung',
    },
  },
  {
    图像增强: {
      cn: '图像增强',
      en: 'Image Enhancement',
      ja: '画像強調',
      kr: '이미지 향상',
      ar: 'تحسين الصورة',
      fr: "Amélioration d'image",
      de: 'Bildverbesserung',
    },
  },
  {
    超分辨率: {
      cn: '超分辨率',
      en: 'Super Resolution',
      ja: '超解像',
      kr: '초해상도',
      ar: 'دقة فائقة',
      fr: 'Super résolution',
      de: 'Superauflösung',
    },
  },
  {
    代码生成: {
      cn: '代码生成',
      en: 'Code Generation',
      ja: 'コード生成',
      kr: '코드 생성',
      ar: 'توليد الكود',
      fr: 'Génération de code',
      de: 'Codegenerierung',
    },
  },
  {
    编程语言: {
      cn: '编程语言',
      en: 'Programming Language',
      ja: 'プログラミング言語',
      kr: '프로그래밍 언어',
      ar: 'لغة البرمجة',
      fr: 'Langage de programmation',
      de: 'Programmiersprache',
    },
  },
  {
    图像描述: {
      cn: '图像描述',
      en: 'Image Captioning',
      ja: '画像キャプション',
      kr: '이미지 캡션',
      ar: 'وصف الصور',
      fr: "Légende d'image",
      de: 'Bildbeschreibung',
    },
  },
  {
    视频类别: {
      cn: '视频类别',
      en: 'Video Category',
      ja: 'ビデオカテゴリ',
      kr: '비디오 카테고리',
      ar: 'فئة الفيديو',
      fr: 'Catégorie vidéo',
      de: 'Videokategorie',
    },
  },
  {
    HPC: {
      cn: 'HPC',
      en: 'HPC',
      ja: 'HPC',
      kr: 'HPC',
      ar: 'HPC',
      fr: 'HPC',
      de: 'HPC',
    },
  },
  {
    语音情绪识别: {
      cn: '语音情绪识别',
      en: 'Speech Emotion Recognition',
      ja: '音声感情認識',
      kr: '음성 감정 인식',
      ar: 'التعرف على المشاعر الصوتية',
      fr: 'Reconnaissance des émotions vocales',
      de: 'Spracherkennung von Emotionen',
    },
  },
  {
    气象学: {
      cn: '气象学',
      en: 'Meteorology',
      ja: '気象学',
      kr: '기상학',
      ar: 'علم الأرصاد الجوية',
      fr: 'Météorologie',
      de: 'Meteorologie',
    },
  },
  {
    视觉对话: {
      cn: '视觉对话',
      en: 'Visual Dialogue',
      ja: 'ビジュアルダイアログ',
      kr: '시각적 대화',
      ar: 'الحوار البصري',
      fr: 'Dialogue visuel',
      de: 'Visueller Dialog',
    },
  },
]
