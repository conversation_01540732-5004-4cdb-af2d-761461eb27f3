import type { TranslatedTag } from '@/types'

// Generated from https://orion.hyper.ai/en/wp-json/hyperai/v1/tags?post_type=events&per_page=100
export const tagsEvents: TranslatedTag[] = [
  {
    计算机网络: {
      cn: '计算机网络',
      en: 'Computer Networks',
      ja: 'コンピュータネットワーク',
      kr: '컴퓨터 네트워크',
      ar: 'شبكات الحاسوب',
      fr: 'Réseaux informatiques',
      de: 'Computernetzwerke',
    },
  },
  {
    计算机网络与无线通信: {
      cn: '计算机网络与无线通信',
      en: 'Computer Networks and Wireless Communications',
      ja: 'コンピュータネットワークと無線通信',
      kr: '컴퓨터 네트워크 및 무선 통신',
      ar: 'شبكات الحاسوب والاتصالات اللاسلكية',
      fr: 'Réseaux informatiques et communications sans fil',
      de: 'Computernetzwerke und drahtlose Kommunikation',
    },
  },
  {
    计算机图形学与多媒体: {
      cn: '计算机图形学与多媒体',
      en: 'Computer Graphics and Multimedia',
      ja: 'コンピュータグラフィックスとマルチメディア',
      kr: '컴퓨터 그래픽스 및 멀티미디어',
      ar: 'رسومات الحاسوب والوسائط المتعددة',
      fr: 'Infographie et multimédia',
      de: 'Computergrafik und Multimedia',
    },
  },
  {
    内容检索: {
      cn: '内容检索',
      en: 'Content Retrieval',
      ja: 'コンテンツ検索',
      kr: '콘텐츠 검색',
      ar: 'استرجاع المحتوى',
      fr: 'Récupération de contenu',
      de: 'Inhaltsabruf',
    },
  },
  {
    数据库: {
      cn: '数据库',
      en: 'Database',
      ja: 'データベース',
      kr: '데이터베이스',
      ar: 'قواعد البيانات',
      fr: 'Base de données',
      de: 'Datenbank',
    },
  },
  {
    数据挖掘: {
      cn: '数据挖掘',
      en: 'Data Mining',
      ja: 'データマイニング',
      kr: '데이터 마이닝',
      ar: 'التنقيب عن البيانات',
      fr: 'Exploration de données',
      de: 'Data Mining',
    },
  },
  {
    程序设计语言: {
      cn: '程序设计语言',
      en: 'Programming Languages',
      ja: 'プログラミング言語',
      kr: '프로그래밍 언어',
      ar: 'لغات البرمجة',
      fr: 'Langages de programmation',
      de: 'Programmiersprachen',
    },
  },
  {
    系统软件: {
      cn: '系统软件',
      en: 'System Software',
      ja: 'システムソフトウェア',
      kr: '시스템 소프트웨어',
      ar: 'برمجيات النظام',
      fr: 'Logiciel système',
      de: 'Systemsoftware',
    },
  },
  {
    软件工程: {
      cn: '软件工程',
      en: 'Software Engineering',
      ja: 'ソフトウェア工学',
      kr: '소프트웨어 공학',
      ar: 'هندسة البرمجيات',
      fr: 'Génie logiciel',
      de: 'Software Engineering',
    },
  },
  {
    人工智能: {
      cn: '人工智能',
      en: 'Artificial Intelligence',
      ja: '人工知能',
      kr: '인공지능',
      ar: 'الذكاء الاصطناعي',
      fr: 'Intelligence artificielle',
      de: 'Künstliche Intelligenz',
    },
  },
  {
    存储系统: {
      cn: '存储系统',
      en: 'Storage Systems',
      ja: 'ストレージシステム',
      kr: '스토리지 시스템',
      ar: 'أنظمة التخزين',
      fr: 'Systèmes de stockage',
      de: 'Speichersysteme',
    },
  },
  {
    并行与分布计算: {
      cn: '并行与分布计算',
      en: 'Parallel and Distributed Computing',
      ja: '並列分散コンピューティング',
      kr: '병렬 및 분산 컴퓨팅',
      ar: 'الحوسبة المتوازية والموزعة',
      fr: 'Calcul parallèle et distribué',
      de: 'Paralleles und verteiltes Rechnen',
    },
  },
  {
    计算机体系结构: {
      cn: '计算机体系结构',
      en: 'Computer Architecture',
      ja: 'コンピュータアーキテクチャ',
      kr: '컴퓨터 아키텍처',
      ar: 'هندسة الحاسوب',
      fr: 'Architecture informatique',
      de: 'Computerarchitektur',
    },
  },
  {
    机器人学: {
      cn: '机器人学',
      en: 'Robotics',
      ja: 'ロボット工学',
      kr: '로봇공학',
      ar: 'علم الروبوتات',
      fr: 'Robotique',
      de: 'Robotik',
    },
  },
  {
    网络与信息安全: {
      cn: '网络与信息安全',
      en: 'Network and Information Security',
      ja: 'ネットワークと情報セキュリティ',
      kr: '네트워크 및 정보 보안',
      ar: 'أمن الشبكات والمعلومات',
      fr: "Sécurité des réseaux et de l'information",
      de: 'Netzwerk- und Informationssicherheit',
    },
  },
  {
    自然语言处理: {
      cn: '自然语言处理',
      en: 'Natural Language Processing',
      ja: '自然言語処理',
      kr: '자연어 처리',
      ar: 'معالجة اللغة الطبيعية',
      fr: 'Traitement du langage naturel',
      de: 'Natürliche Sprachverarbeitung',
    },
  },
  {
    计算机视觉: {
      cn: '计算机视觉',
      en: 'Computer Vision',
      ja: 'コンピュータビジョン',
      kr: '컴퓨터 비전',
      ar: 'رؤية الحاسوب',
      fr: 'Vision par ordinateur',
      de: 'Maschinelles Sehen',
    },
  },
  {
    人机交互: {
      cn: '人机交互',
      en: 'Human-Computer Interaction',
      ja: 'ヒューマンコンピュータインタラクション',
      kr: '인간-컴퓨터 상호작용',
      ar: 'تفاعل الإنسان والحاسوب',
      fr: 'Interaction homme-machine',
      de: 'Mensch-Computer-Interaktion',
    },
  },
  {
    计算机安全: {
      cn: '计算机安全',
      en: 'Computer Security',
      ja: 'コンピュータセキュリティ',
      kr: '컴퓨터 보안',
      ar: 'أمن الحاسوب',
      fr: 'Sécurité informatique',
      de: 'Computersicherheit',
    },
  },
  {
    计算机科学理论: {
      cn: '计算机科学理论',
      en: 'Theoretical Computer Science',
      ja: '理論計算機科学',
      kr: '이론 컴퓨터 과학',
      ar: 'نظرية علوم الحاسوب',
      fr: 'Informatique théorique',
      de: 'Theoretische Informatik',
    },
  },
  {
    计算机理论科学: {
      cn: '计算机理论科学',
      en: 'Theoretical Computer Science',
      ja: '理論計算機科学',
      kr: '이론 컴퓨터 과학',
      ar: 'نظرية علوم الحاسوب',
      fr: 'Informatique théorique',
      de: 'Theoretische Informatik',
    },
  },
  {
    普适计算: {
      cn: '普适计算',
      en: 'Ubiquitous Computing',
      ja: 'ユビキタスコンピューティング',
      kr: '유비쿼터스 컴퓨팅',
      ar: 'الحوسبة في كل مكان',
      fr: 'Informatique ubiquitaire',
      de: 'Ubiquitäres Computing',
    },
  },
  {
    人机交互与普适计算: {
      cn: '人机交互与普适计算',
      en: 'Human-Computer Interaction and Ubiquitous Computing',
      ja: 'ヒューマンコンピュータインタラクションとユビキタスコンピューティング',
      kr: '인간-컴퓨터 상호작용 및 유비쿼터스 컴퓨팅',
      ar: 'تفاعل الإنسان والحاسوب والحوسبة في كل مكان',
      fr: 'Interaction homme-machine et informatique ubiquitaire',
      de: 'Mensch-Computer-Interaktion und ubiquitäres Computing',
    },
  },
]
