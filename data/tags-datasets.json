[{"term_id": 360, "name": "场景理解", "slug": "%e5%9c%ba%e6%99%af%e7%90%86%e8%a7%a3", "term_group": 0, "term_taxonomy_id": 360, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 22, "filter": "raw"}, {"term_id": 767, "name": "基准", "slug": "%e5%9f%ba%e5%87%86", "term_group": 0, "term_taxonomy_id": 767, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 298, "name": "深度学习", "slug": "%e6%b7%b1%e5%ba%a6%e5%ad%a6%e4%b9%a0", "term_group": 0, "term_taxonomy_id": 298, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 365, "filter": "raw"}, {"term_id": 313, "name": "图像识别", "slug": "%e5%9b%be%e5%83%8f%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 313, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 61, "filter": "raw"}, {"term_id": 337, "name": "自动驾驶", "slug": "%e8%87%aa%e5%8a%a8%e9%a9%be%e9%a9%b6", "term_group": 0, "term_taxonomy_id": 337, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 51, "filter": "raw"}, {"term_id": 1342, "name": "图像理解", "slug": "%e5%9b%be%e5%83%8f%e7%90%86%e8%a7%a3", "term_group": 0, "term_taxonomy_id": 1342, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 21, "filter": "raw"}, {"term_id": 865, "name": "多模态", "slug": "%e5%a4%9a%e6%a8%a1%e6%80%81", "term_group": 0, "term_taxonomy_id": 865, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 76, "filter": "raw"}, {"term_id": 332, "name": "文本分析", "slug": "%e6%96%87%e6%9c%ac%e5%88%86%e6%9e%90", "term_group": 0, "term_taxonomy_id": 332, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 533, "name": "视频生成", "slug": "%e8%a7%86%e9%a2%91%e7%94%9f%e6%88%90", "term_group": 0, "term_taxonomy_id": 533, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 27, "filter": "raw"}, {"term_id": 1125, "name": "OCR", "slug": "ocr", "term_group": 0, "term_taxonomy_id": 1125, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 15, "filter": "raw"}, {"term_id": 1261, "name": "文本识别", "slug": "%e6%96%87%e6%9c%ac%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 1261, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 18, "filter": "raw"}, {"term_id": 2252, "name": "生物", "slug": "%e7%94%9f%e7%89%a9", "term_group": 0, "term_taxonomy_id": 2252, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 2367, "name": "细胞", "slug": "%e7%bb%86%e8%83%9e", "term_group": 0, "term_taxonomy_id": 2367, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 2500, "name": "医疗", "slug": "%e5%8c%bb%e7%96%97", "term_group": 0, "term_taxonomy_id": 2500, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 12, "filter": "raw"}, {"term_id": 5006, "name": "空间转录组学", "slug": "%e7%a9%ba%e9%97%b4%e8%bd%ac%e5%bd%95%e7%bb%84%e5%ad%a6", "term_group": 0, "term_taxonomy_id": 5006, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 461, "name": "卫星图像", "slug": "%e5%8d%ab%e6%98%9f%e5%9b%be%e5%83%8f", "term_group": 0, "term_taxonomy_id": 461, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 9, "filter": "raw"}, {"term_id": 4729, "name": "地理信息", "slug": "%e5%9c%b0%e7%90%86%e4%bf%a1%e6%81%af", "term_group": 0, "term_taxonomy_id": 4729, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 307, "name": "机器学习", "slug": "%e6%9c%ba%e5%99%a8%e5%ad%a6%e4%b9%a0", "term_group": 0, "term_taxonomy_id": 307, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 322, "filter": "raw"}, {"term_id": 1924, "name": "高光谱图像分类", "slug": "%e9%ab%98%e5%85%89%e8%b0%b1%e5%9b%be%e5%83%8f%e5%88%86%e7%b1%bb", "term_group": 0, "term_taxonomy_id": 1924, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 8, "filter": "raw"}, {"term_id": 2666, "name": "视频分割", "slug": "%e8%a7%86%e9%a2%91%e5%88%86%e5%89%b2", "term_group": 0, "term_taxonomy_id": 2666, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 1759, "name": "视频处理", "slug": "%e8%a7%86%e9%a2%91%e5%a4%84%e7%90%86", "term_group": 0, "term_taxonomy_id": 1759, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 15, "filter": "raw"}, {"term_id": 5068, "name": "推理数据集", "slug": "%e6%8e%a8%e7%90%86%e6%95%b0%e6%8d%ae%e9%9b%86", "term_group": 0, "term_taxonomy_id": 5068, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 18, "filter": "raw"}, {"term_id": 2345, "name": "数学", "slug": "%e6%95%b0%e5%ad%a6", "term_group": 0, "term_taxonomy_id": 2345, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 21, "filter": "raw"}, {"term_id": 1215, "name": "3D 目标检测", "slug": "3d-%e7%9b%ae%e6%a0%87%e6%a3%80%e6%b5%8b", "term_group": 0, "term_taxonomy_id": 1215, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 15, "filter": "raw"}, {"term_id": 1355, "name": "RGB显著目标检测", "slug": "rgb%e6%98%be%e8%91%97%e7%9b%ae%e6%a0%87%e6%a3%80%e6%b5%8b", "term_group": 0, "term_taxonomy_id": 1355, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 362, "name": "工业场景", "slug": "%e5%b7%a5%e4%b8%9a%e5%9c%ba%e6%99%af", "term_group": 0, "term_taxonomy_id": 362, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 1506, "name": "异常检测", "slug": "%e5%bc%82%e5%b8%b8%e6%a3%80%e6%b5%8b", "term_group": 0, "term_taxonomy_id": 1506, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 1109, "name": "医学", "slug": "%e5%8c%bb%e5%ad%a6", "term_group": 0, "term_taxonomy_id": 1109, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 72, "filter": "raw"}, {"term_id": 323, "name": "文本生成", "slug": "%e6%96%87%e6%9c%ac%e7%94%9f%e6%88%90", "term_group": 0, "term_taxonomy_id": 323, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 20, "filter": "raw"}, {"term_id": 1397, "name": "问答数据集", "slug": "%e9%97%ae%e7%ad%94%e6%95%b0%e6%8d%ae%e9%9b%86", "term_group": 0, "term_taxonomy_id": 1397, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 26, "filter": "raw"}, {"term_id": 5152, "name": "法国", "slug": "%e6%b3%95%e5%9b%bd", "term_group": 0, "term_taxonomy_id": 5152, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 1, "filter": "raw"}, {"term_id": 367, "name": "遥感影像", "slug": "%e9%81%a5%e6%84%9f%e5%bd%b1%e5%83%8f", "term_group": 0, "term_taxonomy_id": 367, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 4678, "name": "多语言", "slug": "%e5%a4%9a%e8%af%ad%e8%a8%80", "term_group": 0, "term_taxonomy_id": 4678, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 1648, "name": "高校", "slug": "%e9%ab%98%e6%a0%a1", "term_group": 0, "term_taxonomy_id": 1648, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 11, "filter": "raw"}, {"term_id": 5149, "name": "RAG", "slug": "rag", "term_group": 0, "term_taxonomy_id": 5149, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 3, "filter": "raw"}, {"term_id": 2431, "name": "内容检索", "slug": "%e5%86%85%e5%ae%b9%e6%a3%80%e7%b4%a2", "term_group": 0, "term_taxonomy_id": 2431, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 9, "filter": "raw"}, {"term_id": 1942, "name": "文本理解", "slug": "%e6%96%87%e6%9c%ac%e7%90%86%e8%a7%a3", "term_group": 0, "term_taxonomy_id": 1942, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 2145, "name": "化学", "slug": "%e5%8c%96%e5%ad%a6", "term_group": 0, "term_taxonomy_id": 2145, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 14, "filter": "raw"}, {"term_id": 1539, "name": "医学诊断", "slug": "%e5%8c%bb%e5%ad%a6%e8%af%8a%e6%96%ad", "term_group": 0, "term_taxonomy_id": 1539, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 17, "filter": "raw"}, {"term_id": 1155, "name": "图文数据集", "slug": "%e5%9b%be%e6%96%87%e6%95%b0%e6%8d%ae%e9%9b%86", "term_group": 0, "term_taxonomy_id": 1155, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 584, "name": "统计型数据", "slug": "%e7%bb%9f%e8%ae%a1%e5%9e%8b%e6%95%b0%e6%8d%ae", "term_group": 0, "term_taxonomy_id": 584, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 55, "filter": "raw"}, {"term_id": 2359, "name": "数学推理", "slug": "%e6%95%b0%e5%ad%a6%e6%8e%a8%e7%90%86", "term_group": 0, "term_taxonomy_id": 2359, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 27, "filter": "raw"}, {"term_id": 2621, "name": "评测基准", "slug": "%e8%af%84%e6%b5%8b%e5%9f%ba%e5%87%86", "term_group": 0, "term_taxonomy_id": 2621, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 12, "filter": "raw"}, {"term_id": 492, "name": "音频分析", "slug": "%e9%9f%b3%e9%a2%91%e5%88%86%e6%9e%90", "term_group": 0, "term_taxonomy_id": 492, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 17, "filter": "raw"}, {"term_id": 2570, "name": "法律领域", "slug": "%e6%b3%95%e5%be%8b%e9%a2%86%e5%9f%9f", "term_group": 0, "term_taxonomy_id": 2570, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 2743, "name": "基准测试", "slug": "%e5%9f%ba%e5%87%86%e6%b5%8b%e8%af%95", "term_group": 0, "term_taxonomy_id": 2743, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 425, "name": "图像处理", "slug": "%e5%9b%be%e5%83%8f%e5%a4%84%e7%90%86", "term_group": 0, "term_taxonomy_id": 425, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 89, "filter": "raw"}, {"term_id": 1114, "name": "艺术设计", "slug": "%e8%89%ba%e6%9c%af%e8%ae%be%e8%ae%a1", "term_group": 0, "term_taxonomy_id": 1114, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 11, "filter": "raw"}, {"term_id": 1111, "name": "中文", "slug": "%e4%b8%ad%e6%96%87", "term_group": 0, "term_taxonomy_id": 1111, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 21, "filter": "raw"}, {"term_id": 379, "name": "语音识别", "slug": "%e8%af%ad%e9%9f%b3%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 379, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 27, "filter": "raw"}, {"term_id": 359, "name": "医学影像", "slug": "%e5%8c%bb%e5%ad%a6%e5%bd%b1%e5%83%8f", "term_group": 0, "term_taxonomy_id": 359, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 33, "filter": "raw"}, {"term_id": 1627, "name": "图像分割", "slug": "%e5%9b%be%e5%83%8f%e5%88%86%e5%89%b2", "term_group": 0, "term_taxonomy_id": 1627, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 25, "filter": "raw"}, {"term_id": 348, "name": "图像分类", "slug": "%e5%9b%be%e5%83%8f%e5%88%86%e7%b1%bb", "term_group": 0, "term_taxonomy_id": 348, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 201, "filter": "raw"}, {"term_id": 2188, "name": "yolo", "slug": "yolo", "term_group": 0, "term_taxonomy_id": 2188, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 18, "filter": "raw"}, {"term_id": 341, "name": "目标检测", "slug": "%e7%9b%ae%e6%a0%87%e6%a3%80%e6%b5%8b", "term_group": 0, "term_taxonomy_id": 341, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 150, "filter": "raw"}, {"term_id": 1794, "name": "音乐信息检索", "slug": "%e9%9f%b3%e4%b9%90%e4%bf%a1%e6%81%af%e6%a3%80%e7%b4%a2", "term_group": 0, "term_taxonomy_id": 1794, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 10, "filter": "raw"}, {"term_id": 316, "name": "计算机视觉", "slug": "%e8%ae%a1%e7%ae%97%e6%9c%ba%e8%a7%86%e8%a7%89", "term_group": 0, "term_taxonomy_id": 316, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 68, "filter": "raw"}, {"term_id": 918, "name": "高分辨率", "slug": "%e9%ab%98%e5%88%86%e8%be%a8%e7%8e%87", "term_group": 0, "term_taxonomy_id": 918, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 4725, "name": "音频识别", "slug": "%e9%9f%b3%e9%a2%91%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 4725, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 4921, "name": "DeepSeek", "slug": "deepseek", "term_group": 0, "term_taxonomy_id": 4921, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 11, "filter": "raw"}, {"term_id": 2547, "name": "蛋白质", "slug": "%e8%9b%8b%e7%99%bd%e8%b4%a8", "term_group": 0, "term_taxonomy_id": 2547, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 40, "filter": "raw"}, {"term_id": 1094, "name": "情感识别", "slug": "%e6%83%85%e6%84%9f%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 1094, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 8, "filter": "raw"}, {"term_id": 1225, "name": "智能问答", "slug": "%e6%99%ba%e8%83%bd%e9%97%ae%e7%ad%94", "term_group": 0, "term_taxonomy_id": 1225, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 15, "filter": "raw"}, {"term_id": 699, "name": "编程语言", "slug": "%e7%bc%96%e7%a8%8b%e8%af%ad%e8%a8%80", "term_group": 0, "term_taxonomy_id": 699, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 2633, "name": "材料科学", "slug": "%e6%9d%90%e6%96%99%e7%a7%91%e5%ad%a6", "term_group": 0, "term_taxonomy_id": 2633, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 11, "filter": "raw"}, {"term_id": 2382, "name": "数学问题", "slug": "%e6%95%b0%e5%ad%a6%e9%97%ae%e9%a2%98", "term_group": 0, "term_taxonomy_id": 2382, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 19, "filter": "raw"}, {"term_id": 2192, "name": "AI for Science", "slug": "ai-for-science", "term_group": 0, "term_taxonomy_id": 2192, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 278, "filter": "raw"}, {"term_id": 787, "name": "强化学习", "slug": "%e5%bc%ba%e5%8c%96%e5%ad%a6%e4%b9%a0", "term_group": 0, "term_taxonomy_id": 787, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 19, "filter": "raw"}, {"term_id": 2285, "name": "大模型", "slug": "%e5%a4%a7%e6%a8%a1%e5%9e%8b", "term_group": 0, "term_taxonomy_id": 2285, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 228, "filter": "raw"}, {"term_id": 2615, "name": "RNA", "slug": "rna", "term_group": 0, "term_taxonomy_id": 2615, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 2186, "name": "无人机", "slug": "%e6%97%a0%e4%ba%ba%e6%9c%ba", "term_group": 0, "term_taxonomy_id": 2186, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 1234, "name": "航拍", "slug": "%e8%88%aa%e6%8b%8d", "term_group": 0, "term_taxonomy_id": 1234, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 480, "name": "道路交通", "slug": "%e9%81%93%e8%b7%af%e4%ba%a4%e9%80%9a", "term_group": 0, "term_taxonomy_id": 480, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 17, "filter": "raw"}, {"term_id": 2045, "name": "航拍图像", "slug": "%e8%88%aa%e6%8b%8d%e5%9b%be%e5%83%8f", "term_group": 0, "term_taxonomy_id": 2045, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 1982, "name": "对话生成", "slug": "%e5%af%b9%e8%af%9d%e7%94%9f%e6%88%90", "term_group": 0, "term_taxonomy_id": 1982, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 600, "name": "文本分类", "slug": "%e6%96%87%e6%9c%ac%e5%88%86%e7%b1%bb", "term_group": 0, "term_taxonomy_id": 600, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 35, "filter": "raw"}, {"term_id": 410, "name": "视频分类", "slug": "%e8%a7%86%e9%a2%91%e5%88%86%e7%b1%bb", "term_group": 0, "term_taxonomy_id": 410, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 17, "filter": "raw"}, {"term_id": 2162, "name": "NVIDIA", "slug": "nvidia-2", "term_group": 0, "term_taxonomy_id": 2162, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 521, "name": "机器人", "slug": "%e6%9c%ba%e5%99%a8%e4%ba%ba", "term_group": 0, "term_taxonomy_id": 521, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 21, "filter": "raw"}, {"term_id": 292, "name": "安全事故", "slug": "%e5%ae%89%e5%85%a8%e4%ba%8b%e6%95%85", "term_group": 0, "term_taxonomy_id": 292, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 1424, "name": "视频描述", "slug": "%e8%a7%86%e9%a2%91%e6%8f%8f%e8%bf%b0", "term_group": 0, "term_taxonomy_id": 1424, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 9, "filter": "raw"}, {"term_id": 350, "name": "垃圾分类", "slug": "%e5%9e%83%e5%9c%be%e5%88%86%e7%b1%bb", "term_group": 0, "term_taxonomy_id": 350, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 1532, "name": "车道检测", "slug": "%e8%bd%a6%e9%81%93%e6%a3%80%e6%b5%8b", "term_group": 0, "term_taxonomy_id": 1532, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 7, "filter": "raw"}, {"term_id": 2564, "name": "指令微调", "slug": "%e6%8c%87%e4%bb%a4%e5%be%ae%e8%b0%83", "term_group": 0, "term_taxonomy_id": 2564, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 6, "filter": "raw"}, {"term_id": 4704, "name": "具身智能", "slug": "%e5%85%b7%e8%ba%ab%e6%99%ba%e8%83%bd", "term_group": 0, "term_taxonomy_id": 4704, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 9, "filter": "raw"}, {"term_id": 1962, "name": "机器人抓取", "slug": "%e6%9c%ba%e5%99%a8%e4%ba%ba%e6%8a%93%e5%8f%96", "term_group": 0, "term_taxonomy_id": 1962, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 4, "filter": "raw"}, {"term_id": 1847, "name": "光学字符识别", "slug": "%e5%85%89%e5%ad%a6%e5%ad%97%e7%ac%a6%e8%af%86%e5%88%ab", "term_group": 0, "term_taxonomy_id": 1847, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 5, "filter": "raw"}, {"term_id": 662, "name": "CUDA", "slug": "cuda", "term_group": 0, "term_taxonomy_id": 662, "taxonomy": "post_tag", "description": "", "parent": 0, "count": 3, "filter": "raw"}]