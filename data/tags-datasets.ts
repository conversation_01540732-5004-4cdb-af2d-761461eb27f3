// Generated from https://orion.hyper.ai/en/wp-json/hyperai/v1/tags?post_type=datasets&per_page=100

import type { TranslatedTag } from '@/types'

export const tagsDatasets: TranslatedTag[] = [
  {
    'AI for Science': {
      cn: 'AI for Science',
      en: 'AI for Science',
      ja: '科学のためのAI',
      kr: '과학을 위한 AI',
      ar: 'الذكاء الاصطناعي للعلوم',
      fr: 'IA pour la science',
      de: 'KI für die Wissenschaft',
    },
  },
  {
    空间转录组学: {
      cn: '空间转录组学',
      en: 'Spatial Transcriptomics',
      ja: '空間トランスクリプトミクス',
      kr: '공간 전사체학',
      ar: 'علم النسخ المكاني',
      fr: 'Transcriptomique spatiale',
      de: 'Räumliche Transkriptomik',
    },
  },
  {
    强化学习: {
      cn: '强化学习',
      en: 'Reinforcement Learning',
      ja: '強化学習',
      kr: '강화학습',
      ar: 'التعلم المعزز',
      fr: 'Apprentissage par renforcement',
      de: 'Verstärkendes Lernen',
    },
  },
  {
    编程语言: {
      cn: '编程语言',
      en: 'Programming Languages',
      ja: 'プログラミング言語',
      kr: '프로그래밍 언어',
      ar: 'لغات البرمجة',
      fr: 'Langages de programmation',
      de: 'Programmiersprachen',
    },
  },
  {
    问答数据集: {
      cn: '问答数据集',
      en: 'Question Answering Dataset',
      ja: '質問応答データセット',
      kr: '질의응답 데이터셋',
      ar: 'مجموعة بيانات الأسئلة والأجوبة',
      fr: 'Ensemble de données de questions-réponses',
      de: 'Frage-Antwort-Datensatz',
    },
  },
  {
    大模型: {
      cn: '大模型',
      en: 'Large Language Models',
      ja: '大規模言語モデル',
      kr: '대규모 언어 모델',
      ar: 'نماذج اللغة الكبيرة',
      fr: 'Grands modèles de langage',
      de: 'Große Sprachmodelle',
    },
  },
  {
    数学推理: {
      cn: '数学推理',
      en: 'Mathematical Reasoning',
      ja: '数学的推論',
      kr: '수학적 추론',
      ar: 'الاستدلال الرياضي',
      fr: 'Raisonnement mathématique',
      de: 'Mathematisches Schlussfolgern',
    },
  },
  {
    模型推理: {
      cn: '模型推理',
      en: 'Model Inference',
      ja: 'モデル推論',
      kr: '모델 추론',
      ar: 'استدلال النموذج',
      fr: 'Inférence de modèle',
      de: 'Modellschlussfolgerung',
    },
  },
  {
    RNA: {
      cn: 'RNA',
      en: 'RNA',
      ja: 'RNA',
      kr: 'RNA',
      ar: 'الحمض النووي الريبوزي',
      fr: 'ARN',
      de: 'RNA',
    },
  },
  {
    医学图像: {
      cn: '医学图像',
      en: 'Medical Imaging',
      ja: '医療画像',
      kr: '의료 영상',
      ar: 'التصوير الطبي',
      fr: 'Imagerie médicale',
      de: 'Medizinische Bildgebung',
    },
  },
  {
    超声图像: {
      cn: '超声图像',
      en: 'Ultrasound Imaging',
      ja: '超音波画像',
      kr: '초음파 영상',
      ar: 'التصوير بالموجات فوق الصوتية',
      fr: 'Imagerie par ultrasons',
      de: 'Ultraschallbildgebung',
    },
  },
  {
    医学临床: {
      cn: '医学临床',
      en: 'Clinical Medicine',
      ja: '臨床医学',
      kr: '임상 의학',
      ar: 'الطب السريري',
      fr: 'Médecine clinique',
      de: 'Klinische Medizin',
    },
  },
  {
    无人机: {
      cn: '无人机',
      en: 'Drone',
      ja: 'ドローン',
      kr: '드론',
      ar: 'طائرة بدون طيار',
      fr: 'Drone',
      de: 'Drohne',
    },
  },
  {
    航拍: {
      cn: '航拍',
      en: 'Aerial Photography',
      ja: '空中写真',
      kr: '항공 사진',
      ar: 'التصوير الجوي',
      fr: 'Photographie aérienne',
      de: 'Luftaufnahmen',
    },
  },
  {
    道路交通: {
      cn: '道路交通',
      en: 'Road Traffic',
      ja: '道路交通',
      kr: '도로 교통',
      ar: 'حركة المرور على الطرق',
      fr: 'Trafic routier',
      de: 'Straßenverkehr',
    },
  },
  {
    城市: {
      cn: '城市',
      en: 'City',
      ja: '都市',
      kr: '도시',
      ar: 'مدينة',
      fr: 'Ville',
      de: 'Stadt',
    },
  },
  {
    航拍图像: {
      cn: '航拍图像',
      en: 'Aerial Images',
      ja: '空撮画像',
      kr: '항공 이미지',
      ar: 'صور جوية',
      fr: 'Images aériennes',
      de: 'Luftbilder',
    },
  },
  {
    目标检测: {
      cn: '目标检测',
      en: 'Object Detection',
      ja: '物体検出',
      kr: '객체 감지',
      ar: 'كشف الأجسام',
      fr: "Détection d'objets",
      de: 'Objekterkennung',
    },
  },
  {
    图像分类: {
      cn: '图像分类',
      en: 'Image Classification',
      ja: '画像分類',
      kr: '이미지 분류',
      ar: 'تصنيف الصور',
      fr: "Classification d'images",
      de: 'Bildklassifizierung',
    },
  },
  {
    对话生成: {
      cn: '对话生成',
      en: 'Dialogue Generation',
      ja: '対話生成',
      kr: '대화 생성',
      ar: 'توليد الحوار',
      fr: 'Génération de dialogue',
      de: 'Dialoggenerierung',
    },
  },
  {
    文本分类: {
      cn: '文本分类',
      en: 'Text Classification',
      ja: 'テキスト分類',
      kr: '텍스트 분류',
      ar: 'تصنيف النص',
      fr: 'Classification de texte',
      de: 'Textklassifizierung',
    },
  },
  {
    角色扮演: {
      cn: '角色扮演',
      en: 'Role-Playing',
      ja: 'ロールプレイング',
      kr: '역할 놀이',
      ar: 'لعب الأدوار',
      fr: 'Jeu de rôle',
      de: 'Rollenspiel',
    },
  },
  {
    多模态: {
      cn: '多模态',
      en: 'Multimodal',
      ja: 'マルチモーダル',
      kr: '멀티모달',
      ar: 'متعدد الوسائط',
      fr: 'Multimodal',
      de: 'Multimodal',
    },
  },
  {
    触觉传感: {
      cn: '触觉传感',
      en: 'Tactile Sensing',
      ja: '触覚センシング',
      kr: '촉각 센싱',
      ar: 'الاستشعار اللمسي',
      fr: 'Détection tactile',
      de: 'Taktile Wahrnehmung',
    },
  },
  {
    '4D 场景': {
      cn: '4D 场景',
      en: '4D Scene',
      ja: '4Dシーン',
      kr: '4D 장면',
      ar: 'مشهد رباعي الأبعاد',
      fr: 'Scène 4D',
      de: '4D-Szene',
    },
  },
  {
    视频分类: {
      cn: '视频分类',
      en: 'Video Classification',
      ja: '動画分類',
      kr: '동영상 분류',
      ar: 'تصنيف الفيديو',
      fr: 'Classification vidéo',
      de: 'Videoklassifizierung',
    },
  },
  {
    NVIDIA: {
      cn: 'NVIDIA',
      en: 'NVIDIA',
      ja: 'NVIDIA',
      kr: 'NVIDIA',
      ar: 'NVIDIA',
      fr: 'NVIDIA',
      de: 'NVIDIA',
    },
  },
  {
    机器人: {
      cn: '机器人',
      en: 'Robot',
      ja: 'ロボット',
      kr: '로봇',
      ar: 'روبوت',
      fr: 'Robot',
      de: 'Roboter',
    },
  },
  {
    AI4S: {
      cn: 'AI4S',
      en: 'AI4S',
      ja: 'AI4S',
      kr: 'AI4S',
      ar: 'AI4S',
      fr: 'AI4S',
      de: 'AI4S',
    },
  },
  {
    蛋白质突变: {
      cn: '蛋白质突变',
      en: 'Protein Mutation',
      ja: 'タンパク質変異',
      kr: '단백질 돌연변이',
      ar: 'طفرة البروتين',
      fr: 'Mutation protéique',
      de: 'Proteinmutation',
    },
  },
  {
    电池寿命: {
      cn: '电池寿命',
      en: 'Battery Life',
      ja: 'バッテリー寿命',
      kr: '배터리 수명',
      ar: 'عمر البطارية',
      fr: 'Durée de vie de la batterie',
      de: 'Akkulaufzeit',
    },
  },
  {
    预测数据集: {
      cn: '预测数据集',
      en: 'Prediction Dataset',
      ja: '予測データセット',
      kr: '예측 데이터셋',
      ar: 'مجموعة بيانات التنبؤ',
      fr: 'Ensemble de données de prédiction',
      de: 'Vorhersagedatensatz',
    },
  },
  {
    鸟类: {
      cn: '鸟类',
      en: 'Birds',
      ja: '鳥類',
      kr: '새',
      ar: 'الطيور',
      fr: 'Oiseaux',
      de: 'Vögel',
    },
  },
  {
    安全事故: {
      cn: '安全事故',
      en: 'Safety Incidents',
      ja: '安全事故',
      kr: '안전 사고',
      ar: 'حوادث السلامة',
      fr: 'Incidents de sécurité',
      de: 'Sicherheitsvorfälle',
    },
  },
  {
    自动驾驶: {
      cn: '自动驾驶',
      en: 'Autonomous Driving',
      ja: '自動運転',
      kr: '자율 운전',
      ar: 'القيادة الذاتية',
      fr: 'Conduite autonome',
      de: 'Autonomes Fahren',
    },
  },
  {
    视频描述: {
      cn: '视频描述',
      en: 'Video Description',
      ja: '動画説明',
      kr: '동영상 설명',
      ar: 'وصف الفيديو',
      fr: 'Description vidéo',
      de: 'Videobeschreibung',
    },
  },
  {
    图像识别: {
      cn: '图像识别',
      en: 'Image Recognition',
      ja: '画像認識',
      kr: '이미지 인식',
      ar: 'التعرف على الصور',
      fr: "Reconnaissance d'image",
      de: 'Bilderkennung',
    },
  },
  {
    垃圾分类: {
      cn: '垃圾分类',
      en: 'Waste Classification',
      ja: 'ゴミ分類',
      kr: '쓰레기 분류',
      ar: 'تصنيف النفايات',
      fr: 'Classification des déchets',
      de: 'Abfallklassifizierung',
    },
  },
  {
    海洋塑料垃圾: {
      cn: '海洋塑料垃圾',
      en: 'Marine Plastic Waste',
      ja: '海洋プラスチックごみ',
      kr: '해양 플라스틱 쓰레기',
      ar: 'النفايات البلاستيكية البحرية',
      fr: 'Déchets plastiques marins',
      de: 'Meeresplastikmüll',
    },
  },
  {
    车道检测: {
      cn: '车道检测',
      en: 'Lane Detection',
      ja: '車線検出',
      kr: '차선 감지',
      ar: 'كشف المسار',
      fr: 'Détection de voie',
      de: 'Fahrspurerkennung',
    },
  },
  {
    OCR: {
      cn: 'OCR',
      en: 'OCR',
      ja: 'OCR',
      kr: 'OCR',
      ar: 'التعرف الضوئي على الحروف',
      fr: 'OCR',
      de: 'OCR',
    },
  },
  {
    语音识别: {
      cn: '语音识别',
      en: 'Speech Recognition',
      ja: '音声認識',
      kr: '음성 인식',
      ar: 'التعرف على الكلام',
      fr: 'Reconnaissance vocale',
      de: 'Spracherkennung',
    },
  },
  {
    天文: {
      cn: '天文',
      en: 'Astronomy',
      ja: '天文学',
      kr: '천문학',
      ar: 'علم الفلك',
      fr: 'Astronomie',
      de: 'Astronomie',
    },
  },
  {
    多语言: {
      cn: '多语言',
      en: 'Multilingual',
      ja: '多言語',
      kr: '다언어',
      ar: 'متعدد اللغات',
      fr: 'Multilingue',
      de: 'Mehrsprachig',
    },
  },
  {
    编码: {
      cn: '编码',
      en: 'Coding',
      ja: 'コーディング',
      kr: '코딩',
      ar: 'البرمجة',
      fr: 'Codage',
      de: 'Codierung',
    },
  },
  {
    指令微调: {
      cn: '指令微调',
      en: 'Instruction Fine-tuning',
      ja: '指示微調整',
      kr: '지시 미조정',
      ar: 'الضبط الدقيق بالتعليمات',
      fr: 'Réglage fin par instructions',
      de: 'Anweisungs-Feinabstimmung',
    },
  },
  {
    计算机视觉: {
      cn: '计算机视觉',
      en: 'Computer Vision',
      ja: 'コンピュータビジョン',
      kr: '컴퓨터 비전',
      ar: 'رؤية الحاسوب',
      fr: 'Vision par ordinateur',
      de: 'Maschinelles Sehen',
    },
  },
  {
    具身智能: {
      cn: '具身智能',
      en: 'Embodied Intelligence',
      ja: '身体化知能',
      kr: '신체화 지능',
      ar: 'الذكاء المتجسد',
      fr: 'Intelligence incarnée',
      de: 'Verkörperte Intelligenz',
    },
  },
  {
    机器人抓取: {
      cn: '机器人抓取',
      en: 'Robot Grasping',
      ja: 'ロボットグラスピング',
      kr: '로봇 포착',
      ar: 'قبضة الروبوت',
      fr: 'Préhension robotique',
      de: 'Robotisches Greifen',
    },
  },
  {
    中文: {
      cn: '中文',
      en: 'Chinese',
      ja: '中国語',
      kr: '중국어',
      ar: 'الصينية',
      fr: 'Chinois',
      de: 'Chinesisch',
    },
  },
  {
    光学字符识别: {
      cn: '光学字符识别',
      en: 'Optical Character Recognition',
      ja: '光学文字認識',
      kr: '광학 문자 인식',
      ar: 'التعرف الضوئي على الحروف',
      fr: 'Reconnaissance optique de caractères',
      de: 'Optische Zeichenerkennung',
    },
  },
  {
    数学问题: {
      cn: '数学问题',
      en: 'Mathematical Problems',
      ja: '数学の問題',
      kr: '수학적 문제',
      ar: 'المسائل الرياضية',
      fr: 'Problèmes mathématiques',
      de: 'Mathematische Probleme',
    },
  },
  {
    '3D 分子': {
      cn: '3D 分子',
      en: '3D Molecules',
      ja: '3D分子',
      kr: '3차원 분자',
      ar: 'جزيئات ثلاثية الأبعاد',
      fr: 'Molécules 3D',
      de: '3D-Moleküle',
    },
  },
  {
    化学: {
      cn: '化学',
      en: 'Chemistry',
      ja: '化学',
      kr: '화학',
      ar: 'الكيمياء',
      fr: 'Chimie',
      de: 'Chemie',
    },
  },
  {
    量子化学: {
      cn: '量子化学',
      en: 'Quantum Chemistry',
      ja: '量子化学',
      kr: '양자 화학',
      ar: 'الكيمياء الكمية',
      fr: 'Chimie quantique',
      de: 'Quantenchemie',
    },
  },
  {
    CUDA: {
      cn: 'CUDA',
      en: 'CUDA',
      ja: 'CUDA',
      kr: 'CUDA',
      ar: 'CUDA',
      fr: 'CUDA',
      de: 'CUDA',
    },
  },
  {
    编程语言处理: {
      cn: '编程语言处理',
      en: 'Programming Language Processing',
      ja: 'プログラミング言語処理',
      kr: '프로그래밍 언어 처리',
      ar: 'معالجة لغة البرمجة',
      fr: 'Traitement du langage de programmation',
      de: 'Programmiersprachen-Verarbeitung',
    },
  },
  {
    推理: {
      cn: '推理',
      en: 'Inference',
      ja: '推論',
      kr: '추론',
      ar: 'الاستدلال',
      fr: 'Inférence',
      de: 'Inferenz',
    },
  },
  {
    医学数据: {
      cn: '医学数据',
      en: 'Medical Data',
      ja: '医療データ',
      kr: '의료 데이터',
      ar: 'البيانات الطبية',
      fr: 'Données médicales',
      de: 'Medizinische Daten',
    },
  },
  {
    医疗诊断: {
      cn: '医疗诊断',
      en: 'Medical Diagnosis',
      ja: '医療診断',
      kr: '의료 진단',
      ar: 'التشخيص الطبي',
      fr: 'Diagnostic médical',
      de: 'Medizinische Diagnose',
    },
  },
  {
    文本处理: {
      cn: '文本处理',
      en: 'Text Processing',
      ja: 'テキスト処理',
      kr: '텍스트 처리',
      ar: 'معالجة النص',
      fr: 'Traitement de texte',
      de: 'Textverarbeitung',
    },
  },
  {
    法律领域: {
      cn: '法律领域',
      en: 'Legal Domain',
      ja: '法律分野',
      kr: '법률 분야',
      ar: 'المجال القانوني',
      fr: 'Domaine juridique',
      de: 'Rechtsbereich',
    },
  },
  {
    CVPR: {
      cn: 'CVPR',
      en: 'CVPR',
      ja: 'CVPR',
      kr: 'CVPR',
      ar: 'CVPR',
      fr: 'CVPR',
      de: 'CVPR',
    },
  },
  {
    物体检测: {
      cn: '物体检测',
      en: 'Object Detection',
      ja: '物体検出',
      kr: '객체 감지',
      ar: 'كشف الأجسام',
      fr: "Détection d'objets",
      de: 'Objekterkennung',
    },
  },
  {
    人类偏好: {
      cn: '人类偏好',
      en: 'Human Preferences',
      ja: '人間の好み',
      kr: '인간의 선호도',
      ar: 'التفضيلات البشرية',
      fr: 'Préférences humaines',
      de: 'Menschliche Präferenzen',
    },
  },
  {
    文生图: {
      cn: '文生图',
      en: 'Text-to-Image',
      ja: 'テキストから画像',
      kr: '텍스트에서 이미지',
      ar: 'تحويل النص إلى صورة',
      fr: 'Texte vers image',
      de: 'Text-zu-Bild',
    },
  },
  {
    图像视觉: {
      cn: '图像视觉',
      en: 'Image Vision',
      ja: '画像視覚',
      kr: '이미지 시각',
      ar: 'رؤية الصور',
      fr: "Vision d'image",
      de: 'Bildsehen',
    },
  },
  {
    姿态识别: {
      cn: '姿态识别',
      en: 'Pose Recognition',
      ja: 'ポーズ認識',
      kr: '포즈 인식',
      ar: 'التعرف على الوضعية',
      fr: 'Reconnaissance de pose',
      de: 'Posenerkennung',
    },
  },
  {
    手势检测: {
      cn: '手势检测',
      en: 'Gesture Detection',
      ja: 'ジェスチャー検出',
      kr: '제스처 감지',
      ar: 'كشف الإيماءات',
      fr: 'Détection de gestes',
      de: 'Gestenerkennung',
    },
  },
  {
    材料化学: {
      cn: '材料化学',
      en: 'Materials Chemistry',
      ja: '材料化学',
      kr: '재료 화학',
      ar: 'كيمياء المواد',
      fr: 'Chimie des matériaux',
      de: 'Materialchemie',
    },
  },
  {
    偏好对齐: {
      cn: '偏好对齐',
      en: 'Preference Alignment',
      ja: '選好アライメント',
      kr: '선호도 정렬',
      ar: 'محاذاة التفضيلات',
      fr: 'Alignement des préférences',
      de: 'Präferenzausrichtung',
    },
  },
  {
    yolo: {
      cn: 'yolo',
      en: 'YOLO',
      ja: 'YOLO',
      kr: 'YOLO',
      ar: 'YOLO',
      fr: 'YOLO',
      de: 'YOLO',
    },
  },
  {
    遥感影像: {
      cn: '遥感影像',
      en: 'Remote Sensing Images',
      ja: 'リモートセンシング画像',
      kr: '원격 감지 이미지',
      ar: 'صور الاستشعار عن بعد',
      fr: 'Images de télédétection',
      de: 'Fernerkundungsbilder',
    },
  },
  {
    DeepSeek: {
      cn: 'DeepSeek',
      en: 'DeepSeek',
      ja: 'DeepSeek',
      kr: 'DeepSeek',
      ar: 'DeepSeek',
      fr: 'DeepSeek',
      de: 'DeepSeek',
    },
  },
  {
    仿生学: {
      cn: '仿生学',
      en: 'Bionics',
      ja: 'バイオニクス',
      kr: '생체 공학',
      ar: 'علم البيونيكس',
      fr: 'Bionique',
      de: 'Bionik',
    },
  },
  {
    拍翼机器: {
      cn: '拍翼机器',
      en: 'Flapping-Wing Machine',
      ja: '羽ばたき機',
      kr: '날개 흔들기 기계',
      ar: 'آلة الرفرفة',
      fr: 'Machine à ailes battantes',
      de: 'Schlagflügelmaschine',
    },
  },
  {
    电影视频: {
      cn: '电影视频',
      en: 'Movie Videos',
      ja: '映画動画',
      kr: '영화 동영상',
      ar: 'مقاطع فيديو الأفلام',
      fr: 'Vidéos de films',
      de: 'Filmvideos',
    },
  },
  {
    视频生成: {
      cn: '视频生成',
      en: 'Video Generation',
      ja: '動画生成',
      kr: '동영상 생성',
      ar: 'توليد الفيديو',
      fr: 'Génération de vidéo',
      de: 'Videogenerierung',
    },
  },
  {
    Python: {
      cn: 'Python',
      en: 'Python',
      ja: 'Python',
      kr: '파이썬',
      ar: 'بايثون',
      fr: 'Python',
      de: 'Python',
    },
  },
  {
    焦点映射: {
      cn: '焦点映射',
      en: 'Focus Mapping',
      ja: 'フォーカスマッピング',
      kr: '초점 매핑',
      ar: 'رسم خرائط التركيز',
      fr: 'Cartographie de focus',
      de: 'Fokuskartierung',
    },
  },
  {
    分子动力学: {
      cn: '分子动力学',
      en: 'Molecular Dynamics',
      ja: '分子動力学',
      kr: '분자 동역학',
      ar: 'ديناميكا الجزيئات',
      fr: 'Dynamique moléculaire',
      de: 'Molekulardynamik',
    },
  },
  {
    代码: {
      cn: '代码',
      en: 'Code',
      ja: 'コード',
      kr: '코드',
      ar: 'الشفرة',
      fr: 'Code',
      de: 'Code',
    },
  },
  {
    数学: {
      cn: '数学',
      en: 'Mathematics',
      ja: '数学',
      kr: '수학',
      ar: 'الرياضيات',
      fr: 'Mathématiques',
      de: 'Mathematik',
    },
  },
  {
    图像分析: {
      cn: '图像分析',
      en: 'Image Analysis',
      ja: '画像分析',
      kr: '이미지 분석',
      ar: 'تحليل الصور',
      fr: "Analyse d'image",
      de: 'Bildanalyse',
    },
  },
  {
    机器视觉: {
      cn: '机器视觉',
      en: 'Computer Vision',
      ja: 'コンピュータビジョン',
      kr: '컴퓨터 비전',
      ar: 'رؤية الحاسوب',
      fr: 'Vision par ordinateur',
      de: 'Maschinelles Sehen',
    },
  },
  {
    国外企业: {
      cn: '国外企业',
      en: 'Foreign Companies',
      ja: '海外企業',
      kr: '해외 기업',
      ar: 'الشركات الأجنبية',
      fr: 'Entreprises étrangères',
      de: 'Ausländische Unternehmen',
    },
  },
  {
    人脸检测: {
      cn: '人脸检测',
      en: 'Face Detection',
      ja: '顔検出',
      kr: '얼굴 감지',
      ar: 'كشف الوجه',
      fr: 'Détection de visage',
      de: 'Gesichtserkennung',
    },
  },
  {
    基准测试: {
      cn: '基准测试',
      en: 'Benchmark Testing',
      ja: 'ベンチマークテスト',
      kr: '벤치마크 테스트',
      ar: 'اختبار المعايير',
      fr: 'Tests de référence',
      de: 'Benchmark-Tests',
    },
  },
  {
    时空数据: {
      cn: '时空数据',
      en: 'Spatiotemporal Data',
      ja: '時空間データ',
      kr: '시공간 데이터',
      ar: 'البيانات المكانية الزمانية',
      fr: 'Données spatio-temporelles',
      de: 'Raumzeitliche Daten',
    },
  },
  {
    材料学: {
      cn: '材料学',
      en: 'Materials Science',
      ja: '材料科学',
      kr: '재료 과학',
      ar: 'علم المواد',
      fr: 'Science des matériaux',
      de: 'Materialwissenschaft',
    },
  },
  {
    游戏场景设计: {
      cn: '游戏场景设计',
      en: 'Game Scene Design',
      ja: 'ゲームシーン設計',
      kr: '게임 장면 디자인',
      ar: 'تصميم مشهد اللعبة',
      fr: 'Conception de scène de jeu',
      de: 'Spielszenendesign',
    },
  },
  {
    视频处理: {
      cn: '视频处理',
      en: 'Video Processing',
      ja: '動画処理',
      kr: '비디오 처리',
      ar: 'معالجة الفيديو',
      fr: 'Traitement vidéo',
      de: 'Videoverarbeitung',
    },
  },
  {
    游戏: {
      cn: '游戏',
      en: 'Game',
      ja: 'ゲーム',
      kr: '게임',
      ar: 'لعبة',
      fr: 'Jeu',
      de: 'Spiel',
    },
  },
  {
    文化: {
      cn: '文化',
      en: 'Culture',
      ja: '文化',
      kr: '문화',
      ar: 'ثقافة',
      fr: 'Culture',
      de: 'Kultur',
    },
  },
  {
    医学: {
      cn: '医学',
      en: 'Medicine',
      ja: '医学',
      kr: '의학',
      ar: 'الطب',
      fr: 'Médecine',
      de: 'Medizin',
    },
  },
  {
    图像处理: {
      cn: '图像处理',
      en: 'Image Processing',
      ja: '画像処理',
      kr: '이미지 처리',
      ar: 'معالجة الصور',
      fr: "Traitement d'image",
      de: 'Bildverarbeitung',
    },
  },
  {
    交通标志检测: {
      cn: '交通标志检测',
      en: 'Traffic Sign Detection',
      ja: '交通標識検出',
      kr: '교통 표지판 감지',
      ar: 'كشف إشارات المرور',
      fr: 'Détection de panneaux de signalisation',
      de: 'Verkehrszeichenerkennung',
    },
  },
  {
    交通标志识别: {
      cn: '交通标志识别',
      en: 'Traffic Sign Recognition',
      ja: '交通標識認識',
      kr: '교통 표지판 인식',
      ar: 'التعرف على إشارات المرور',
      fr: 'Reconnaissance des panneaux de signalisation',
      de: 'Verkehrszeichenerkennung',
    },
  },
  {
    图像理解: {
      cn: '图像理解',
      en: 'Image Understanding',
      ja: '画像理解',
      kr: '이미지 이해',
      ar: 'فهم الصور',
      fr: "Compréhension d'images",
      de: 'Bildverständnis',
    },
  },
  {
    春节: {
      cn: '春节',
      en: 'Spring Festival',
      ja: '春節',
      kr: '춘절',
      ar: 'عيد الربيع',
      fr: 'Fête du Printemps',
      de: 'Frühlingsfest',
    },
  },
  {
    标签噪音: {
      cn: '标签噪音',
      en: 'Label Noise',
      ja: 'ラベルノイズ',
      kr: '라벨 노이즈',
      ar: 'ضوضاء التسمية',
      fr: "Bruit d'étiquette",
      de: 'Label-Rauschen',
    },
  },
  {
    音频伪造: {
      cn: '音频伪造',
      en: 'Audio Forgery',
      ja: '音声偽造',
      kr: '오디오 위조',
      ar: 'تزوير الصوت',
      fr: 'Contrefaçon audio',
      de: 'Audiofälschung',
    },
  },
  {
    大模型微调: {
      cn: '大模型微调',
      en: 'Large Model Fine-tuning',
      ja: '大規模モデル微調整',
      kr: '대규모 모델 미세 조정',
      ar: 'الضبط الدقيق للنموذج الكبير',
      fr: 'Réglage fin de grand modèle',
      de: 'Feinabstimmung großer Modelle',
    },
  },
  {
    人脸特征点检测: {
      cn: '人脸特征点检测',
      en: 'Facial Landmark Detection',
      ja: '顔特徴点検出',
      kr: '얼굴 특징점 감지',
      ar: 'كشف معالم الوجه',
      fr: 'Détection de points caractéristiques du visage',
      de: 'Gesichtsmerkmale-Erkennung',
    },
  },
  {
    面部检测: {
      cn: '面部检测',
      en: 'Face Detection',
      ja: '顔検出',
      kr: '얼굴 감지',
      ar: 'كشف الوجه',
      fr: 'Détection de visage',
      de: 'Gesichtserkennung',
    },
  },
  {
    面部识别: {
      cn: '面部识别',
      en: 'Face Recognition',
      ja: '顔認識',
      kr: '얼굴 인식',
      ar: 'التعرف على الوجه',
      fr: 'Reconnaissance faciale',
      de: 'Gesichtserkennung',
    },
  },
  {
    情感分析: {
      cn: '情感分析',
      en: 'Sentiment Analysis',
      ja: '感情分析',
      kr: '감정 분석',
      ar: 'تحليل المشاعر',
      fr: 'Analyse des sentiments',
      de: 'Stimmungsanalyse',
    },
  },
  {
    医学诊断: {
      cn: '医学诊断',
      en: 'Medical Diagnosis',
      ja: '医療診断',
      kr: '의학 진단',
      ar: 'التشخيص الطبي',
      fr: 'Diagnostic médical',
      de: 'Medizinische Diagnose',
    },
  },
  {
    自然语言处理: {
      cn: '自然语言处理',
      en: 'Natural Language Processing',
      ja: '自然言語処理',
      kr: '자연어 처리',
      ar: 'معالجة اللغة الطبيعية',
      fr: 'Traitement du langage naturel',
      de: 'Natürliche Sprachverarbeitung',
    },
  },
  {
    对话数据集: {
      cn: '对话数据集',
      en: 'Dialogue Dataset',
      ja: '対話データセット',
      kr: '대화 데이터셋',
      ar: 'مجموعة بيانات الحوار',
      fr: 'Ensemble de données de dialogue',
      de: 'Dialog-Datensatz',
    },
  },
  {
    教育: {
      cn: '教育',
      en: 'Education',
      ja: '教育',
      kr: '교육',
      ar: 'التعليم',
      fr: 'Éducation',
      de: 'Bildung',
    },
  },
  {
    中医: {
      cn: '中医',
      en: 'Traditional Chinese Medicine',
      ja: '中国伝統医学',
      kr: '중국 전통 의학',
      ar: 'الطب الصيني التقليدي',
      fr: 'Médecine traditionnelle chinoise',
      de: 'Traditionelle Chinesische Medizin',
    },
  },
  {
    医学健康: {
      cn: '医学健康',
      en: 'Medical Health',
      ja: '医療健康',
      kr: '의학 건강',
      ar: 'الصحة الطبية',
      fr: 'Santé médicale',
      de: 'Medizinische Gesundheit',
    },
  },
  {
    圣诞节数据集: {
      cn: '圣诞节数据集',
      en: 'Christmas Dataset',
      ja: 'クリスマスデータセット',
      kr: '크리스마스 데이터셋',
      ar: 'مجموعة بيانات عيد الميلاد',
      fr: 'Ensemble de données de Noël',
      de: 'Weihnachtsdatensatz',
    },
  },
  {
    方言: {
      cn: '方言',
      en: 'Dialect',
      ja: '方言',
      kr: '방언',
      ar: 'لهجة',
      fr: 'Dialecte',
      de: 'Dialekt',
    },
  },
  {
    语音对话: {
      cn: '语音对话',
      en: 'Voice Dialogue',
      ja: '音声対話',
      kr: '음성 대화',
      ar: 'حوار صوتي',
      fr: 'Dialogue Vocal',
      de: 'Sprachdialog',
    },
  },
  {
    几何问题: {
      cn: '几何问题',
      en: 'Geometric Problems',
      ja: '幾何学的問題',
      kr: '기하학 문제',
      ar: 'مسائل هندسية',
      fr: 'Problèmes Géométriques',
      de: 'Geometrische Probleme',
    },
  },
  {
    国外高校: {
      cn: '国外高校',
      en: 'Foreign Universities',
      ja: '海外大学',
      kr: '해외 대학',
      ar: 'جامعات أجنبية',
      fr: 'Universités Étrangères',
      de: 'Ausländische Hochschulen',
    },
  },
  {
    评估基准: {
      cn: '评估基准',
      en: 'Evaluation Benchmark',
      ja: '評価ベンチマーク',
      kr: '평가 벤치마크',
      ar: 'معيار التقييم',
      fr: "Référence d'Évaluation",
      de: 'Bewertungsmaßstab',
    },
  },
  {
    推理数据集: {
      cn: '推理数据集',
      en: 'Inference Dataset',
      ja: '推論データセット',
      kr: '추론 데이터셋',
      ar: 'مجموعة بيانات الاستدلال',
      fr: "Ensemble de Données d'Inférence",
      de: 'Inferenz-Datensatz',
    },
  },
  {
    有机晶体: {
      cn: '有机晶体',
      en: 'Organic Crystal',
      ja: '有機結晶',
      kr: '유기 결정',
      ar: 'بلورة عضوية',
      fr: 'Cristal Organique',
      de: 'Organischer Kristall',
    },
  },
  {
    机器学习: {
      cn: '机器学习',
      en: 'Machine Learning',
      ja: '機械学習',
      kr: '머신 러닝',
      ar: 'تعلم الآلة',
      fr: 'Apprentissage Automatique',
      de: 'Maschinelles Lernen',
    },
  },
  {
    晶体化学: {
      cn: '晶体化学',
      en: 'Crystal Chemistry',
      ja: '結晶化学',
      kr: '결정 화학',
      ar: 'كيمياء البلورات',
      fr: 'Chimie Cristalline',
      de: 'Kristallchemie',
    },
  },
  {
    材料科学: {
      cn: '材料科学',
      en: 'Materials Science',
      ja: '材料科学',
      kr: '재료 과학',
      ar: 'علم المواد',
      fr: 'Science des Matériaux',
      de: 'Materialwissenschaft',
    },
  },
  {
    DFT: {
      cn: 'DFT',
      en: 'DFT',
      ja: 'DFT',
      kr: 'DFT',
      ar: 'نظرية دالة الكثافة',
      fr: 'DFT',
      de: 'DFT',
    },
  },
  {
    质谱: {
      cn: '质谱',
      en: 'Mass Spectrometry',
      ja: '質量分析',
      kr: '질량 분석',
      ar: 'مطياف الكتلة',
      fr: 'Spectrométrie de Masse',
      de: 'Massenspektrometrie',
    },
  },
  {
    场景理解: {
      cn: '场景理解',
      en: 'Scene Understanding',
      ja: 'シーン理解',
      kr: '장면 이해',
      ar: 'فهم المشهد',
      fr: 'Compréhension de scène',
      de: 'Szenenverständnis',
    },
  },
  {
    基准: {
      cn: '基准',
      en: 'Benchmark',
      ja: 'ベンチマーク',
      kr: '벤치마크',
      ar: 'معيار',
      fr: 'Référence',
      de: 'Benchmark',
    },
  },
  {
    深度学习: {
      cn: '深度学习',
      en: 'Deep Learning',
      ja: 'ディープラーニング',
      kr: '딥러닝',
      ar: 'التعلم العميق',
      fr: 'Apprentissage profond',
      de: 'Deep Learning',
    },
  },
  {
    文本分析: {
      cn: '文本分析',
      en: 'Text Analysis',
      ja: 'テキスト分析',
      kr: '텍스트 분석',
      ar: 'تحليل النص',
      fr: 'Analyse de texte',
      de: 'Textanalyse',
    },
  },
  {
    文本识别: {
      cn: '文本识别',
      en: 'Text Recognition',
      ja: 'テキスト認識',
      kr: '텍스트 인식',
      ar: 'التعرف على النص',
      fr: 'Reconnaissance de texte',
      de: 'Texterkennung',
    },
  },
  {
    生物: {
      cn: '生物',
      en: 'Biology',
      ja: '生物学',
      kr: '생물학',
      ar: 'علم الأحياء',
      fr: 'Biologie',
      de: 'Biologie',
    },
  },
  {
    细胞: {
      cn: '细胞',
      en: 'Cell',
      ja: '細胞',
      kr: '세포',
      ar: 'خلية',
      fr: 'Cellule',
      de: 'Zelle',
    },
  },
  {
    医疗: {
      cn: '医疗',
      en: 'Medical',
      ja: '医療',
      kr: '의료',
      ar: 'طبي',
      fr: 'Médical',
      de: 'Medizinisch',
    },
  },
  {
    卫星图像: {
      cn: '卫星图像',
      en: 'Satellite Images',
      ja: '衛星画像',
      kr: '위성 이미지',
      ar: 'صور الأقمار الصناعية',
      fr: 'Images satellites',
      de: 'Satellitenbilder',
    },
  },
  {
    地理信息: {
      cn: '地理信息',
      en: 'Geographic Information',
      ja: '地理情報',
      kr: '지리 정보',
      ar: 'المعلومات الجغرافية',
      fr: 'Information géographique',
      de: 'Geografische Information',
    },
  },
  {
    高光谱图像分类: {
      cn: '高光谱图像分类',
      en: 'Hyperspectral Image Classification',
      ja: 'ハイパースペクトル画像分類',
      kr: '초분광 이미지 분류',
      ar: 'تصنيف الصور فائقة الطيف',
      fr: "Classification d'images hyperspectrales",
      de: 'Hyperspektrale Bildklassifikation',
    },
  },
  {
    视频分割: {
      cn: '视频分割',
      en: 'Video Segmentation',
      ja: 'ビデオセグメンテーション',
      kr: '비디오 분할',
      ar: 'تجزئة الفيديو',
      fr: 'Segmentation vidéo',
      de: 'Videosegmentierung',
    },
  },
  {
    '3D 目标检测': {
      cn: '3D 目标检测',
      en: '3D Object Detection',
      ja: '3D物体検出',
      kr: '3D 객체 감지',
      ar: 'كشف الكائنات ثلاثية الأبعاد',
      fr: "Détection d'objets 3D",
      de: '3D-Objekterkennung',
    },
  },
  {
    RGB显著目标检测: {
      cn: 'RGB显著目标检测',
      en: 'RGB Salient Object Detection',
      ja: 'RGB顕著物体検出',
      kr: 'RGB 현저 객체 감지',
      ar: 'كشف الكائنات البارزة RGB',
      fr: "Détection d'objets saillants RGB",
      de: 'RGB-Saliente Objekterkennung',
    },
  },
  {
    工业场景: {
      cn: '工业场景',
      en: 'Industrial Scene',
      ja: '産業シーン',
      kr: '산업 장면',
      ar: 'المشهد الصناعي',
      fr: 'Scène industrielle',
      de: 'Industrielle Szene',
    },
  },
  {
    异常检测: {
      cn: '异常检测',
      en: 'Anomaly Detection',
      ja: '異常検出',
      kr: '이상 감지',
      ar: 'كشف الشذوذ',
      fr: "Détection d'anomalies",
      de: 'Anomalieerkennung',
    },
  },
  {
    文本生成: {
      cn: '文本生成',
      en: 'Text Generation',
      ja: 'テキスト生成',
      kr: '텍스트 생성',
      ar: 'توليد النص',
      fr: 'Génération de texte',
      de: 'Textgenerierung',
    },
  },
  {
    法国: {
      cn: '法国',
      en: 'France',
      ja: 'フランス',
      kr: '프랑스',
      ar: 'فرنسا',
      fr: 'France',
      de: 'Frankreich',
    },
  },
  {
    高校: {
      cn: '高校',
      en: 'University',
      ja: '大学',
      kr: '대학',
      ar: 'جامعة',
      fr: 'Université',
      de: 'Universität',
    },
  },
  {
    RAG: {
      cn: 'RAG',
      en: 'RAG',
      ja: 'RAG',
      kr: 'RAG',
      ar: 'RAG',
      fr: 'RAG',
      de: 'RAG',
    },
  },
  {
    内容检索: {
      cn: '内容检索',
      en: 'Content Retrieval',
      ja: 'コンテンツ検索',
      kr: '콘텐츠 검색',
      ar: 'استرجاع المحتوى',
      fr: 'Récupération de contenu',
      de: 'Inhaltsabruf',
    },
  },
  {
    文本理解: {
      cn: '文本理解',
      en: 'Text Understanding',
      ja: 'テキスト理解',
      kr: '텍스트 이해',
      ar: 'فهم النص',
      fr: 'Compréhension de texte',
      de: 'Textverständnis',
    },
  },
  {
    图文数据集: {
      cn: '图文数据集',
      en: 'Image-Text Dataset',
      ja: '画像テキストデータセット',
      kr: '이미지-텍스트 데이터셋',
      ar: 'مجموعة بيانات الصور والنصوص',
      fr: 'Ensemble de données image-texte',
      de: 'Bild-Text-Datensatz',
    },
  },
  {
    统计型数据: {
      cn: '统计型数据',
      en: 'Statistical Data',
      ja: '統計データ',
      kr: '통계 데이터',
      ar: 'البيانات الإحصائية',
      fr: 'Données statistiques',
      de: 'Statistische Daten',
    },
  },
  {
    评测基准: {
      cn: '评测基准',
      en: 'Evaluation Benchmark',
      ja: '評価ベンチマーク',
      kr: '평가 벤치마크',
      ar: 'معيار التقييم',
      fr: "Référence d'évaluation",
      de: 'Evaluierungsbenchmark',
    },
  },
  {
    音频分析: {
      cn: '音频分析',
      en: 'Audio Analysis',
      ja: 'オーディオ分析',
      kr: '오디오 분석',
      ar: 'تحليل الصوت',
      fr: 'Analyse audio',
      de: 'Audioanalyse',
    },
  },
  {
    艺术设计: {
      cn: '艺术设计',
      en: 'Art Design',
      ja: 'アートデザイン',
      kr: '예술 디자인',
      ar: 'التصميم الفني',
      fr: 'Design artistique',
      de: 'Kunstdesign',
    },
  },
  {
    医学影像: {
      cn: '医学影像',
      en: 'Medical Imaging',
      ja: '医用画像',
      kr: '의료 영상',
      ar: 'التصوير الطبي',
      fr: 'Imagerie médicale',
      de: 'Medizinische Bildgebung',
    },
  },
  {
    图像分割: {
      cn: '图像分割',
      en: 'Image Segmentation',
      ja: '画像セグメンテーション',
      kr: '이미지 분할',
      ar: 'تجزئة الصور',
      fr: "Segmentation d'image",
      de: 'Bildsegmentierung',
    },
  },
  {
    音乐信息检索: {
      cn: '音乐信息检索',
      en: 'Music Information Retrieval',
      ja: '音楽情報検索',
      kr: '음악 정보 검색',
      ar: 'استرجاع معلومات الموسيقى',
      fr: "Récupération d'informations musicales",
      de: 'Musikinformationsabruf',
    },
  },
  {
    高分辨率: {
      cn: '高分辨率',
      en: 'High Resolution',
      ja: '高解像度',
      kr: '고해상도',
      ar: 'دقة عالية',
      fr: 'Haute résolution',
      de: 'Hohe Auflösung',
    },
  },
  {
    音频识别: {
      cn: '音频识别',
      en: 'Audio Recognition',
      ja: 'オーディオ認識',
      kr: '오디오 인식',
      ar: 'التعرف على الصوت',
      fr: 'Reconnaissance audio',
      de: 'Audioerkennung',
    },
  },
  {
    蛋白质: {
      cn: '蛋白质',
      en: 'Protein',
      ja: 'タンパク質',
      kr: '단백질',
      ar: 'بروتين',
      fr: 'Protéine',
      de: 'Protein',
    },
  },
  {
    情感识别: {
      cn: '情感识别',
      en: 'Emotion Recognition',
      ja: '感情認識',
      kr: '감정 인식',
      ar: 'التعرف على المشاعر',
      fr: 'Reconnaissance des émotions',
      de: 'Emotionserkennung',
    },
  },
  {
    智能问答: {
      cn: '智能问答',
      en: 'Intelligent Q&A',
      ja: 'インテリジェントQ&A',
      kr: '지능형 Q&A',
      ar: 'الأسئلة والأجوبة الذكية',
      fr: 'Q&R intelligent',
      de: 'Intelligente Fragen und Antworten',
    },
  },
]
