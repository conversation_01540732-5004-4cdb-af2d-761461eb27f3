@import 'tailwindcss';
@import 'tw-animate-css';

/* Need migration */
@plugin "@tailwindcss/typography";

@import './kladewind.css';
@import './schemes.css';

/* https://tailwindcss.com/docs/dark-mode#toggling-dark-mode-manually */
@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

@theme inline static {
  /* --font-logo: var(--font-syne), var(--font-suffix); */
  --font-logo: var(--font-space-grotesk), var(--font-suffix);
  --font-title: var(--font-space-grotesk), var(--font-sans);
}

@layer base {
  /*
    The default border color has changed to `currentColor` in Tailwind CSS v4,
    so we've added these compatibility styles to make sure everything still
    looks the same as it did with Tailwind CSS v3.

    If we ever want to remove these styles, we need to add an explicit border
    color utility to any element that depends on these defaults.
  */
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: color-mix(in oklab, var(--color-fg) 20%, transparent);
  }

  html,
  body {
    color: var(--color-fg);
    background: var(--color-bg);

    font-size: 16px;
    padding: 0;
    margin: 0;
    font-family: var(--font-sans);

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-title);
    font-weight: bold;
    letter-spacing: -0.0125em;
  }

  h1 {
    font-size: 2.4rem;
  }
  h2 {
    font-size: 1.8rem;
  }
  h3 {
    font-size: 1.6rem;
  }
  h4 {
    font-size: 1.4rem;
  }
  h5 {
    font-size: 1.2rem;
  }
  h6 {
    font-size: 1.1rem;
  }

  pre {
    overflow-x: auto;
    padding: 0.5rem;
    background-color: --theme(--color-amber-500 / 10%);
  }

  code {
    color: var(--color-amber-500);
    word-break: break-word;
  }

  summary {
    margin: 0.25em 0;
    cursor: pointer;
  }

  .blockquote,
  blockquote {
    font-family: var(--font-sans-serif);
  }

  hr {
    border: none;
    border-top: 1px solid --theme(--color-fg / 20%);
  }
}

@layer utilities {
  /* Tailwind Typography */
  /* https://github.com/tailwindlabs/tailwindcss-typography#adding-custom-color-themes */
  .prose {
    --tw-prose-body: var(--color-fg);
    --tw-prose-headings: var(--color-fg);
    --tw-prose-lead: var(--color-fg);
    --tw-prose-links: var(--color-ac);
    --tw-prose-bold: var(--color-fg);
    --tw-prose-counters: color-mix(in oklch, var(--color-ac) 40%, transparent);
    --tw-prose-bullets: color-mix(in oklch, var(--color-ac) 40%, transparent);
    --tw-prose-hr: color-mix(in oklch, var(--color-fg) 20%, transparent);
    --tw-prose-quotes: var(--color-ac);
    --tw-prose-quote-borders: color-mix(in oklch, var(--color-ac) 40%, transparent);
    --tw-prose-captions: var(--color-fg);
    --tw-prose-code: var(--color-orange-500);
    --tw-prose-pre-code: var(--color-orange-500);
    --tw-prose-pre-bg: color-mix(in oklch, var(--color-orange-500) 10%, transparent);
    --tw-prose-th-borders: color-mix(in oklch, var(--color-fg) 20%, transparent);
    --tw-prose-td-borders: color-mix(in oklch, var(--color-fg) 20%, transparent);
    /* --tw-prose-invert-body: var(--color-pink-200);
      --tw-prose-invert-headings: var(--color-white);
      --tw-prose-invert-lead: var(--color-pink-300);
      --tw-prose-invert-links: var(--color-white);
      --tw-prose-invert-bold: var(--color-white);
      --tw-prose-invert-counters: var(--color-pink-400);
      --tw-prose-invert-bullets: var(--color-pink-500);
      --tw-prose-invert-hr: var(--color-fg);
      --tw-prose-invert-quotes: var(--color-pink-100);
      --tw-prose-invert-quote-borders: var(--color-fg);
      --tw-prose-invert-captions: var(--color-pink-400);
      --tw-prose-invert-code: var(--color-white);
      --tw-prose-invert-pre-code: var(--color-pink-300);
      --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
      --tw-prose-invert-th-borders: var(--color-pink-500);
      --tw-prose-invert-td-borders: var(--color-fg); */
  }
}

html {
  --font-suffix: var(--font-sans);

  &[lang='cn'] {
    --font-suffix:
      ui-sans-serif, system-ui, 'Helvetica Neue', 'Lantinghei SC', Arial, sans-serif, 'Apple Color Emoji',
      'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  &[lang='ja'] {
    --font-suffix:
      ui-sans-serif, system-ui, 'Helvetica Neue', 'Hiragino Sans', Arial, sans-serif, 'Apple Color Emoji',
      'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }
}

a {
  text-decoration: none;

  &:hover,
  &:focus {
    text-decoration: none;
  }
}

figure {
  figcaption {
    &.wp-element-caption {
      color: color-mix(in oklch, var(--color-fg) 60%, transparent);
      text-align: center;
    }
  }
}

.link {
  color: var(--color-ac);

  /* &:hover,
  &:focus {
  } */
}

/* h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: bold;
  margin-top: 2rem;
  margin-bottom: 1rem;
} */

.latex {
  margin: 0 !important;
  vertical-align: middle;
}

/* .masonryWrap {
  margin-left: -.5em;
  margin-right: -.5em;
} */

.masonryItemWrap {
  width: 33.3333333333%;
  padding: 0.5em;

  /* $mantine-breakpoint-md */
  @media (max-width: 62em) {
    width: 50%;
  }

  /* $mantine-breakpoint-xs */
  @media (max-width: 36em) {
    width: 100%;
  }
}

.tagsWrapper {
  flex-wrap: wrap;

  @media (max-width: 767px) {
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    &::after {
      position: absolute;
      content: '';
      top: 0;
      right: 0;
      bottom: 0;
      width: 2em;
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
      pointer-events: none;
    }
  }

  .tagItem {
    flex-shrink: 0;
  }
}

.masonryItemWrap {
  width: 50%;
}

.wp-block-image {
  margin-bottom: 1rem;

  /* Override Tailwind prose defaults */
  figure {
    margin: 0;

    img {
      margin: 0;
      margin-right: 0.5rem;
    }
  }

  .alignleft,
  &.alignleft {
    float: left;
  }

  .aligncenter,
  &.aligncenter {
    /* Tailwind make img display block by default so text-align does not work */
    /* Ref: https://tailwindcss.com/docs/preflight#images-are-block-level */
    text-align: center;

    img {
      margin-left: auto;
      margin-right: auto;
    }
  }

  /* This does not work and we dont use it actually so leave it for reference */
  .alignright,
  &.alignright {
    text-align: right;
  }
}

.wwads-horizontal,
.wwads-vertical {
  background: transparent !important;
  padding: 0 !important;
  gap: 0.5em !important;
  min-height: 0 !important;
}

.wwads-img {
  margin: 0 !important;
}

.wwads-horizontal .wwads-content,
.wwads-horizontal .wwads-img,
.wwads-vertical .wwads-content,
.wwads-vertical .wwads-img {
  margin: 0 !important;
}
