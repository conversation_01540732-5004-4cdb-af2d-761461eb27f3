/* Elasticsearch UI Custom Styles */

/* Search Box */
.sui-search-box__wrapper {
  @apply w-full;
}

.sui-search-box__text-input {
  @apply border-input focus:ring-primary w-full rounded-md border px-4 py-2 focus:ring-2 focus:outline-none;
}

.sui-search-box__submit {
  @apply hidden;
}

/* Autocomplete */
.sui-search-box__autocomplete-container {
  @apply bg-background border-border absolute z-50 mt-1 w-full rounded-md border shadow-lg;
}

.sui-search-box__suggestion-list {
  @apply py-2;
}

.sui-search-box__suggestion {
  @apply hover:bg-muted cursor-pointer px-4 py-2;
}

/* Facets */
.sui-facet {
  @apply space-y-2;
}

.sui-facet__title {
  @apply hidden;
}

.sui-multi-checkbox-facet__option {
  @apply flex items-center space-x-2 py-1;
}

.sui-multi-checkbox-facet__checkbox {
  @apply text-primary focus:ring-primary h-4 w-4 rounded border-gray-300;
}

.sui-multi-checkbox-facet__option-label {
  @apply text-sm;
}

.sui-multi-checkbox-facet__option-count {
  @apply text-muted-foreground ml-auto text-xs;
}

/* Sorting */
.sui-sorting {
  @apply w-full;
}

.sui-sorting__label {
  @apply mb-2 block text-sm font-medium;
}

.sui-select {
  @apply border-input focus:ring-primary w-full rounded-md border px-3 py-2 focus:ring-2 focus:outline-none;
}

/* Results */
.sui-results-container {
  @apply space-y-4;
}

.sui-result {
  @apply rounded-lg border p-4 transition-shadow hover:shadow-md;
}

/* Paging */
.sui-paging {
  @apply flex justify-center space-x-2;
}

.sui-paging__item {
  @apply hover:bg-muted rounded border px-3 py-1;
}

.sui-paging__item--current {
  @apply bg-primary text-primary-foreground;
}

/* Paging Info */
.sui-paging-info {
  @apply text-muted-foreground text-sm;
}

/* Results Per Page */
.sui-results-per-page {
  @apply flex items-center space-x-2;
}

.sui-results-per-page__label {
  @apply text-sm;
}

/* Loading */
.sui-loading {
  @apply flex justify-center py-8;
}

/* Error */
.sui-error {
  @apply text-destructive;
}
