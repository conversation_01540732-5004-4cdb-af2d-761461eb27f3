import { ES_BASE_URL, ES_SEARCH_INDEX_NAME } from '@/lib/constants'

// Use environment variable for API key, fallback to the existing key temporarily
export const ES_API_KEY =
  process.env.NEXT_PUBLIC_ES_API_KEY || 'N2MyM2FaY0JTTjNIY20tNFRndkw6WlFmZGVNa3g4aDR3VW5xaWZ2Z2Nidw=='

export const getIndexName = (lang: string, suffix?: string) => {
  const baseIndex = `${ES_SEARCH_INDEX_NAME}${lang}`
  return suffix ? `${baseIndex}_${suffix}` : baseIndex
}

export const ES_CONFIG = {
  host: ES_BASE_URL,
  apiKey: ES_API_KEY,
  resultsPerPage: {
    options: [10, 20, 50],
    default: 20,
  },
  searchFields: {
    title: { weight: 3 },
    abstract: { weight: 2 },
    content: { weight: 1.5 },
    tags: { weight: 2 },
    full_text: { weight: 1 },
  },
  autocomplete: {
    minChars: 2,
    debounceLength: 300,
    resultsPerPage: 5,
  },
}
