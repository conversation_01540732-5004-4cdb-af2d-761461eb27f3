import type { components } from '@/types/scraper'

import { defaultLocale } from '@/lib/locales'

// Export type aliases for use in components
export type SotaResponse = components['schemas']['SotaResponse']
export type BenchmarkResponse = components['schemas']['BenchmarkResponse']
export type TaskInfoResponse = components['schemas']['TaskInfoResponse']
export type CategoryResponse = components['schemas']['CategoryResponse']
export type GPUSoftwareBenchmarkResponse = components['schemas']['GPUSoftwareBenchmarkResponse']
export type PapersResponse = components['schemas']['PapersResponse']
export type NewsResponse = components['schemas']['NewsResponse']
export type NewsTopicResponse = components['schemas']['NewsTopicResponse']
export type PaperDetailResponse = components['schemas']['PaperDetailResponse']
export type EvaluationItem = components['schemas']['EvaluationItem']
export type TaskItem = components['schemas']['TaskItem']
export type BenchmarkResult = components['schemas']['BenchmarkResult']
export type EvaluationBenchmarkItem = components['schemas']['EvaluationBenchmarkItem']
export type SotaAllCategory = components['schemas']['SotaAllCategory']

// Define the base URL for the scraper API
const SCRAPER_API_BASE_URL = 'https://web-api.hyper.ai'

/**
 * Generic fetch function for the HyperAI Scraper API
 */
async function fetchScraperAPI<T>(
  path: string,
  options: {
    locale?: string
    revalidate?: number
  } = {}
): Promise<T> {
  const { locale = defaultLocale, revalidate = 600 } = options
  // const lang = locale === 'cn' ? 'cn' : 'en' // Map locales to supported languages (cn, en, ja, fr)
  const lang = locale

  // Construct the full URL with the language parameter
  const reqUrl = `${SCRAPER_API_BASE_URL}/v1/${lang}${path}`

  if (process.env.NODE_ENV === 'development') {
    console.log(`Scraper API request:`, reqUrl)
  }

  const res = await fetch(reqUrl, {
    next: {
      revalidate,
    },
  })

  if (!res.ok) {
    throw new Error(`API error: ${res.status} ${res.statusText}`)
  }

  const data = await res.json()

  if (data.r !== 200) {
    console.error('API returned error:', data)
    throw new Error('Failed to fetch from Scraper API')
  }

  return data
}

/**
 * Fetch recent AI news
 */
export async function fetchNews(options: {
  locale?: string
  page_num?: number
  page_size?: number
  sticky_num?: number
}): Promise<components['schemas']['NewsResponse']> {
  const { locale, page_num = 1, page_size = 10, sticky_num = 3 } = options
  return fetchScraperAPI<components['schemas']['NewsResponse']>(
    `/news/fetch?page_num=${page_num}&page_size=${page_size}&sticky_num=${sticky_num}`,
    { locale, revalidate: 60 }
  )
}

/**
 * Fetch news topic details
 */
export async function fetchNewsTopic(
  topic_id: string,
  options: { locale?: string }
): Promise<components['schemas']['NewsTopicResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['NewsTopicResponse']>(`/news/topic/${topic_id}`, {
    locale,
    revalidate: 60,
  })
}

/**
 * Fetch latest papers from the last 24 hours
 */
export async function fetchLatestPapers(options: {
  locale?: string
}): Promise<components['schemas']['PapersResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['PapersResponse']>(`/papers/latest`, {
    locale,
    revalidate: 60,
  })
}

/**
 * Fetch papers with pagination
 */
export async function fetchPaginatedPapers(options: {
  locale?: string
  page_num?: number
  page_size?: number
}): Promise<components['schemas']['PapersResponse']> {
  const { locale, page_num = 1, page_size = 10 } = options
  return fetchScraperAPI<components['schemas']['PapersResponse']>(
    `/papers/fetch?page_num=${page_num}&page_size=${page_size}`,
    { locale, revalidate: 60 }
  )
}

/**
 * Fetch papers by date
 */
export async function fetchPapersByDate(
  data_timestamp: number,
  options: { locale?: string }
): Promise<components['schemas']['PapersResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['PapersResponse']>(`/papers/date/${data_timestamp}`, { locale })
}

/**
 * Fetch paper details
 */
export async function fetchPaperDetail(
  paper_id: string,
  options: { locale?: string }
): Promise<components['schemas']['PaperDetailResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['PaperDetailResponse']>(`/papers/${paper_id}`, { locale })
}

/**
 * Fetch all SOTA categories
 */
export async function fetchAllSota(options: { locale?: string }): Promise<components['schemas']['SotaResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['SotaResponse']>(`/sota`, { locale })
}

/**
 * Fetch category details with all tasks
 */
export async function fetchSotaCategory(
  area_id: string,
  options: { locale?: string }
): Promise<components['schemas']['CategoryResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['CategoryResponse']>(`/category/${area_id}/all`, { locale })
}

/**
 * Fetch task information
 */
export async function fetchTaskInfo(
  task_id: string,
  options: { locale?: string }
): Promise<components['schemas']['TaskInfoResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['TaskInfoResponse']>(`/task/${task_id}/info`, { locale })
}

/**
 * Fetch task benchmark details
 */
export async function fetchTaskBench(
  task_id: string,
  evaluation_id: string,
  options: { locale?: string }
): Promise<components['schemas']['BenchmarkResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['BenchmarkResponse']>(`/task/${task_id}/benchmark/${evaluation_id}`, {
    locale,
  })
}

/**
 * Fetch GPU software benchmark information
 */
export async function fetchGpuSoftwareBenchmark(options: {
  locale?: string
}): Promise<components['schemas']['GPUSoftwareBenchmarkResponse']> {
  const { locale } = options
  return fetchScraperAPI<components['schemas']['GPUSoftwareBenchmarkResponse']>(`/benchmark/software`, { locale })
}
