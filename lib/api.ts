import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostTypeSlug, RecentPostsProps } from '@/types'

import { defaultLocale } from '@/lib/locales'

import { WP_API_BASE } from './constants'

async function fetchAPI<T = HyperApiPost[]>(
  path = '',
  options: {
    locale?: string
    revalidate?: number
  }
): Promise<T> {
  const { locale, revalidate } = options
  const resolvedApiPrefix = locale && locale !== defaultLocale ? `/${locale}` : ''

  // signed media cache expires in 1 hour, which is defined in `hyperai-wordpress-intruder` project
  const resolvedRevalidate = revalidate || (process.env.NODE_ENV === 'development' ? 5 : 3600)

  // Fetch data from external API
  const reqUrl = `${WP_API_BASE}${resolvedApiPrefix}${path}`

  if (process.env.NODE_ENV === 'development') {
    console.log(`reqUrl`, reqUrl)
  }

  const res = await fetch(reqUrl, {
    next: {
      revalidate: resolvedRevalidate,
    },
  })
  const data = await res.json()

  if (data.errors) {
    console.error(data.errors)
    throw new Error('Failed to fetch API')
  }
  return data
}

export async function getNextRuntime() {
  const runtime = process.env['NEXT_RUNTIME'] || ''

  return runtime
}

export async function getPageById(
  id: number,
  options: {
    locale?: string
  }
) {
  const { locale } = options
  const data = await fetchAPI(`/wp-json/wp/v2/pages/${id}`, { locale })

  return data
}

export async function getPostById<T = HyperApiPost>(
  id: number,
  options?: {
    type?: PostTypeSlug
    locale?: string
    revalidate?: number
  }
) {
  const { type = 'posts', locale } = options ?? {}
  const data = await fetchAPI<T>(`/wp-json/wp/v2/${type}/${id}?_embed&acf_format=standard`, {
    locale,
    revalidate: 3600,
  })

  return data
}

export async function getRecentPosts({ exclude, type = 'posts', page = 1, per_page = 3, locale }: RecentPostsProps) {
  // Limit max page to avoid sql attack
  const perPageLimit = per_page > 20 ? 20 : per_page
  const data = await fetchAPI(
    `/wp-json/wp/v2/${type}?exclude=${exclude || ''}&per_page=${perPageLimit || 3}&page=${page || 1}&_embed`,
    { locale }
  )

  return data
}

/**
 * Get related posts from YARPP
 * @param param0
 * @returns `HyperApiPost[]`
 * @deprecated Use `useRelatedPosts` instead
 */
export async function getRelatedPosts({ id, limit = 3, locale }: { id: number; limit?: number; locale?: string }) {
  // Limit max page to avoid sql attack
  const pageLimit = limit > 20 ? 20 : limit
  const data = await fetchAPI(`/yarpp/v1/related/${id}?limit=${pageLimit}`, { locale })

  return data
}

export async function getPageByName(
  name: string,
  options: {
    locale?: string
  }
) {
  const { locale } = options

  const data = await fetchAPI(`/wp/v2/pages/?slug=${name}`, { locale })

  if (data && data.length > 0) {
    return data[0]
  } else {
    return {
      code: 'rest_page_invalid_slug',
      message: '文章 slug 无效。',
      data: {
        status: 404,
      },
    }
  }
}
