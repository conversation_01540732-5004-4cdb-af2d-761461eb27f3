import { NextRequest, NextResponse } from 'next/server'

// Simple in-memory rate limiter (use Redis in production)
const requestCounts = new Map<string, { count: number; resetTime: number }>()

export interface RateLimitOptions {
  windowMs?: number // Time window in milliseconds
  max?: number // Max requests per window
}

export function rateLimit(options: RateLimitOptions = {}) {
  const { windowMs = 60 * 1000, max = 30 } = options // Default: 30 requests per minute

  return async (request: NextRequest) => {
    // Get client identifier (IP or fallback to a default)
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'anonymous'

    const now = Date.now()
    const resetTime = now + windowMs

    // Get or create rate limit entry
    let entry = requestCounts.get(ip)

    if (!entry || now > entry.resetTime) {
      entry = { count: 0, resetTime }
      requestCounts.set(ip, entry)
    }

    entry.count++

    // Clean up old entries
    if (requestCounts.size > 1000) {
      for (const [key, value] of requestCounts.entries()) {
        if (now > value.resetTime) {
          requestCounts.delete(key)
        }
      }
    }

    // Check if rate limit exceeded
    if (entry.count > max) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': max.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString(),
          },
        }
      )
    }

    // Add rate limit headers
    const headers = {
      'X-RateLimit-Limit': max.toString(),
      'X-RateLimit-Remaining': (max - entry.count).toString(),
      'X-RateLimit-Reset': entry.resetTime.toString(),
    }

    return { headers }
  }
}
