'use client'

import type { Locale } from '@/types'

// Client-side dictionaries - pre-loaded for better performance
import ar from '../app/[lang]/dictionaries/ar.ts'
import cn from '../app/[lang]/dictionaries/cn.ts'
import de from '../app/[lang]/dictionaries/de.ts'
import en from '../app/[lang]/dictionaries/en.ts'
import fr from '../app/[lang]/dictionaries/fr.ts'
import ja from '../app/[lang]/dictionaries/ja.ts'
import kr from '../app/[lang]/dictionaries/kr.ts'

const dictionaries = {
  en,
  cn,
  ja,
  kr,
  ar,
  fr,
  de,
}

export const getClientDictionary = (locale: Locale) => dictionaries[locale]
