import { ProjectDocument, UserDatasetModelQueryDocument } from '@/generated/graphql'

export type ConsoleOperationName = keyof typeof ConsoleOperationNameMap

export const ConsoleOperationNameMap = {
  /**
   * ie. https://openbayes.com/console/hyperai-tutorials/containers/vQ5bubpXRzb
   */
  containers: ProjectDocument,
  /**
   * ie. https://openbayes.com/console/public/tutorials/m6k2bdSu30C
   */
  tutorials: ProjectDocument,
  datasets: UserDatasetModelQueryDocument,
  models: UserDatasetModelQueryDocument,
}

export const ConsoleObjectMap = {
  containers: 'project',
  tutorials: 'project',
  datasets: 'dataset',
  models: 'dataset',
}
