// https://stackoverflow.com/a/76760064/412385
import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'

export const GA_TRACKING_ID = process.env['NEXT_PUBLIC_GA_ID']

// https://developers.google.com/analytics/devguides/collection/gtagjs/pages
export const pageview = (url: URL) => {
  window.gtag('config', 'G-YY2E0ZQRP8', {
    page_path: url,
  })
}

// https://developers.google.com/analytics/devguides/collection/gtagjs/events
export const event = (action: Gtag.EventNames, { event_category, event_label, value }: Gtag.EventParams) => {
  window.gtag('event', action, {
    event_category,
    event_label,
    value,
  })
}

export const useGtag = () => {
  const pathname = usePathname() // Get current route

  // Save pathname on component mount into a REF
  const savedPathNameRef = useRef(pathname)

  useEffect(() => {
    const handleRouteChange = (url: URL) => {
      pageview(url)
    }

    if (pathname && savedPathNameRef.current !== pathname) {
      handleRouteChange(new URL(pathname, window.location.origin))
      // Update REF
      savedPathNameRef.current = pathname
    }
  }, [pathname])
}
