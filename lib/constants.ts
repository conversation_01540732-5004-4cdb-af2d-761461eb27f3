export const BASE_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:3001' : 'https://hyper.ai'

// WordPress API
export const WP_API_BASE = 'https://orion.hyper.ai'
export const WP_FIELDS_POST_LIST = `date,date_gmt,excerpt,hyperai_categories,hyperai_tags,id,link,modified,modified_gmt,title,_links.wp:featuredmedia`
export const WP_FIELDS_DATASET_LIST = `${WP_FIELDS_POST_LIST},hyperai_organizations`
export const WP_FIELDS_EVENT_LIST = `${WP_FIELDS_POST_LIST},acf`

// Next.js API routes (proxies)
export const API_PROXY_BASE = '/api/orion'

// OpenBayes API
export const OB_API_BASE = 'https://openbayes.com/gateway/graphql'

// S3 Static Assets
export const S3_ASSETS_BASE = 'https://hyperai.s3.cn-north-1.amazonaws.com.cn/media'

// Edge fetcher
export const WORKERS_BASE = 'https://workers.hyper.ai'

// ElasticSearch
export const ES_BASE_URL = 'https://web-es.hyper.ai'
export const ES_SEARCH_INDEX_NAME = 'search_hyperai_'

// Algolia
export const ALGOLIA_PROXY_BASE = 'experiments-hk.sparanoid.net/stwak05fuv-dsn.algolia.net'
export const INSTANT_SEARCH_INDEX_NAME = 'wp_searchable_posts'
export const INSTANT_SEARCH_QUERY_SUGGESTIONS = 'wp_searchable_posts'
export const INSTANT_SEARCH_HIERARCHICAL_ATTRIBUTES = [
  // 'hierarchicalCategories.lvl0',
  // 'hierarchicalCategories.lvl1',
  'taxonomies_hierarchical.category.lvl0',
]
