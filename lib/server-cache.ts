import { cache } from 'react'

// inspired by: https://github.com/manvalls/server-only-context
// info: https://github.com/vercel/next.js/discussions/51818
export const serverCache = <T>(defaultValue: T): [() => T, (v: T) => void] => {
  const getRef = cache(() => ({ current: defaultValue }))

  const getValue = (): T => getRef().current

  const setValue = (value: T) => {
    getRef().current = value
  }

  return [getValue, setValue]
}
