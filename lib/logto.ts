// libraries/logto.js
// it's weird that LogtoNextConfig is not exported from @logto/next/edge
import { type LogtoNextConfig } from '@logto/next'
import LogtoClient from '@logto/next/edge'

// https://github.com/logto-io/js/blob/master/packages/next-app-dir-sample/libraries/config.ts
export const logtoConfig: LogtoNextConfig = {
  appId: process.env['LOGTO_APP_ID'] ?? '',
  appSecret: process.env['LOGTO_APP_SECRET'] ?? '',
  endpoint: process.env['LOGTO_ENDPOINT'] ?? '',
  baseUrl: process.env['LOGTO_BASE_URL'] ?? 'http://localhost:3000',
  cookieSecret: process.env['LOGTO_COOKIE_SECRET'] ?? '',
  cookieSecure: process.env['NODE_ENV'] === 'production',
  // Optional fields for RBAC
  resources: process.env['LOGTO_RESOURCES']?.split(','),
  scopes: process.env['LOGTO_SCOPES']?.split(','),
}

export const logtoClient = new LogtoClient(logtoConfig)
