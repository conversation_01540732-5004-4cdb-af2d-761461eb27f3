import type { PostTypeSlug } from '@/types'

import { BASE_URL, WP_API_BASE } from '@/lib/constants'

export const dynamic = 'force-dynamic'

export async function generateSitemapItems(
  type: PostTypeSlug,
  /** 传入的 sitemap 分页 index */
  idx: number,
  options?: {
    /** @default 100 */
    limit?: number
    /** 给前端用的 slug，与 wordpress 的 post type 不同，通常是给 news (posts) 用的 */
    slug?: string
  }
) {
  const limit = options?.limit || 100
  const slug = options?.slug || type

  // Calculate the starting page (assuming 100 items per page from the API)
  const startPage = (idx * limit) / 100

  // Number of pages to fetch for this sitemap
  const totalPages = Math.ceil(limit / 100)

  const posts = []

  // Fetch products from the API, one page at a time
  for (let page = startPage + 1; page <= startPage + totalPages; page++) {
    const url = `${WP_API_BASE}/wp-json/wp/v2/${type}?page=${page}&per_page=100&_fields=date_gmt%2Cid%2Cmodified_gmt`
    const resp = await fetch(url)
    const data = await resp.json()
    posts.push(...data)
  }

  // Generate sitemap entries from fetched products
  return posts.map((post: { id: number; modified_gmt: string }) => ({
    url: `${BASE_URL}/cn/${slug}/${post.id}`,
    lastModified: new Date(post.modified_gmt).toISOString() || new Date().toISOString(),
    alternates: {
      languages: {
        // TODO: after the site is fully migrated, rename the /cn to /en
        en: `${BASE_URL}/en/${slug}/${post.id}`,
        ja: `${BASE_URL}/ja/${slug}/${post.id}`,
        kr: `${BASE_URL}/kr/${slug}/${post.id}`,
        ar: `${BASE_URL}/ar/${slug}/${post.id}`,
        fr: `${BASE_URL}/fr/${slug}/${post.id}`,
        de: `${BASE_URL}/de/${slug}/${post.id}`,
      },
    },
  }))
}
