import type { PostTypeSlug } from '@/types'

import { WP_API_BASE } from '@/lib/constants'

export const dynamic = 'force-dynamic'

export async function calculateSitemapIndex(
  type: PostTypeSlug,
  options?: {
    /** @default 100 */
    limit?: number
  }
) {
  // Get the total number of products
  const limit = options?.limit || 100
  const url = `${WP_API_BASE}/wp-json/wp/v2/${type}?per_page=1&_fields=id`
  const resp = await fetch(url)
  const totalPosts = parseInt(resp.headers.get('X-WP-Total') ?? '0', 10)
  const totalSitemaps = Math.ceil(totalPosts / limit)
  const arr = Array.from({ length: totalSitemaps }, (_, index) => ({ id: index }))

  return arr

  // Fetch the total number of products and calculate the number of sitemaps needed
  // return [{ id: 0 }, { id: 1 }, { id: 2 }, { id: 3 }]
}
