// https://nextjs.org/docs/app/building-your-application/routing/internationalization

import type { Locale } from '@/types'

import 'server-only'

const dictionaries = {
  en: () => import('../app/[lang]/dictionaries/en').then(module => module.default),
  cn: () => import('../app/[lang]/dictionaries/cn').then(module => module.default),
  ja: () => import('../app/[lang]/dictionaries/ja').then(module => module.default),
  kr: () => import('../app/[lang]/dictionaries/kr').then(module => module.default),
  ar: () => import('../app/[lang]/dictionaries/ar').then(module => module.default),
  fr: () => import('../app/[lang]/dictionaries/fr').then(module => module.default),
  de: () => import('../app/[lang]/dictionaries/de').then(module => module.default),
}

export const getDictionary = async (locale: Locale) => dictionaries[locale]()

// Export the dictionary type for easier usage in components
export type Dictionary = Awaited<ReturnType<typeof getDictionary>>
