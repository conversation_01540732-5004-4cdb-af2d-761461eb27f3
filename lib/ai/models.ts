import type { ModelWithMeta } from '@/types/ai'

/**
 * Model nodes
 */
export const customModels: ModelWithMeta[] = [
  // Perplexity
  {
    id: 'perplexity/sonar',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'perplexity',
    tags: ['free'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 1,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'perplexity/sonar-pro',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'perplexity',
    tags: ['pro'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
  },
  {
    id: 'openrouter/sonar-reasoning',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'perplexity',
    provider_id: 'perplexity/sonar-reasoning',
    tags: ['free'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'perplexity/sonar-reasoning-pro',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'perplexity',
    tags: ['free'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'perplexity/sonar-deep-research',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'perplexity',
    tags: ['pro'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'perplexity/r1-1776',
    created: Date.now(),
    // description: '内置联网检索功能',
    object: 'model',
    owned_by: 'deepseek',
    tags: ['pro'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  // {
  //   id: 'perplexity/codellama-70b-instruct',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'meta',
  //   tags: ['enterprise', 'deprecated'],
  //   links: {
  //     website: 'https://docs.perplexity.ai/docs/getting-starte/',
  //     pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
  //     modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
  //   },
  //   pricing: {
  //     inputCostPerMil: 1,
  //     outputCostPerMil: 1,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 16384,
  //       range: [0, 4096],
  //     },
  //   },
  // },

  // OpenAI
  // {
  //   id: 'azure/gpt-3.5-turbo',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'openai',
  //   tags: ['free', 'deprecated'],
  //   // https://platform.openai.com/docs/models/gpt-3-5-turbo
  //   description: '该模型目前指向 gpt-3.5-turbo-0125',
  //   links: {
  //     website: 'https://openai.com/',
  //     pricingUrl: 'https://openai.com/pricing',
  //     modelUrl: 'https://platform.openai.com/docs/models/gpt-3-5-turbo',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.5,
  //     outputCostPerMil: 1.5,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 16385,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     tool_calling: true,
  //     file_uploading: true,
  //   },
  // },
  // {
  //   id: 'openrouter/gpt-4-turbo',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'openai',
  //   // hosted_by: 'azure',
  //   provider_id: 'openai/gpt-4-turbo',
  //   // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
  //   description: '该模型目前指向 gpt-4-turbo-2024-04-09',
  //   tags: ['enterprise', 'deprecated'],
  //   links: {
  //     website: 'https://openai.com/',
  //     pricingUrl: 'https://openai.com/pricing',
  //     modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
  //   },
  //   pricing: {
  //     inputCostPerMil: 10,
  //     outputCostPerMil: 30,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 128000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     tool_calling: true,
  //     file_uploading: true,
  //     vision: true,
  //   },
  // },
  {
    id: 'openrouter/gpt-4o',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4o',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 gpt-4o-2024-11-20',
    tags: ['pro'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 2.5,
      outputCostPerMil: 10.0,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  // {
  //   id: 'openai/gpt-4o',
  //   // id: 'openrouter/gpt-4o-search-preview',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'openai',
  //   // provider_id: 'openai/gpt-4o-search-preview',
  //   // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
  //   // description: '该模型目前指向 gpt-4o-2024-11-20',
  //   description: 'test',
  //   tags: ['pro'],
  //   links: {
  //     website: 'https://openai.com/',
  //     pricingUrl: 'https://openai.com/pricing',
  //     modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
  //   },
  //   pricing: {
  //     inputCostPerMil: 2.5,
  //     outputCostPerMil: 10.0,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 128000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     tool_calling: true,
  //     file_uploading: true,
  //     vision: true,
  //   },
  // },
  {
    id: 'openrouter/gpt-4o-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4o-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 gpt-4o-mini-2024-07-18',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 0.15,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  // used by healthcheck
  {
    id: 'openai/gpt-4o-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 gpt-4o-mini-2024-07-18',
    tags: ['free', 'hidden'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 0.15,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/gpt-4.5',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4.5-preview',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 gpt-4.5-preview',
    tags: ['enterprise', 'deprecated'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 75,
      outputCostPerMil: 150,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  // {
  //   id: 'openrouter/gpt-4',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'openai',
  //   provider_id: 'openai/gpt-4o',
  //   // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
  //   description: '该模型目前指向 gpt-4-0613',
  //   tags: ['enterprise', 'deprecated'],
  //   links: {
  //     website: 'https://openai.com/',
  //     pricingUrl: 'https://openai.com/pricing',
  //     modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
  //   },
  //   pricing: {
  //     inputCostPerMil: 30,
  //     outputCostPerMil: 60,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 8192,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     tool_calling: true,
  //     file_uploading: true,
  //   },
  // },
  {
    id: 'openrouter/o1',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o1',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 o1-2024-12-17',
    tags: ['enterprise'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 15,
      outputCostPerMil: 60,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/o1-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o1-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '该模型目前指向 o1-mini-2024-09-12',
    tags: ['pro'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 12,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/o3-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o3-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '该模型目前指向 o1-mini-2024-09-12',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 1.1,
      outputCostPerMil: 4.4,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/o3-mini-high',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o3-mini-high',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 1.1,
      outputCostPerMil: 4.4,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/o3',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o3',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['pro'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/o3-pro',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o3',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['enterprise'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 20,
      outputCostPerMil: 80,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/o4-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o4-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '该模型目前指向 o1-mini-2024-09-12',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 1.1,
      outputCostPerMil: 4.4,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openrouter/o4-mini-high',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/o4-mini-high',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    description: '与 o4-mini 相同，但 reasoning_effort 为 high',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 1.1,
      outputCostPerMil: 4.4,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
      // file_uploading: true,
      vision: true,
    },
  },
  {
    id: 'openai/o1-pro',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['enterprise', 'hidden'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 150,
      outputCostPerMil: 600,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 100000],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      reasoning: true,
      // vision: true,
    },
  },
  {
    id: 'openrouter/gpt-4.1',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4.1',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['pro'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 1047576,
        range: [0, 100000],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      // reasoning: true,
      // vision: true,
    },
  },
  {
    id: 'openrouter/gpt-4.1-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4.1-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 0.4,
      outputCostPerMil: 1.6,
    },
    parameters: {
      maximumLength: {
        value: 1047576,
        range: [0, 100000],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      // reasoning: true,
      // vision: true,
    },
  },
  {
    id: 'openrouter/gpt-4.1-nano',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/gpt-4.1-nano',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 0.1,
      outputCostPerMil: 0.4,
    },
    parameters: {
      maximumLength: {
        value: 1047576,
        range: [0, 100000],
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      // reasoning: true,
      // vision: true,
    },
  },
  {
    id: 'openrouter/codex-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'openai',
    provider_id: 'openai/codex-mini',
    // https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo
    // description: '与 o3-mini 相同，但 reasoning_effort 为 high',
    tags: ['free'],
    links: {
      website: 'https://openai.com/',
      pricingUrl: 'https://openai.com/pricing',
      modelUrl: 'https://platform.openai.com/docs/models/gpt-4-and-gpt-4-turbo',
    },
    pricing: {
      inputCostPerMil: 1.5,
      outputCostPerMil: 6,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 100000],
      },
    },
    features: {
      // tool_calling: true,
      // file_uploading: true,
      reasoning: true,
      // vision: true,
    },
  },

  // Tencent
  // https://console.cloud.tencent.com/cam/capi
  {
    id: 'qcloud/hunyuan-lite',
    object: 'model',
    created: Date.now(),
    owned_by: 'tencent',
    links: {
      website: 'https://hunyuan.tencent.com/',
      pricingUrl: 'https://cloud.tencent.com/document/product/1729/97731',
      modelUrl: 'https://cloud.tencent.com/document/product/1729',
    },
    tags: ['free'],
    parameters: {
      maximumLength: {
        value: 256000,
        range: [0, 250000],
      },
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'qcloud/hunyuan-role',
    object: 'model',
    created: Date.now(),
    owned_by: 'tencent',
    links: {
      website: 'https://hunyuan.tencent.com/',
      pricingUrl: 'https://cloud.tencent.com/document/product/1729/97731',
      modelUrl: 'https://cloud.tencent.com/document/product/1729',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 8,
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'qcloud/hunyuan-functioncall',
    object: 'model',
    created: Date.now(),
    owned_by: 'tencent',
    description: '然而工具调用并不工作，令人忍俊不禁',
    links: {
      website: 'https://hunyuan.tencent.com/',
      pricingUrl: 'https://cloud.tencent.com/document/product/1729/97731',
      modelUrl: 'https://cloud.tencent.com/document/product/1729',
    },
    tags: ['free'],
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 28000],
      },
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 8,
    },
    features: {
      // Does not work
      tool_calling: true,
    },
  },
  {
    id: 'qcloud/hunyuan-turbos-latest',
    object: 'model',
    created: Date.now(),
    owned_by: 'tencent',
    tags: ['free'],
    links: {
      website: 'https://hunyuan.tencent.com/',
      pricingUrl: 'https://cloud.tencent.com/document/product/1729/97731',
      modelUrl: 'https://cloud.tencent.com/document/product/1729',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.8,
      outputCostPerMil: 2,
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'qcloud/hunyuan-turbo-latest',
    object: 'model',
    created: Date.now(),
    owned_by: 'tencent',
    tags: ['free'],
    links: {
      website: 'https://hunyuan.tencent.com/',
      pricingUrl: 'https://cloud.tencent.com/document/product/1729/97731',
      modelUrl: 'https://cloud.tencent.com/document/product/1729',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 2.4,
      outputCostPerMil: 9.6,
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },

  // Mistral
  {
    id: 'mistral/mistral-small-latest',
    created: Date.now(),
    object: 'model',
    owned_by: 'mistral',
    links: {
      website: 'https://mistral.ai/',
      pricingUrl: 'https://docs.mistral.ai/platform/pricing/',
      modelUrl: 'https://mistral.ai/products/la-plateforme#pricing#models',
    },
    tags: ['free'],
    pricing: {
      inputCostPerMil: 0.1,
      outputCostPerMil: 0.3,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'mistral/mistral-large-latest',
    created: Date.now(),
    object: 'model',
    owned_by: 'mistral',
    description: '指向 mistral-large-2407',
    tags: ['free'],
    links: {
      website: 'https://mistral.ai/',
      pricingUrl: 'https://mistral.ai/products/la-plateforme#pricing',
      modelUrl: 'https://docs.mistral.ai/getting-started/models/',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 6,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },

  // Xiaosuan
  {
    id: 'litellm/xiaosuan-1',
    created: Date.now(),
    object: 'model',
    owned_by: 'xiaosuan',
    tags: ['free', 'hidden'],
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'litellm/xiaosuan-2',
    created: Date.now(),
    object: 'model',
    owned_by: 'xiaosuan',
    tags: ['free', 'hidden'],
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'thomas-2',
    created: Date.now(),
    object: 'model',
    owned_by: 'xiaosuan',
    tags: ['enterprise', 'hidden'],
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'thomas-1',
    created: Date.now(),
    object: 'model',
    owned_by: 'xiaosuan',
    tags: ['free', 'hidden'],
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },

  // Claude
  {
    id: 'openrouter/claude-2.1',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    tags: ['enterprise', 'deprecated', 'hidden'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://www-files.anthropic.com/production/images/model_pricing_july2023.pdf',
      modelUrl: 'https://www-files.anthropic.com/production/images/Model-Card-Claude-2.pdf',
    },
    pricing: {
      inputCostPerMil: 8.0,
      outputCostPerMil: 24.0,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    deprecated: true,
  },
  {
    id: 'openrouter/claude-3-sonnet',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-3-sonnet',
    tags: ['pro', 'deprecated'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'openrouter/claude-3-opus',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-3-opus',
    tags: ['enterprise', 'deprecated', 'hidden'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 15,
      outputCostPerMil: 75,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'openrouter/claude-3-haiku',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    tags: ['free', 'deprecated', 'hidden'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 0.25,
      outputCostPerMil: 1.25,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'openrouter/claude-3.5-sonnet',
    // aliases: ['anthropic/claude-3.5-sonnet'],
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-3.5-sonnet',
    tags: ['pro', 'deprecated'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      file_uploading: true,
      tool_calling: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'openrouter/claude-3.5-haiku',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-3.5-haiku',
    tags: ['free'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  // used by healthcheck
  {
    id: 'anthropic/claude-3.5-haiku',
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'claude-3-5-haiku-latest',
    tags: ['free', 'hidden'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'openrouter/claude-3.7-sonnet',
    // aliases: ['anthropic/claude-3.5-sonnet'],
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-3.7-sonnet',
    tags: ['pro'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      file_uploading: true,
      tool_calling: true,
      reasoning: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'openrouter/claude-4-sonnet',
    // aliases: ['anthropic/claude-3.5-sonnet'],
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-sonnet-4',
    tags: ['pro'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      file_uploading: true,
      tool_calling: true,
      reasoning: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'openrouter/claude-4-opus',
    // aliases: ['anthropic/claude-3.5-sonnet'],
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-opus-4',
    tags: ['enterprise', 'deprecated'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 15,
      outputCostPerMil: 75,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      file_uploading: true,
      tool_calling: true,
      reasoning: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'anthropic/claude-opus-4.1',
    // aliases: ['anthropic/claude-3.5-sonnet'],
    created: Date.now(),
    object: 'model',
    owned_by: 'anthropic',
    provider_id: 'anthropic/claude-opus-4.1',
    tags: ['enterprise'],
    links: {
      website: 'https://www.anthropic.com/',
      pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
      modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
    },
    pricing: {
      inputCostPerMil: 15,
      outputCostPerMil: 75,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 8192],
      },
    },
    features: {
      file_uploading: true,
      tool_calling: true,
      reasoning: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },

  // {
  //   id: 'openrouter/claude-3-opus',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'anthropic',
  //   tags: ['enterprise'],
  //   links: {
  //     website: 'https://www.anthropic.com/',
  //     pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
  //     modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
  //   },
  //   pricing: {
  //     inputCostPerMil: 15,
  //     outputCostPerMil: 75,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 200000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     file_uploading: true,
  //     // tool_calling: true,
  //   },
  // },
  // {
  //   id: 'openrouter/claude-3-haiku',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'anthropic',
  //   tags: ['free'],
  //   links: {
  //     website: 'https://www.anthropic.com/',
  //     pricingUrl: 'https://docs.anthropic.com/claude/docs/models-overview#model-comparison',
  //     modelUrl: 'https://docs.anthropic.com/claude/docs/models-overview',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.25,
  //     outputCostPerMil: 1.25,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 200000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     file_uploading: true,
  //   },
  // },

  // OpenRouter
  {
    id: 'openrouter/dbrx-instruct',
    created: Date.now(),
    object: 'model',
    owned_by: 'databricks',
    provider_id: 'databricks/dbrx-instruct',
    tags: ['free', 'broken', 'hidden'],
    links: {
      website: 'https://openrouter.ai/',
      pricingUrl: 'https://openrouter.ai/models/databricks/dbrx-instruct',
      modelUrl: 'https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm',
    },
    pricing: {
      inputCostPerMil: 0.6,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {},
  },

  {
    id: 'openrouter/llama-4-maverick',
    created: Date.now(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'meta-llama/llama-4-maverick',
    tags: ['free'],
    links: {
      website: 'https://openrouter.ai/',
      pricingUrl: 'https://openrouter.ai/meta-llama/llama-4-maverick',
      modelUrl: 'https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm',
    },
    pricing: {
      inputCostPerMil: 0.2,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 16384],
      },
    },
    features: {
      // tool_calling: true,
      file_uploading: true,
    },
  },

  {
    id: 'openrouter/llama-4-scout',
    created: Date.now(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'meta-llama/llama-4-scout',
    tags: ['free'],
    links: {
      website: 'https://openrouter.ai/',
      pricingUrl: 'https://openrouter.ai/meta-llama/llama-4-scout',
      modelUrl: 'https://www.databricks.com/blog/introducing-dbrx-new-state-art-open-llm',
    },
    pricing: {
      inputCostPerMil: 0.1,
      outputCostPerMil: 0.3,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 16384],
      },
    },
    features: {
      // tool_calling: true,
      file_uploading: true,
    },
  },

  // Google
  {
    id: 'google/gemma-3-27b',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    provider_id: 'gemma-3-27b-it',
    tags: ['free'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
        output: 4096,
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'google/gemini-1.5-flash',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    provider_id: 'gemini-1.5-flash-latest',
    description: '该模型指向 gemini-1.5-flash-latest',
    tags: ['free', 'deprecated'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 0.15,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 1048576],
        output: 8192,
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'google/gemini-1.5-pro',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    provider_id: 'gemini-1.5-pro-latest',
    description: '该模型指向 gemini-1.5-pro-latest',
    tags: ['pro'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 2.5,
      outputCostPerMil: 10.0,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 2097152],
        output: 8192,
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'google/gemini-2.0-flash',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    // provider_id: 'gemini-2.0-flash',
    // description: '该模型指向 gemini-2.0-flash-latest',
    tags: ['free'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 0.1,
      outputCostPerMil: 0.4,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 2097152],
        output: 8192,
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'google/gemini-2.5-flash',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    provider_id: 'gemini-2.5-flash-preview-04-17',
    description: '该模型指向 gemini-2.5-flash-preview-04-17',
    tags: ['free'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 0.15,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 1048576,
        range: [0, 2097152],
        output: 8192,
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      reasoning: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },
  {
    id: 'google/gemini-2.5-pro-exp',
    object: 'model',
    created: Date.now(),
    owned_by: 'google',
    provider_id: 'gemini-2.5-pro-exp-03-25',
    description: '该模型指向 gemini-2.5-pro-exp-03-25',
    tags: ['free'],
    links: {
      website: 'https://gemini.google.com/',
      pricingUrl: 'https://ai.google.dev/pricing',
      modelUrl: 'https://ai.google.dev/models/gemini#model-variations',
    },
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 2097152],
        output: 8192,
      },
    },
    features: {
      tool_calling: true,
      file_uploading: true,
      accept_file_types: ['image/*', 'text/*', 'application/pdf'],
    },
  },

  // together.ai
  {
    id: 'together/Mixtral-8x7B-Instruct-v0.1', // alias to mistralai/Mixtral-8x7B-Instruct-v0.1
    created: Date.now(),
    object: 'model',
    owned_by: 'mistral',
    provider_id: 'mistralai/Mixtral-8x7B-Instruct-v0.1',
    tags: ['free'],
    links: {
      pricingUrl: 'https://api.together.xyz/models',
    },
    pricing: {
      inputCostPerMil: 0.6,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'together/QwQ-32B-Preview',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    provider_id: 'Qwen/QwQ-32B-Preview',
    tags: ['free'],
    links: {
      pricingUrl: 'https://api.together.xyz/models',
    },
    pricing: {
      inputCostPerMil: 1.2,
      outputCostPerMil: 1.2,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4000],
      },
    },
    // features: {
    //   tool_calling: true,
    // },
  },
  // {
  //   id: 'together/Qwen1.5-72B-Chat', // alias to Qwen/Qwen1.5-72B-Chat
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'ali',
  //   tags: ['free'],
  //   links: {
  //     pricingUrl: 'https://api.together.xyz/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.9,
  //     outputCostPerMil: 0.9,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 32768,
  //       range: [0, 4000],
  //     },
  //   },
  // },
  // {
  //   id: 'together/Qwen1.5-14B-Chat', // alias to Qwen/Qwen1.5-14B-Chat
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'ali',
  //   tags: ['free'],
  //   links: {
  //     pricingUrl: 'https://api.together.xyz/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.3,
  //     outputCostPerMil: 0.3,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 4000],
  //     },
  //   },
  // },
  // {
  //   id: 'together/qwen2-72b-instruct',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'ali',
  //   tags: ['free'],
  //   links: {
  //     pricingUrl: 'https://api.together.xyz/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.9,
  //     outputCostPerMil: 0.9,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 32768,
  //       range: [0, 4096],
  //     },
  //   },
  // },
  // {
  //   id: 'together/vicuna-13b-v1.5',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'lmsys',
  //   tags: ['free'],
  //   links: {
  //     pricingUrl: 'https://api.together.xyz/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.3,
  //     outputCostPerMil: 0.3,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 4000],
  //     },
  //   },
  // },
  // {
  //   id: 'together/alpaca-7b',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'stanford',
  //   tags: ['free'],
  //   links: {
  //     pricingUrl: 'https://api.together.xyz/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.2,
  //     outputCostPerMil: 0.2,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 4000],
  //     },
  //   },
  // },
  {
    id: 'together/DeepSeek-R1-Distill-Llama-70B',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B',
    tags: ['free'],
    links: {
      pricingUrl: 'https://api.together.xyz/models',
    },
    pricing: {
      inputCostPerMil: 2,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // Alibaba
  {
    id: 'openrouter/qwq-32b',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    provider_id: 'qwen/qwq-32b',
    tags: ['free'],
    links: {
      pricingUrl: 'https://openrouter.ai/qwen/qwq-32b',
    },
    pricing: {
      inputCostPerMil: 0.29,
      outputCostPerMil: 0.39,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4000],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'aliyun/qwen-long',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['free'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.5,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 10000000,
        range: [0, 6144],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'aliyun/qwen-turbo',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['free'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.3,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 6144],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'aliyun/qwen-plus',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['free'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.8,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 30720],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'aliyun/qwen-max',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['free'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 20,
      outputCostPerMil: 60,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 6144],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'aliyun/qwen-max-longcontext',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['enterprise'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 40,
      outputCostPerMil: 120,
    },
    parameters: {
      maximumLength: {
        value: 30720,
        range: [0, 28672],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'aliyun/qwq-32b',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    tags: ['free'],
    links: {
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://help.aliyun.com/zh/model-studio/user-guide/qwq',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 2,
      outputCostPerMil: 6,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 98304],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'aliyun/qwq-plus',
    created: Date.now(),
    object: 'model',
    owned_by: 'ali',
    provider_id: 'qwq-plus-latest',
    description: '该模型目前指向 qwq-plus-latest',
    tags: ['free'],
    links: {
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://help.aliyun.com/zh/model-studio/user-guide/qwq',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1.6,
      outputCostPerMil: 4,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 98304],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'aliyun/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-r1',
    tags: ['free'],
    links: {
      website: 'https://tongyi.aliyun.com/',
      pricingUrl:
        'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing',
      modelUrl: 'https://dashscope.console.aliyun.com/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 65792,
        range: [0, 57344],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // fireworks.ai
  {
    id: 'fireworks/llama-3.1-8b-instruct',
    created: new Date('Mar 27, 2025, 12:11 PM').getTime(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
    tags: ['free'],
    pricing: {
      inputCostPerMil: 0.2,
      outputCostPerMil: 0.2,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 1024],
      },
    },
  },
  {
    id: 'fireworks/llama-3.3-70b-instruct',
    created: new Date('Mar 27, 2025, 12:11 PM').getTime(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'accounts/fireworks/models/llama-v3p3-70b-instruct',
    tags: ['free'],
    pricing: {
      inputCostPerMil: 0.9,
      outputCostPerMil: 0.9,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 1024],
      },
    },
  },
  {
    id: 'fireworks/llama-3.1-405b-instruct',
    created: new Date('Jul 23, 2024, 11:59 PM').getTime(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'accounts/fireworks/models/llama-v3p1-405b-instruct',
    tags: ['pro'],
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 3,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 16384],
      },
    },
    // features: {
    //   tool_calling: true,
    // },
  },
  {
    id: 'fireworks/llama-4-maverick-instruct',
    created: new Date('Apr 6, 2025, 2:21 AM').getTime(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'accounts/fireworks/models/llama4-maverick-instruct-basic',
    tags: ['free'],
    pricing: {
      inputCostPerMil: 0.22,
      outputCostPerMil: 0.88,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 131072],
      },
    },
    features: {
      // tool_calling: true,
      file_uploading: true,
    },
  },
  {
    id: 'fireworks/llama-4-scout-instruct',
    created: new Date('Apr 6, 2025, 2:21 AM').getTime(),
    object: 'model',
    owned_by: 'meta',
    provider_id: 'accounts/fireworks/models/llama4-scout-instruct-basic',
    tags: ['free'],
    pricing: {
      inputCostPerMil: 0.15,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 16384],
      },
    },
    features: {
      // tool_calling: true,
      file_uploading: true,
    },
  },
  // fireworks no longer provides this
  // {
  //   id: 'fireworks/llama-v2-7b-chat',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'meta',
  //   tags: ['free'],
  //   pricing: {
  //     inputCostPerMil: 0.2,
  //     outputCostPerMil: 0.2,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 512],
  //     },
  //   },
  // },
  // {
  //   id: 'fireworks/mixtral-8x7b',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'mistral',
  //   tags: ['free', 'deprecated'],
  //   pricing: {
  //     inputCostPerMil: 0.5,
  //     outputCostPerMil: 0.5,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 32768,
  //       range: [0, 4096],
  //     },
  //   },
  // },
  // {
  //   id: 'fireworks/mixtral-8x22b',
  //   created: new Date('Apr 10, 2024, 10:23 AM').getTime(),
  //   object: 'model',
  //   owned_by: 'mistral',
  //   tags: ['free', 'broken'],
  //   pricing: {
  //     inputCostPerMil: 0.9,
  //     outputCostPerMil: 0.9,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 65536,
  //       range: [0, 8192],
  //     },
  //   },
  // },
  {
    id: 'fireworks/firefunction-v2',
    created: Date.now(),
    object: 'model',
    owned_by: 'fireworks',
    provider_id: 'accounts/fireworks/models/firefunction-v2',
    tags: ['free', 'broken'],
    links: {
      website: 'https://fireworks.ai/',
      pricingUrl: 'https://platform.moonshot.cn/docs/pricing',
      modelUrl: 'https://fireworks.ai/blog/firefunction-v1-gpt-4-level-function-calling',
    },
    pricing: {
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  // fireworks no longer provide this
  // {
  //   id: 'fireworks/stablelm-zephyr-3b',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'stabilityai',
  //   tags: ['free', 'deprecated'],
  //   links: {
  //     website: 'https://stability.ai/news/stablelm-zephyr-3b-stability-llm',
  //     pricingUrl: 'https://fireworks.ai/models/stability/stablelm-zephyr-3b',
  //     modelUrl: 'https://huggingface.co/stabilityai/stablelm-zephyr-3b',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.2,
  //     outputCostPerMil: 0.2,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 512],
  //     },
  //   },
  // },
  // {
  //   id: 'fireworks/hermes-2-pro-mistral-7b',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'nousresearch',
  //   tags: ['free', 'deprecated'],
  //   links: {
  //     website: 'https://stability.ai/news/stablelm-zephyr-3b-stability-llm',
  //     pricingUrl: 'https://fireworks.ai/models/fireworks/hermes-2-pro-mistral-7b',
  //     modelUrl: 'https://nousresearch.com',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.2,
  //     outputCostPerMil: 0.2,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       range: [0, 4000],
  //     },
  //   },
  // },
  // {
  //   id: 'fireworks/yi-34b-chat',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: '01ai',
  //   tags: ['free', 'deprecated'],
  //   links: {
  //     website: 'https://stability.ai/news/stablelm-zephyr-3b-stability-llm',
  //     pricingUrl: 'https://fireworks.ai/models/fireworks/yi-34b-chat',
  //     modelUrl: 'https://huggingface.co/01-ai/Yi-34B-Chat',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.9,
  //     outputCostPerMil: 0.9,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 512],
  //     },
  //   },
  //   features: {
  //     // Does not work
  //     // tool_calling: true,
  //   },
  // },
  {
    id: 'fireworks/yi-large',
    created: Date.now(),
    object: 'model',
    owned_by: '01ai',
    provider_id: 'accounts/yi-01-ai/models/yi-large',
    tags: ['free'],
    links: {
      website: 'https://stability.ai/news/stablelm-zephyr-3b-stability-llm',
      pricingUrl: 'https://fireworks.ai/models/yi-01-ai/yi-large',
      modelUrl: 'https://huggingface.co/01-ai/Yi-34B-Chat',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 3,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 512],
      },
    },
  },
  {
    id: 'fireworks/firellava-13b',
    created: Date.now(),
    object: 'model',
    owned_by: 'fireworks',
    provider_id: 'accounts/fireworks/models/firellava-13b',
    tags: ['free', 'broken'],
    links: {
      website: 'https://fireworks.ai/',
      pricingUrl: 'https://fireworks.ai/models/fireworks/firellava-13b',
      modelUrl: 'https://fireworks.ai/models/fireworks/firellava-13b',
    },
    pricing: {
      inputCostPerMil: 0.2,
      outputCostPerMil: 0.2,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
    },
  },

  // 01ai
  {
    id: 'openrouter/yi-large',
    created: Date.now(),
    object: 'model',
    owned_by: '01ai',
    provider_id: '01-ai/yi-large',
    tags: ['free'],
    links: {
      website: 'https://lingyiwanwu.com/',
      pricingUrl: 'https://platform.lingyiwanwu.com/docs#-%E7%94%A8%E9%87%8F%E8%AE%A1%E8%B4%B9',
      modelUrl: 'https://platform.lingyiwanwu.com/docs',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 3,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: '01ai/yi-lightning',
    created: Date.now(),
    object: 'model',
    owned_by: '01ai',
    tags: ['free'],
    links: {
      website: 'https://lingyiwanwu.com/',
      pricingUrl: 'https://platform.lingyiwanwu.com/docs#-%E7%94%A8%E9%87%8F%E8%AE%A1%E8%B4%B9',
      modelUrl: 'https://platform.lingyiwanwu.com/docs',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.99,
      outputCostPerMil: 0.99,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: '01ai/yi-vision-v2',
    created: Date.now(),
    object: 'model',
    owned_by: '01ai',
    tags: ['free'],
    links: {
      website: 'https://lingyiwanwu.com/',
      pricingUrl: 'https://platform.lingyiwanwu.com/docs#-%E7%94%A8%E9%87%8F%E8%AE%A1%E8%B4%B9',
      modelUrl: 'https://platform.lingyiwanwu.com/docs',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 6,
      outputCostPerMil: 6,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      file_uploading: true,
    },
  },

  // 360
  {
    id: '360/360gpt-turbo',
    created: Date.now(),
    object: 'model',
    owned_by: '360',
    tags: ['free'],
    links: {
      website: 'https://ai.360.com/',
      pricingUrl: 'https://ai.360.com/platform/limit',
      modelUrl: 'https://ai.360.com/platform/limit',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: '360/360gpt-pro',
    created: Date.now(),
    object: 'model',
    owned_by: '360',
    tags: ['free'],
    links: {
      website: 'https://ai.360.com/',
      pricingUrl: 'https://ai.360.com/platform/limit',
      modelUrl: 'https://ai.360.com/platform/limit',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 2,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: '360/360gpt2-pro',
    created: Date.now(),
    object: 'model',
    owned_by: '360',
    tags: ['free'],
    links: {
      website: 'https://ai.360.com/',
      pricingUrl: 'https://ai.360.com/platform/limit',
      modelUrl: 'https://ai.360.com/platform/limit',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 2,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: '360/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: '360/deepseek-r1',
    tags: ['free'],
    links: {
      website: 'https://ai.360.com/',
      pricingUrl: 'https://ai.360.com/platform/limit',
      modelUrl: 'https://ai.360.com/platform/limit',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },

  // Stepfun
  {
    id: 'stepfun/step-1-flash',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['free'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 1,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-1-32k',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['pro'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 15,
      outputCostPerMil: 70,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-1v-32k',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['pro'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 15,
      outputCostPerMil: 70,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      file_uploading: true,
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-1.5v-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['pro'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 8,
      outputCostPerMil: 35,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      file_uploading: true,
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-1-256k',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['enterprise'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 95,
      outputCostPerMil: 300,
    },
    parameters: {
      maximumLength: {
        value: 256000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-2-16k',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['pro'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 38,
      outputCostPerMil: 120,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'stepfun/step-2-mini',
    created: Date.now(),
    object: 'model',
    owned_by: 'stepfun',
    tags: ['free'],
    links: {
      website: 'https://platform.stepfun.com',
      pricingUrl: 'https://platform.stepfun.com/docs/overview/concept',
      modelUrl: 'https://platform.stepfun.com',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },

  // groq.com
  {
    id: 'groq/mistral-saba-24b',
    created: Date.now(),
    object: 'model',
    owned_by: 'mistral',
    tags: ['free'],
    links: {
      website: 'https://console.groq.com/',
      pricingUrl: 'https://groq.com/pricing/',
      modelUrl: 'https://console.groq.com/docs/models',
    },
    pricing: {
      inputCostPerMil: 0.79,
      outputCostPerMil: 0.79,
    },
    parameters: {
      maximumLength: {
        value: 21845,
        range: [0, 4096],
      },
    },
  },
  {
    id: 'groq/llama2-70b-4096',
    created: Date.now(),
    object: 'model',
    owned_by: 'meta',
    tags: ['free'],
    links: {
      website: 'https://console.groq.com/',
      pricingUrl: 'https://groq.com/pricing/',
      modelUrl: 'https://console.groq.com/docs/models',
    },
    pricing: {
      inputCostPerMil: 0.64,
      outputCostPerMil: 0.8,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
  },
  // {
  //   id: 'groq/llama3-8b-8192',
  //   created: new Date('Apr 22, 2024, 1:43 AM').getTime(),
  //   object: 'model',
  //   owned_by: 'meta',
  //   tags: ['free', 'deprecated'],
  //   links: {
  //     website: 'https://console.groq.com/',
  //     pricingUrl: 'https://groq.com/pricing/',
  //     modelUrl: 'https://console.groq.com/docs/models',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.05,
  //     outputCostPerMil: 0.1,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 8192,
  //       range: [0, 4096],
  //     },
  //   },
  // },
  {
    id: 'groq/llama-3.3-70b-versatile',
    created: new Date('Apr 22, 2024, 1:43 AM').getTime(),
    object: 'model',
    owned_by: 'meta',
    tags: ['free'],
    links: {
      website: 'https://console.groq.com/',
      pricingUrl: 'https://groq.com/pricing/',
      modelUrl: 'https://console.groq.com/docs/models',
    },
    pricing: {
      inputCostPerMil: 0.59,
      outputCostPerMil: 0.79,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      tool_calling: true,
    },
  },

  // Moonshot
  {
    id: 'moonshot/moonshot-v1-8k',
    created: Date.now(),
    object: 'model',
    owned_by: 'moonshot',
    tags: ['free'],
    links: {
      website: 'https://www.moonshot.cn/',
      pricingUrl: 'https://platform.moonshot.cn/docs/pricing',
      modelUrl: 'https://platform.moonshot.cn/',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 12,
      outputCostPerMil: 12,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'moonshot/moonshot-v1-32k',
    created: Date.now(),
    object: 'model',
    owned_by: 'moonshot',
    links: {
      website: 'https://www.moonshot.cn/',
      pricingUrl: 'https://platform.moonshot.cn/docs/pricing',
      modelUrl: 'https://platform.moonshot.cn/',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 24,
      outputCostPerMil: 24,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'moonshot/moonshot-v1-128k',
    created: Date.now(),
    object: 'model',
    owned_by: 'moonshot',
    links: {
      website: 'https://www.moonshot.cn/',
      pricingUrl: 'https://platform.moonshot.cn/docs/pricing',
      modelUrl: 'https://platform.moonshot.cn/',
    },
    tags: ['enterprise'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 60,
      outputCostPerMil: 60,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },
  {
    id: 'moonshot/kimi-k2',
    created: Date.now(),
    object: 'model',
    owned_by: 'moonshot',
    provider_id: 'kimi-k2-0711-preview',
    tags: ['free'],
    links: {
      website: 'https://www.moonshot.cn/',
      pricingUrl: 'https://platform.moonshot.cn/docs/pricing',
      modelUrl: 'https://platform.moonshot.cn/',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // file_uploading: true,
      tool_calling: true,
    },
  },

  // Zhipu, ChatGLM
  // {
  //   id: 'zhipu/glm-3-turbo',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'zhipu',
  //   tags: ['free', 'deprecated'],
  //   links: {
  //     website: 'https://zhipuai.cn/',
  //     pricingUrl: 'https://open.bigmodel.cn/pricing',
  //     modelUrl: 'https://open.bigmodel.cn/',
  //   },
  //   // tags: ['enterprise'],
  //   pricing: {
  //     currency: 'CNY',
  //     inputCostPerMil: 1,
  //     outputCostPerMil: 1,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 131072,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     // Does not work
  //     // tool_calling: true,
  //   },
  // },
  {
    id: 'zhipu/glm-4-plus',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['enterprise'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 50,
      outputCostPerMil: 50,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4-air',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.5,
      outputCostPerMil: 0.5,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4-long',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 1,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 800000],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-zero-preview',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['enterprise'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 10,
      outputCostPerMil: 10,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4-flashx',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.1,
      outputCostPerMil: 0.1,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4-flash',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4-alltools',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['enterprise'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 100,
      outputCostPerMil: 100,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      tool_calling: true,
    },
  },
  {
    id: 'zhipu/glm-4v-plus',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    provider_id: 'glm-4v-plus-0111',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 4,
    },
    parameters: {
      maximumLength: {
        value: 2048,
        range: [0, 2048],
      },
    },
    features: {
      // image_generation: true,
      file_uploading: true,
    },
  },
  {
    id: 'zhipu/glm-4v-flash',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    provider_id: 'glm-4v-plus-0111',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    features: {
      // image_generation: true,
      file_uploading: true,
    },
  },
  {
    id: 'zhipu/emohaa',
    created: Date.now(),
    object: 'model',
    owned_by: 'zhipu',
    links: {
      website: 'https://zhipuai.cn/',
      pricingUrl: 'https://open.bigmodel.cn/pricing',
      modelUrl: 'https://open.bigmodel.cn/',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 15,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 2048],
      },
    },
    features: {
      // image_generation: true,
      // file_uploading: true,
    },
  },

  // Xunfei
  {
    id: 'xunfei/spark-lite',
    created: Date.now(),
    object: 'model',
    owned_by: 'xunfei',
    provider_id: 'lite',
    links: {
      website: 'https://xinghuo.xfyun.cn/',
      pricingUrl: 'https://xinghuo.xfyun.cn/sparkapi',
      modelUrl: 'https://www.xfyun.cn/doc/spark/Web.html',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
  },
  {
    id: 'xunfei/Spark4.0-Ultra',
    created: Date.now(),
    object: 'model',
    owned_by: 'xunfei',
    provider_id: '4.0Ultra',
    links: {
      website: 'https://xinghuo.xfyun.cn/',
      pricingUrl: 'https://xinghuo.xfyun.cn/sparkapi',
      modelUrl: 'https://www.xfyun.cn/doc/spark/Web.html',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 70,
      outputCostPerMil: 70,
    },
  },

  // Baidu
  {
    id: 'baidu/ernie-4.0-8k',
    created: Date.now(),
    object: 'model',
    owned_by: 'baidu',
    provider_id: 'ernie-4.0-8k-latest',
    description: '该模型指向 ernie-4.0-8k-latest',
    links: {
      website: 'https://yiyan.baidu.com/',
      pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
      modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 30,
      outputCostPerMil: 30,
    },
  },
  {
    id: 'baidu/ernie-4.5-8k',
    created: Date.now(),
    object: 'model',
    owned_by: 'baidu',
    provider_id: 'ernie-4.5-8k-preview',
    description: '该模型指向 ernie-4.5-8k-preview',
    links: {
      website: 'https://yiyan.baidu.com/',
      pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
      modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    features: {
      file_uploading: true,
    },
  },
  // {
  //   id: 'baidu/ernie-x1-32k',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'baidu',
  //   provider_id: 'ernie-x1-32k-preview',
  //   description: '该模型指向 ernie-x1-32k-preview',
  //   links: {
  //     website: 'https://yiyan.baidu.com/',
  //     pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //     modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //   },
  //   tags: ['free'],
  //   pricing: {
  //     currency: 'CNY',
  //     inputCostPerMil: 2,
  //     outputCostPerMil: 8,
  //   },
  //   features: {
  //     // file_uploading: true,
  //     reasoning: true,
  //   },
  // },
  // {
  //   id: 'baidu/ernie-3.5-8k',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'baidu',
  //   links: {
  //     website: 'https://yiyan.baidu.com/',
  //     pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //     modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //   },
  //   tags: ['pro', 'deprecated'],
  //   pricing: {
  //     currency: 'CNY',
  //     inputCostPerMil: 120,
  //     outputCostPerMil: 120,
  //   },
  // },
  // {
  //   id: 'baidu/ernie-lite-8k',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'baidu',
  //   links: {
  //     website: 'https://yiyan.baidu.com/',
  //     pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //     modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
  //   },
  //   description: '原ERNIE-Bot-turbo-0922',
  //   tags: ['free', 'deprecated'],
  //   pricing: {
  //     currency: 'CNY',
  //     inputCostPerMil: 8,
  //     outputCostPerMil: 8,
  //   },
  // },
  {
    id: 'baidu/ernie-speed-8k',
    created: Date.now(),
    object: 'model',
    owned_by: 'baidu',
    links: {
      website: 'https://yiyan.baidu.com/',
      pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
      modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0, // 4 CNY
      outputCostPerMil: 0, // 8 CNY
    },
  },
  {
    id: 'baidu/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    links: {
      website: 'https://yiyan.baidu.com/',
      pricingUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
      modelUrl: 'https://console.bce.baidu.com/qianfan/modelcenter/model/buildIn/list',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 2,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // MiniMax
  {
    id: 'minimax/abab5.5s-chat',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    tags: ['free', 'deprecated'],
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    // tags: ['enterprise'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 5,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'minimax/abab5.5-chat',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free', 'deprecated'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 15,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 16384,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'minimax/abab6.5s-chat',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 1,
    },
    parameters: {
      maximumLength: {
        value: 245760,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'minimax/abab6.5g-chat',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 5,
      outputCostPerMil: 5,
    },
    parameters: {
      maximumLength: {
        value: 8192,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'minimax/MiniMax-Text-01',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
    },
  },
  {
    id: 'minimax/MiniMax-M1',
    created: Date.now(),
    object: 'model',
    owned_by: 'minimax',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.8,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 1000000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'minimax/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'DeepSeek-R1',
    links: {
      website: 'https://www.minimaxi.com/',
      pricingUrl: 'https://www.minimaxi.com/document/price',
      modelUrl: 'https://platform.minimaxi.com',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 64000,
        range: [0, 4096],
      },
    },
    features: {
      // Does not work
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // Baichuan
  {
    id: 'baichuan/Baichuan4',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['pro'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 100,
      outputCostPerMil: 100,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'baichuan/Baichuan4-Turbo',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 15,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'baichuan/Baichuan3-Turbo',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 12,
      outputCostPerMil: 12,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'baichuan/Baichuan3-Turbo-128k',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 24,
      outputCostPerMil: 24,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'baichuan/Baichuan2-Turbo',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['free'],
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 8,
      outputCostPerMil: 8,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
  },
  // {
  //   id: 'baichuan/Baichuan2-Turbo-192k',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'baichuan',
  //   links: {
  //     website: 'https://www.baichuan-ai.com/',
  //     pricingUrl: 'https://platform.baichuan-ai.com/price',
  //     modelUrl: 'https://platform.baichuan-ai.com/docs/api',
  //   },
  //   tags: ['free', 'deprecated'],
  //   pricing: {
  //     currency: 'CNY',
  //     inputCostPerMil: 16,
  //     outputCostPerMil: 16,
  //   },
  // },
  {
    id: 'baichuan/Baichuan2-53B',
    created: Date.now(),
    object: 'model',
    owned_by: 'baichuan',
    links: {
      website: 'https://www.baichuan-ai.com/',
      pricingUrl: 'https://platform.baichuan-ai.com/price',
      modelUrl: 'https://platform.baichuan-ai.com/docs/api',
    },
    tags: ['free'],
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 20,
      outputCostPerMil: 20,
    },
  },

  // Recursal
  // {
  //   id: 'EagleX-V2',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'recursal',
  //   tags: ['free'],
  //   links: {
  //     website: 'https://recursal.ai/',
  //     pricingUrl: 'https://platform.recursal.com/docs',
  //     modelUrl: 'https://platform.recursal.com/docs',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.2,
  //     outputCostPerMil: 0.2,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 4096,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     // image_generation: true,
  //   },
  // },

  // DeepSeek
  {
    id: 'deepseek/deepseek-chat',
    description: '该模型指向 DeepSeek V3',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 1,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 64000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      tool_calling: true,
    },
  },
  {
    id: 'deepseek/deepseek-reasoner',
    description: '官方 R1 模型',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 64000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/deepseek-v3',
    description: '该模型指向 deepseek-chat-v3-0324',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek/deepseek-chat-v3-0324:free',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://openrouter.ai/deepseek/deepseek-chat-v3-0324:free',
      modelUrl: 'https://openrouter.ai/deepseek/deepseek-chat-v3-0324:free',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0,
      outputCostPerMil: 0,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      tool_calling: true,
    },
  },
  {
    id: 'openrouter/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek/deepseek-r1',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      inputCostPerMil: 0.75,
      outputCostPerMil: 2.4,
    },
    parameters: {
      maximumLength: {
        value: 16000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      reasoning: true,
    },
  },
  {
    id: 'openrouter/deepseek-r1-distill-qwen-1.5b',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek/deepseek-r1-distill-qwen-1.5b',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      inputCostPerMil: 0.18,
      outputCostPerMil: 0.18,
    },
    parameters: {
      maximumLength: {
        value: 131000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  // {
  //   id: 'openrouter/deepseek-r1-distill-qwen-14b',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: 'deepseek',
  //   hosted_by: 'openrouter',
  //   tags: ['free'],
  //   links: {
  //     website: 'https://platform.deepseek.com/',
  //     pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
  //     modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
  //   },
  //   pricing: {
  //     inputCostPerMil: 1.6,
  //     outputCostPerMil: 1.6,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 131000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     // image_generation: true,
  //   },
  // },
  {
    id: 'openrouter/deepseek-r1-distill-qwen-32b',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek/deepseek-r1-distill-qwen-32b',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      inputCostPerMil: 0.12,
      outputCostPerMil: 0.18,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/deepseek-r1-distill-llama-70b',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek/deepseek-r1-distill-llama-70b',
    tags: ['free'],
    links: {
      website: 'https://platform.deepseek.com/',
      pricingUrl: 'https://platform.deepseek.com/api-docs/zh-cn/pricing/',
      modelUrl: 'https://platform.deepseek.com/api-docs/zh-cn/',
    },
    pricing: {
      inputCostPerMil: 0.23,
      outputCostPerMil: 0.69,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // Doubao
  {
    id: 'volcengine/doubao-1.5-lite-32k',
    created: Date.now(),
    object: 'model',
    owned_by: 'bytedance',
    provider_id: 'doubao-1-5-lite-32k-250115',
    tags: ['free'],
    links: {
      website: 'https://www.volcengine.com/product/doubao',
      pricingUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?current=1&pageSize=10',
      modelUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.3,
      outputCostPerMil: 0.6,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
    },
  },
  {
    id: 'volcengine/doubao-1.5-pro-256k',
    created: Date.now(),
    object: 'model',
    owned_by: 'bytedance',
    provider_id: 'doubao-1-5-pro-256k-250115',
    tags: ['free'],
    links: {
      website: 'https://www.volcengine.com/product/doubao',
      pricingUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?current=1&pageSize=10',
      modelUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 5,
      outputCostPerMil: 9,
    },
    parameters: {
      maximumLength: {
        value: 256000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      // tool_calling: true,
    },
  },
  {
    id: 'volcengine/doubao-1.5-pro-32k',
    created: Date.now(),
    object: 'model',
    owned_by: 'bytedance',
    provider_id: 'doubao-1-5-pro-32k-250115',
    tags: ['free'],
    links: {
      website: 'https://www.volcengine.com/product/doubao',
      pricingUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?current=1&pageSize=10',
      modelUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 0.8,
      outputCostPerMil: 2,
    },
    parameters: {
      maximumLength: {
        value: 32000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
    },
  },
  {
    id: 'volcengine/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-r1-250120',
    tags: ['free'],
    links: {
      website: 'https://www.volcengine.com/product/doubao',
      pricingUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint?current=1&pageSize=10',
      modelUrl: 'https://console.volcengine.com/ark/region:ark+cn-beijing/model',
    },
    pricing: {
      currency: 'CNY',
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 64000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // xAI
  // {
  //   id: 'xai/grok-2-mini',
  //   created: Date.now(),
  //   // description: '指向 doubao-pro-128k/240515',
  //   object: 'model',
  //   owned_by: 'xai',
  //   tags: ['free', 'broken'],
  //   links: {
  //     website: 'https://x.ai/',
  //     pricingUrl: 'https://x.ai/grok',
  //     modelUrl: 'https://x.ai/grok',
  //   },
  //   pricing: {
  //     inputCostPerMil: 5,
  //     outputCostPerMil: 9,
  //   },
  //   parameters: {
  //     maximumLength: {
  //       value: 128000,
  //       range: [0, 4096],
  //     },
  //   },
  //   features: {
  //     // image_generation: true,
  //   },
  // },
  {
    id: 'xai/grok-2',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    // provider_id: 'x-ai/grok-2-1212', // openrouter
    tags: ['pro'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 5,
      outputCostPerMil: 10,
    },
    parameters: {
      maximumLength: {
        value: 33000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },

  // xAI Grok
  {
    id: 'xai/grok-2-vision',
    created: Date.now(),
    // description: '指向 grok-2-vision-1212',
    object: 'model',
    owned_by: 'xai',
    provider_id: 'grok-2-vision-1212',
    tags: ['pro'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 5,
      outputCostPerMil: 10,
    },
    parameters: {
      maximumLength: {
        value: 33000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
      file_uploading: true,
    },
  },
  {
    id: 'xai/grok-3-fast',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    // provider_id: 'x-ai/grok-2-1212', // openrouter
    tags: ['limited-free'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 5,
      outputCostPerMil: 25,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'xai/grok-3-mini-fast',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    // provider_id: 'x-ai/grok-2-1212', // openrouter
    tags: ['free'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 0.6,
      outputCostPerMil: 4,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/grok-3',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    provider_id: 'x-ai/grok-3-beta', // openrouter
    tags: ['pro'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
  {
    id: 'openrouter/grok-3-mini',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    provider_id: 'x-ai/grok-3-mini-beta', // openrouter
    tags: ['free'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 0.3,
      outputCostPerMil: 0.5,
    },
    parameters: {
      maximumLength: {
        value: 131072,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'openrouter/grok-4',
    created: Date.now(),
    // description: '指向 doubao-pro-128k/240515',
    object: 'model',
    owned_by: 'xai',
    provider_id: 'x-ai/grok-4', // openrouter
    tags: ['pro'],
    links: {
      website: 'https://x.ai/',
      pricingUrl: 'https://x.ai/grok',
      modelUrl: 'https://x.ai/grok',
    },
    pricing: {
      inputCostPerMil: 3,
      outputCostPerMil: 15,
    },
    parameters: {
      maximumLength: {
        value: 256000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // Cohere
  {
    id: 'cohere/command-r-plus',
    created: Date.now(),
    object: 'model',
    owned_by: 'cohere',
    provider_id: 'command-r-plus-08-2024',
    description: '指向 command-r-plus-08-2024',
    tags: ['pro'],
    links: {
      website: 'https://cohere.com',
      pricingUrl: 'https://cohere.com/pricing',
      modelUrl: 'https://docs.cohere.com/v2/docs/command-r-plus',
    },
    pricing: {
      inputCostPerMil: 2.5,
      outputCostPerMil: 10,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4000],
      },
    },
    features: {
      tool_calling: true,
    },
  },
  {
    id: 'cohere/command-r7b',
    created: Date.now(),
    object: 'model',
    owned_by: 'cohere',
    provider_id: 'command-r7b-12-2024',
    description: '指向 command-r7b-12-2024',
    tags: ['pro'],
    links: {
      website: 'https://cohere.com',
      pricingUrl: 'https://cohere.com/pricing',
      modelUrl: 'https://docs.cohere.com/v2/docs/command-r7b',
    },
    pricing: {
      inputCostPerMil: 0.0375,
      outputCostPerMil: 0.15,
    },
    parameters: {
      maximumLength: {
        value: 128000,
        range: [0, 4000],
      },
    },
    features: {
      tool_calling: true,
    },
  },

  // Replicate (broken)
  // {
  //   id: 'replicate/yi-34b-chat',
  //   created: Date.now(),
  //   object: 'model',
  //   owned_by: '01ai',
  //   provider_id: '01-ai/yi-34b-chat:914692bbe8a8e2b91a4e44203e70d170c9c5ccc1359b283c84b0ec8d47819a46',
  //   tags: ['free'],
  //   links: {
  //     website: 'https://replicate.com/',
  //     pricingUrl: 'https://replicate.com/01-ai/yi-34b-chat',
  //     modelUrl: 'https://replicate.com/01-ai/yi-34b-chat',
  //   },
  //   pricing: {
  //     inputCostPerMil: 0.1,
  //     outputCostPerMil: 0.1,
  //   },
  //   parameters: {
  //     // maximumLength: {
  //     //   value: 128000,
  //     //   range: [0, 4000],
  //     // },
  //   },
  //   features: {
  //     tool_calling: true,
  //   },
  // },

  // NIM
  {
    id: 'nim/deepseek-r1',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-ai/deepseek-r1',
    tags: ['free', 'limited-free'],
    links: {
      website: 'https://build.nvidia.com/deepseek-ai/deepseek-r1',
      pricingUrl: 'https://build.nvidia.com/deepseek-ai/deepseek-r1',
      modelUrl: 'https://build.nvidia.com/deepseek-ai/deepseek-r1',
    },
    pricing: {
      inputCostPerMil: 4,
      outputCostPerMil: 16,
    },
    parameters: {
      maximumLength: {
        value: 64000,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // DeepInfra
  {
    id: 'deepinfra/deepseek-r1-turbo',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-ai/DeepSeek-R1-Turbo',
    tags: ['free', 'limited-free'],
    links: {
      website: 'https://deepinfra.com/deepseek-ai/DeepSeek-R1-Turbo',
      pricingUrl: 'https://deepinfra.com/deepseek-ai/DeepSeek-R1-Turbo',
      modelUrl: 'https://deepinfra.com/deepseek-ai/DeepSeek-R1-Turbo',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 3,
    },
    parameters: {
      maximumLength: {
        value: 32768,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },
  {
    id: 'deepinfra/deepseek-v3',
    created: Date.now(),
    object: 'model',
    owned_by: 'deepseek',
    provider_id: 'deepseek-ai/DeepSeek-V3-0324',
    description: '指向 DeepSeek-V3-0324',
    tags: ['free'],
    links: {
      website: 'https://deepinfra.com/deepseek-ai/DeepSeek-V3-0324',
      pricingUrl: 'https://deepinfra.com/deepseek-ai/DeepSeek-V3-0324',
      modelUrl: 'https://deepinfra.com/deepseek-ai/DeepSeek-V3-0324',
    },
    pricing: {
      inputCostPerMil: 0.4,
      outputCostPerMil: 0.89,
    },
    parameters: {
      maximumLength: {
        value: 65536,
        range: [0, 4096],
      },
    },
    features: {
      // image_generation: true,
      // tool_calling: true,
      // reasoning: true,
      // reasoningByDefault: true,
    },
  },

  // Jina
  {
    id: 'jina/jina-deepsearch-v1',
    description: '内置搜索功能',
    created: Date.now(),
    object: 'model',
    owned_by: 'jina',
    tags: ['pro'],
    links: {
      website: 'https://jina.ai/',
      pricingUrl: 'https://jina.ai/deepsearch/',
      modelUrl: 'https://jina.ai/deepsearch/',
    },
    pricing: {
      inputCostPerMil: 0.02,
      outputCostPerMil: 0.02,
    },
    // parameters: {
    //   maximumLength: {
    //     value: 32768,
    //     range: [0, 4096],
    //   },
    // },
    features: {
      // image_generation: true,
      // tool_calling: true,
      reasoning: true,
      reasoningByDefault: true,
    },
  },

  // Gryphe
  {
    id: 'openrouter/mythomax-13b',
    created: Date.now(),
    object: 'model',
    owned_by: 'gryphe',
    provider_id: 'gryphe/mythomax-l2-13b',
    tags: ['free', 'hidden'],
    links: {
      website: 'https://huggingface.co/Gryphe',
      pricingUrl: 'https://openrouter.ai/gryphe/mythomax-l2-13b',
      modelUrl: 'https://openrouter.ai/gryphe/mythomax-l2-13b',
    },
    pricing: {
      inputCostPerMil: 0.065,
      outputCostPerMil: 0.065,
    },
    parameters: {
      maximumLength: {
        value: 4096,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
]

/**
 * 普通模型白名单
 */
export const whitelistFree = customModels
  .filter(
    model => (model.tags.includes('free') || model.tags.includes('limited-free')) && !model.tags.includes('hidden')
  )
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * 高级模型白名单
 */
export const whitelistPro = customModels
  .filter(model => model.tags.includes('pro') && !model.tags.includes('hidden'))
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * 企业版模型白名单
 */
export const whitelistEnterprise = customModels
  .filter(model => model.tags.includes('enterprise') && !model.tags.includes('hidden'))
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * Get model details by given model name (ID)
 * @param model model name (ID)
 */
export function getModel(model: string) {
  const found = customModels.find(item => item.id === model || (item.aliases && item.aliases.includes(model)))
  return found
}

export function getModelFeatures(model: string) {
  const found = customModels.find(item => item.id === model)
  if (found?.features && Object.keys(found.features).length) {
    return found.features
  } else {
    return undefined
  }
}
