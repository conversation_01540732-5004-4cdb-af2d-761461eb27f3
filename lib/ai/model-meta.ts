import { createProviderRegistry } from 'ai'

import type { ModelWithMeta } from '@/types/ai'

export const modelProviderMap: {
  [key: string]: {
    name: string
    logo: string
    autoDark?: boolean
  }
} = {
  '360': { name: '360', logo: '360-logo-fix.png' },
  'tencent': { name: '腾讯', logo: 'tencent-logo-black.svg' },
  'baidu': { name: '百度', logo: 'baidu-logo-black.svg' },
  'google': { name: 'Google', logo: 'google-logo-black.svg' },
  'xunfei': { name: '科大讯飞', logo: 'xunfei-logo.png' },
  'openai': { name: 'OpenAI', logo: 'openai-logo.svg' },
  'azure': { name: 'Azure OpenAI Service', logo: '' },
  'zhipu': { name: '智谱AI', logo: 'zhipu-logo-black-fix.svg' },
  'anthropic': { name: 'Anthropic', logo: 'anthropic-logo.svg' },
  'moonshot': { name: 'Moonshot AI', logo: 'moonshot-logo.png' },
  'ali': { name: '阿里巴巴', logo: 'ali-logo.svg' },
  'aliyun': { name: '阿里云', logo: 'ali-logo.svg' },
  'minimax': { name: 'MiniMax', logo: 'minimax-logo.png' },
  'baichuan': { name: '百川智能', logo: 'baichuan-logo-black.svg' },
  'perplexity': { name: 'Perplexity', logo: 'pplx-logo-black.svg' },
  'mistral': { name: 'Mistral', logo: 'mistral-logo.png' },
  'together': { name: 'Together AI', logo: 'together-logo.svg' },
  'fireworks': { name: 'fireworks.ai', logo: 'fireworks-logo.svg' },
  'meta': { name: 'Meta', logo: 'meta-logo.svg' },
  'groq': { name: 'Groq', logo: '' },
  'xiaosuan': { name: '小算', logo: 'xiaosuan-logo.svg' },
  'openrouter': { name: 'OpenRouter', logo: '' },
  'stabilityai': { name: 'Stability AI', logo: 'stabilityai-logo.svg' },
  'nousresearch': { name: 'Nous Research', logo: 'nous-logo.png' },
  '01ai': { name: '01.ai', logo: '01ai-logo-en.png' },
  'stepfun': { name: '阶跃星辰', logo: 'stepai-logo-black.svg' },
  'recursal': { name: 'Recursal AI', logo: 'recursal-logo.svg' },
  'lmsys': { name: 'LMSYS Org', logo: '' },
  'stanford': { name: 'Stanford CRFM', logo: 'stanford-logo.svg' },
  'databricks': { name: 'Databricks', logo: 'databricks-logo-black.svg' },
  'deepseek': { name: 'DeepSeek', logo: 'deepseek-logo.svg' },
  'bytedance': { name: 'ByteDance', logo: 'bytedance-logo-black.svg' },
  'deepl': { name: 'DeepL', logo: 'deepl-logo.svg' },
  'xai': { name: 'xAI', logo: 'xai-icon-black.svg' },
  'cohere': { name: 'Cohere', logo: 'cohere-logo-black.svg' },
  'nim': { name: 'NVIDIA NIM', logo: '' },
  'deepinfra': { name: 'DeepInfra', logo: '' },
  'volcengine': { name: '火山引擎', logo: '' },
  'qcloud': { name: '腾讯云', logo: '' },
  'jina': { name: 'Jina AI', logo: 'jina-logo-black.svg' },
  'oapi': { name: 'LAPLACE AI Gateway (Legacy)', logo: '' },
  'litellm': { name: 'LAPLACE AI Gateway', logo: '' },

  'others': { name: '其他', logo: '' },
}

export const modelMap: {
  [key: string]: {
    name?: string
    logo?: string
    autoDark?: boolean
  }
} = {
  'gpt-3': { logo: 'openai-icon.svg' },
  'gpt-4': { logo: 'openai-icon-2.svg' },
  'o1': { logo: 'openai-icon-2.svg' },
  'o2': { logo: 'openai-icon-2.svg' },
  'o3': { logo: 'openai-icon-2.svg' },
  'o4': { logo: 'openai-icon-2.svg' },

  'mistral': { logo: 'mistral-icon.png' },
  'mixtral': { logo: 'mistral-icon.png' },
  'open-mistral': { logo: 'mistral-icon.png' },
  'open-mixtral': { logo: 'mistral-icon.png' },
  'qwen': { logo: 'ali-icon.svg' },
  'qwq': { logo: 'ali-icon.svg' },
  'claude': { logo: 'anthropic-icon.svg' },
  'fire': { logo: 'fireworks-icon.svg', autoDark: true },
  'gemini': { logo: 'google-gemini-icon.svg' },
  'gemma': { logo: 'google-gemini-icon.svg' },
  'palm': { logo: 'google-palm-icon.svg' },
  'llama': { logo: 'meta-icon.svg' },
  'codellama': { logo: 'meta-icon.svg' },
  'sonar': { logo: 'pplx-icon.svg' },
  'moonshot': { logo: 'moonshot-icon.png', autoDark: true },
  'hunyuan': { logo: 'hunyuan-icon.png' },
  'glm': { logo: 'zhipu-icon.svg' },
  'emohaa': { logo: 'zhipu-icon.svg' },
  'spark': { logo: 'xunfei-spark-icon.svg' },
  'baichuan': { logo: 'baichuan-icon.svg' },
  'abab': { logo: 'minimax-icon.png' },
  'minimax': { logo: 'minimax-icon.png' },
  'ernie': { logo: 'yiyan-icon.png' },
  'stablelm-zephyr-3b': { logo: 'stabilityai-icon.png' },
  'hermes-2-pro-mistral-7b': { logo: 'nous-icon.png', autoDark: true },
  'yi-': { logo: '01ai-icon.png', autoDark: true },
  'step': { logo: 'stepai-icon.svg' },
  '360': { logo: '360-icon.png' },
  'xiaosuan': { logo: 'xiaosuan-icon.svg' },
  'thomas-1': { logo: 'thomas-icon.svg' },
  'thomas-2': { logo: 'thomas-icon-2.svg' },
  'eagle': { logo: 'recursal-icon.svg', autoDark: true },
  'alpaca': { logo: 'alpaca.png' },
  'vicuna': { logo: 'vicuna.jpg' },
  'dbrx': { logo: 'databricks-icon.svg' },
  'doubao': { logo: 'doubao.jpg' },
  'deepl': { name: 'DeepL', logo: 'deepl-icon.svg', autoDark: true },
  'grok': { name: 'Grok', logo: 'grok-icon-black.svg', autoDark: true },
  'deepseek': { name: 'DeepSeek', logo: 'deepseek-icon.png' },
  'r1': { name: 'DeepSeek', logo: 'deepseek-icon.png' },
  'command': { name: 'Command', logo: 'cohere-icon.svg' },
  'jina': { name: 'Jina', logo: 'jina-icon-black.svg' },
}

/**
 * Model nodes
 * @deprecated migrated to laplace-workers
 */
export const customModels: ModelWithMeta[] = [
  {
    id: 'sonar',
    created: Date.now(),
    description: '内置联网检索功能',
    object: 'model',
    owned_by: 'pplx',
    hosted_by: 'pplx',
    tags: ['free'],
    links: {
      website: 'https://docs.perplexity.ai/docs/getting-starte/',
      pricingUrl: 'https://docs.perplexity.ai/guides/pricing',
      modelUrl: 'https://docs.perplexity.ai/docs/model-cards',
    },
    pricing: {
      inputCostPerMil: 1,
      outputCostPerMil: 1,
    },
    parameters: {
      maximumLength: {
        value: 200000,
        range: [0, 4096],
      },
    },
    features: {
      // tool_calling: true,
    },
  },
]

/**
 * 普通模型白名单
 */
export const whitelistFree = customModels
  .filter(
    model => (model.tags.includes('free') || model.tags.includes('limited-free')) && !model.tags.includes('hidden')
  )
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * 高级模型白名单
 */
export const whitelistPro = customModels
  .filter(model => model.tags.includes('pro') && !model.tags.includes('hidden'))
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * 企业版模型白名单
 */
export const whitelistEnterprise = customModels
  .filter(model => model.tags.includes('enterprise') && !model.tags.includes('hidden'))
  .map(model => model.id)
  .sort(Intl.Collator().compare)

/**
 * Get model details by given model name (ID)
 * @param model model name (ID)
 */
export function getModel(model: string) {
  const found = customModels.find(item => item.id === model || (item.aliases && item.aliases.includes(model)))
  return found
}

export function getModelFeatures(model: string) {
  const found = customModels.find(item => item.id === model)
  if (found?.features && Object.keys(found.features).length) {
    return found.features
  } else {
    return undefined
  }
}

// Group models by provider (owned_by + hosted_by)
export const modelsByProvider = customModels.reduce<Record<string, ModelWithMeta[]>>((acc, model) => {
  const providerKey = `${model.owned_by}_${model.hosted_by}`
  if (!acc[providerKey]) {
    acc[providerKey] = []
  }
  acc[providerKey].push(model)
  return acc
}, {})

export type RegistryProvider = Parameters<typeof createProviderRegistry>[0]
