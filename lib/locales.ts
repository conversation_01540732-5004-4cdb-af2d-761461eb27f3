/**
 * Standard language codes that the browser is using
 */
export const browserLocales = ['en', 'zh', 'ja', 'kr', 'ar', 'fr', 'de']

/**
 * Our custom route locales, based on BCP 47
 *
 * @see https://en.wikipedia.org/wiki/IETF_language_tag
 */
export const routeLocales = ['en', 'cn', 'ja', 'kr', 'ar', 'fr', 'de']

export const defaultLocale = 'cn'

/**
 * Cookie name for storing language preference
 */
export const LANGUAGE_COOKIE_NAME = 'NEXT_LOCALE'

/**
 * Mapping from browser locale to route locale
 */
export const localeMapping: Record<string, string> = {
  zh: 'cn',
  en: 'en',
  ja: 'ja',
  kr: 'kr',
  ar: 'ar',
  fr: 'fr',
  de: 'de',
}
