// This library is installed and imported only for enforcing types
import type { BaseHit, Hit } from 'instantsearch.js'
import type { HitsProps } from 'react-instantsearch'
import type { WP_Post, WP_REST_API_Post } from 'wp-types'

// 由于一些历史原因，WordPress 的默认 post 类型为 post，但所用到的 slug 为 posts，为了简化此行为，
// 自定义的 post type 就统一使用复数
export type PostType = 'post' | 'pages' | 'datasets' | 'wiki' | 'tutorials' | 'events' | 'hero-sliders'
export type PostTypeSlug = 'posts' | 'pages' | 'datasets' | 'wiki' | 'tutorials' | 'events' | 'hero-sliders'

export type AlgoliaAvailableRoutingParams = 'tags' | 'areas' | 'orgs'

// https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
export type Locale = 'en' | 'cn' | 'ja' | 'kr' | 'ar' | 'fr' | 'de'

export type TranslatedTag = {
  [key: string]: {
    cn: string
    en: string
    ja: string
    kr: string
    ar: string
    fr: string
    de: string
  }
}

export type PostOrder = 'asc' | 'desc'

export type RecentPostsProps = {
  exclude?: string | undefined
  type?: PostTypeSlug
  page?: number
  per_page?: number
  tags?: number
  orderby?: string
  order?: PostOrder
  // used by prefetching server-side api for i18n
  locale?: string
}

export type RelatedPostsProps = {
  id: number
  limit?: number
}

export interface HyperCategory {
  count: number
  id: number
  name: string
  slug: string
  taxonomy: string
  // taxonomy: 'category' | 'datasets-category';
}

export interface HyperTag {
  count: number
  id: number
  name: string
  slug: string
  taxonomy: string
  // taxonomy: 'post_tag';
}

export interface HyperCustomTag {
  count: number
  term_id: number
  name: string
  slug: string
  taxonomy: string
  description: string
  // taxonomy: 'post_tag';
}

export interface HyperOrgnization {
  count: number
  id: number
  name: string
  name_full_en: string
  name_full_cn: string
  logo_image: [string, number, number, boolean] | false
  /**
   * WordPress 此字段会返回空数组或 ""，不知道什么原因，需要做额外判断
   */
  logo_options:
    | /** 圆形 Logo */
    (
        | 'round_logo'
        /** 增加白色背景 */
        | 'bg_white'
        /** 增加黑色背景 */
        | 'bg_black'
        /** 额外边框 */
        | 'extra_border'
      )[]
    | []
    | ''
  slug: string
  description: string
  taxonomy: 'organization'
}

export interface UserAvatarUrls {
  '24': string
  '48': string
  '96': string
}

export interface UserLinks {
  self: Array<{ href: string }>
  collection: Array<{ href: string }>
}

export interface EmbeddedUser {
  id: number
  name: string
  url: string
  description: string
  link: string
  slug: string
  avatar_urls: UserAvatarUrls
  acf: Array<any>
  _links: UserLinks
}

export interface FeaturedMediaSizeItem {
  file?: string
  width?: number
  height?: number
  filesize?: number
  mime_type?: string
  source_url: string
}

/**
 * 部分无用字段改为可选，用于兼容 algolia.tsx 的拼接 post
 */
export interface FeaturedMediaItem {
  id?: number
  /**
   * '2024-04-10T17:34:07'
   */
  date?: string
  slug?: string
  type?: 'attachment'
  link?: string
  title?: {
    rendered: string
  }
  author?: number
  featured_media?: number
  acf?: []
  caption?: {
    rendered: string
  }
  alt_text?: string
  media_type?: 'image'
  mime_type?: string
  media_details: {
    width?: number
    height?: number
    file?: string
    filesize?: number
    sizes: {
      'medium'?: FeaturedMediaSizeItem
      'large'?: FeaturedMediaSizeItem
      'thumbnail'?: FeaturedMediaSizeItem
      'medium_large'?: FeaturedMediaSizeItem
      '1536x1536'?: FeaturedMediaSizeItem
      'yarpp-thumbnail'?: FeaturedMediaSizeItem
      'full'?: FeaturedMediaSizeItem
    }
    image_meta?: {
      aperture: string
      credit: string
      camera: string
      caption: string
      created_timestamp: number
      copyright: string
      focal_length: string
      iso: string
      shutter_speed: string
      title: string
      orientation: string
      keywords: string[]
    }
  }
  /**
   * Full size source URL
   */
  source_url: string
  _links?: {
    self: [
      {
        href: string
      },
    ]
    collection: [
      {
        href: string
      },
    ]
    about: [
      {
        href: string
      },
    ]
    author: [
      {
        embeddable: true
        href: string
      },
    ]
  }
}

export interface HyperApiPost extends WP_REST_API_Post {
  hyperai_categories?: HyperCategory[]
  hyperai_organizations?: HyperOrgnization[]
  hyperai_tags?: HyperTag[]
  acf?: {
    // fallback to unknown for extending acf, this avoid types errors from being extended
    [k: string]: unknown
    /**
     * 用于教程 post type 的 OpenBayes 教程链接
     */
    openbayes_url?: string
    /**
     * 数据集/教程的许可协议
     */
    license?: {
      value: string
      label: string
    }
    baidupan?: string
    baidupan_key?: string
    /**
     * 数据集/教程的论文链接
     */
    paper_url?: string
    /** 教程的 GitHub 链接 */
    github_url?: string
    publish_url?: string
    /**
     * 与当前数据集绑定的种子文件 ID
     */
    related_torrents?: number
    internal_status?: {
      label: string
      value: string
    }[]
    storage_location?: {
      label: string
      value: string
    }[]
    /** 首页幻灯片链接 */
    hero_link?:
      | {
          target: string
          title: string
          url: string
        }
      | ''
    /** 首页幻灯片标题 */
    title?: string
  }
  acf_extended?: {
    torrent_size?: string
    torrent_stats?: {
      name: string | false
      size: number
      seeders: number
      leechers: number
      hits: number
      completed: number
      files: string
    }
  }
  // Extend the _embedded property to include the FeaturedMediaItem
  _embedded?: {
    [k: string]: unknown
    'author': EmbeddedUser[]
    'replies'?: unknown[]
    'wp:term'?: unknown[]
    'wp:featuredmedia'?: FeaturedMediaItem[]
    'up'?: unknown[]
  }
}

export interface Venue {
  address: string
  lat: number
  lng: number
  zoom: number
  place_id: string
  city: string
  state: string
  state_short: string
  country: string
  country_short: string
}

export interface HyperApiPostEvent extends HyperApiPost {
  acf: {
    ccf_level: string
    cost: string
    h5_index: number
    /**
     * 投稿日期
     * @deprecated
     */
    submission_date: string
    /**
     * 截稿日期
     */
    end_date: string
    /**
     * 通知日期、结果公布日期
     */
    publish_date: string
    /**
     * 会议举办日期
     */
    start_date: string
    /**
     * 会议结束日期
     * @deprecated
     */
    event_end_date: string
    website: string
    venue: Venue | null
  }
}

export interface HyperPost extends WP_Post {
  hyperai_categories?: HyperCategory[]
  hyperai_tags?: HyperTag[]
}

// https://github.com/algolia/instantsearch/discussions/5468
export type HitsComponentProps<T extends BaseHit> = HitsProps<T>['hitComponent']

export interface HyperPostAlgoliaHit extends Hit {
  post_id: number
  post_type: string
  post_type_label: string
  post_title: string
  post_excerpt: string
  post_date: number
  post_date_formatted: string
  post_modified: number
  comment_count: number
  menu_order: number
  post_author: {
    user_id: number
    display_name: string
    user_url: string
    user_login: string
  }
  images: {
    thumbnail: {
      url: string
      width: number
      height: number
    }
  }
  permalink: string
  post_mime_type: string
  taxonomies: {
    post_tag: string[]
  }
  taxonomies_hierarchical: any[]
  is_sticky: number
  content: string
  record_index: number
}
