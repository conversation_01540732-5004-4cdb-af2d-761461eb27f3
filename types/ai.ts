// copied from laplace project
import type { Message } from '@ai-sdk/react'

export type ModelTag = 'free' | 'pro' | 'enterprise' | 'broken' | 'deprecated' | 'hidden' | 'limited-free'

/**
 * 给前端用的，避免暴露过多字段
 */
export type ModelWithPartialMeta = Pick<
  ModelWithMeta,
  'description' | 'features' | 'hosted_by' | 'id' | 'owned_by' | 'parameters' | 'pricing' | 'tags'
> & {
  usable: boolean
}

/**
 * 给前端用的，由于构建分组的模型列表
 */
export interface GroupedModels {
  [key: string]: ModelWithPartialMeta[]
}

/**
 * 纯前端的 Message 内容扩展
 */
export interface MessageEnhanced extends Message {
  /** @deprecated use `uiMetadata` instead */
  tokens?: number
  /** @deprecated use `uiMetadata` instead */
  model?: ModelWithPartialMeta
  /** @deprecated use `uiMetadata` instead */
  cost?: number
  uiMetadata?: {
    tokens?: number
    cost?: number
    model?: ModelWithPartialMeta
  }
}

export interface ModelWithMeta {
  /**
   * The model identifier, which can be referenced in the API endpoints.
   *
   * This field is inherited from openai/resources/models.ts
   */
  id: string

  /**
   * The Unix timestamp (in seconds) when the model was created.
   *
   * This field is inherited from openai/resources/models.ts
   */
  created: number

  /**
   * The object type, which is always "model".
   *
   * This field is inherited from openai/resources/models.ts
   */
  object: 'model'

  /**
   * The organization that owns the model.
   *
   * This field is inherited from openai/resources/models.ts
   */
  owned_by: string

  /**
   * 模型供应商，用于 getModelProvider 判断 sdk
   * @deprecated use `<provider>/<model>` notation instead
   */
  hosted_by?: string
  /**
   * 推理平台所用的完整 id，可与 id 不一样，例如 deepseek-r1 在 OpenRouter 上的 provider_id 为 deepseek/deepseek-r1
   */
  provider_id?: string
  parameters?: {
    // temperature: {
    //   value: 0.75
    //   range: [0.01, 5]
    // }
    maximumLength?: {
      /**
       * Max context
       */
      value?: number
      /**
       * Input token range
       */
      range?: [number, number]
      /**
       * Output max
       */
      output?: number
    }
    // topP: {
    //   value: 1
    //   range: [0.01, 1]
    // }
    // frequencyPenalty: {
    //   value: 1
    //   range: [0.01, 1]
    // }
  }
  description?: string
  /**
   * 模型别称，用于让其他第三方渠道依然可以获得对应的模型信息，例如带前缀的 openrouter
   * @deprecated use `provider_id` instead
   */
  aliases?: string[]
  /** 该模型是否为高级模型 */
  tags: ModelTag[]
  /**
   * 模型是否可用，给前端用的字段，通过 `getAvailableModels` 进行判断
   */
  links?: {
    website?: string
    modelUrl?: string
    pricingUrl?: string
  }
  pricing?: {
    currency?: string
    inputCostPerMil: number
    outputCostPerMil: number
    inputCostPerMilLocal?: number
    outputCostPerMilLocal?: number
  }
  features?: {
    function_calling?: boolean
    tool_calling?: boolean
    reasoning?: boolean
    /** 该模型是否会默认使用推理 */
    reasoningByDefault?: boolean
    image_generation?: boolean
    vision?: boolean
    file_uploading?: boolean
    accept_file_types?: string[]
  }
  permission?: [
    {
      id: string
      object: 'model_permission'
      created: number
      allow_create_engine: boolean
      allow_sampling: boolean
      allow_logprobs: boolean
      allow_search_indices: boolean
      allow_view: boolean
      allow_fine_tuning: boolean
      organization: string
      group: any
      is_blocking: boolean
    },
  ]
  deprecated?: boolean
  deprecated_reason?: string
  successor?: string[]
  made_in?: string
  hosted_in?: string
}

export declare namespace Serper {
  // https://serper.dev/playground
  interface WebResult {
    answerBox: {
      snippet: string
      snippetHighlighted: string[]
      title: string
      link: string
      /**
       * '2024年2月5日'
       */
      date: string
    }
    organic: {
      title: string
      link: string
      snippet: string
      /**
       * 小算增强数据
       */
      full_text: string
      date: string
      position: number
      sitelinks?: {
        title: string
        link: string
      }[]
    }[]
    relatedSearches: {
      query: string
    }[]
    peopleAlsoAsk: {
      question: string
      snippet: string
      title: string
      link: string
    }[]
    searchParameters: {
      engine: string
      q: string
      type: string
    }
  }
}

export type ChatAnnotations = ChatAnnotationWebResult | ChatAnnotationBilibiliLookup

export interface ChatAnnotationWebResult {
  type: 'web_search_annotation'
  data: Serper.WebResult[]
}

export interface ChatAnnotationBilibiliLookup {
  type: 'bilibili_lookup'
  data: {
    username: string
    uid: string
  }[]
}

export interface WebSearchQuery {
  q: string
}

/**
 * @deprecated use AiChatStatsV2 instead
 */
export interface AiChatStats {
  inputTokens: string
  outputTokens: string
  inputCosts: string
  outputCosts: string
  counter: string
  tokens: string
}
