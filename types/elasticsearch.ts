import type { estypes } from '@elastic/elasticsearch'

// Document types that map to Elasticsearch indexes
export interface HyperAIDocument {
  id: string
  title: string
  abstract?: string
  content?: string
  full_text?: string // Alternative content field
  body?: string // Some indexes use 'body' for content
  description?: string // Alternative field for content
  text?: string // Generic text field
  section?: string
  tags?: string[]
  tags_facts?: string[]
  link?: string
  createtime?: string
  createstamp?: number
}

// Search result hit type used in the UI
export interface SearchResultHit {
  objectID: string
  post_id: string
  post_title: string
  content?: string
  abstract?: string
  post_type: string
  post_type_label: string
  link?: string
  tags?: string[]
}

// Response type for search function
export interface SearchResponse {
  hits: SearchResultHit[]
}

// Elasticsearch query builder types
export type SearchQuery = estypes.QueryDslQueryContainer
export type SearchRequest = estypes.SearchRequest
export type SearchAggregations = estypes.AggregationsAggregationContainer

// Helper type for search hit with source
export type SearchHitWithSource<T> = estypes.SearchHit<T> & {
  _source: T
}

// Search page
export interface SearchResult {
  id: { raw: string }
  title: { snippet?: string; raw?: string }
  abstract: { snippet?: string; raw?: string }
  content: { snippet?: string; raw?: string }
  section: { raw: string }
  tags: { raw: string[] | string }
  tags_facts: { raw: string[] | string }
  createtime: { raw: string }
  createstamp: { raw: number }
  link: { raw: string }
}

export interface SearchConfig {
  searchQuery: {
    search_fields: Record<string, { weight: number }>
    result_fields: Record<string, any>
    disjunctiveFacets: string[]
    facets: Record<string, any>
  }
  autocompleteQuery: {
    results: any
    suggestions: any
  }
  apiConnector: any
  alwaysSearchOnInitialLoad: boolean
}

export interface IndexConfig {
  baseIndex: string
  locale: string
  searchPlaceholder?: string
  labels?: Record<string, string>
}

export interface SearchDictionary {
  search: {
    placeholder: string
    sortBy: string
    relevance: string
    newest: string
    oldest: string
    section: string
    createTime: string
    displayTags: string
    noTitle: string
    uncategorized: string
    last7Days: string
    last7To30Days: string
    last30To90Days: string
    last90DaysTo1Year: string
    over1Year: string
    noResults: string
    searchError: string
    loading: string
    searchResult?: string
    tags: string
  }
}

export interface SearchContextProps {
  wasSearched: boolean
  isLoading: boolean
  error: Error | null
}

export interface ResultViewProps {
  result: SearchResult
  onClickLink?: () => void
}
