/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/v1/{lang}/news/fetch': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Fetch News
     * @description 获取最新新闻，按照分页显示
     */
    get: operations['fetch_news_v1__lang__news_fetch_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/news/topic/{topic_id}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get News Topic
     * @description 获取指定新闻话题详情
     */
    get: operations['get_news_topic_v1__lang__news_topic__topic_id__get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/papers/date/{data_timestamp}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Papers
     * @description 获取指定日期的最新论文
     */
    get: operations['get_papers_v1__lang__papers_date__data_timestamp__get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/papers/latest': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Papers
     * @description 获取当前时间前24小时内的最新论文
     */
    get: operations['get_papers_v1__lang__papers_latest_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/papers/fetch': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Papers
     * @description 获取最新论文，按照分页显示
     */
    get: operations['get_papers_v1__lang__papers_fetch_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/papers/{paper_id}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Paper Detail
     * @description 获取指定论文的详细信息
     */
    get: operations['get_paper_detail_v1__lang__papers__paper_id__get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/sota': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get All Sota
     * @description 获取所有任务列表
     */
    get: operations['get_all_sota_v1__lang__sota_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/category/{area_id}/all': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /** Get Sota Category */
    get: operations['get_sota_category_v1__lang__category__area_id__all_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/task/{task_id}/info': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /** Get Task Info */
    get: operations['get_task_info_v1__lang__task__task_id__info_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/task/{task_id}/benchmark/{evaluation_id}': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /** Get Task Bench */
    get: operations['get_task_bench_v1__lang__task__task_id__benchmark__evaluation_id__get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/v1/{lang}/benchmark/software': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /**
     * Get Gpu Software Benchmark
     * @description 获取GPU软件评测信息
     */
    get: operations['get_gpu_software_benchmark_v1__lang__benchmark_software_get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
  '/': {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    /** Root */
    get: operations['root__get']
    put?: never
    post?: never
    delete?: never
    options?: never
    head?: never
    patch?: never
    trace?: never
  }
}
export type webhooks = Record<string, never>
export interface components {
  schemas: {
    /** BenchmarkItem */
    BenchmarkItem: {
      /** Gpu */
      'gpu': string
      /** Max Context Length */
      'Max Context Length': number
      /** Max Sequences */
      'Max Sequences': number
      'metrics': components['schemas']['Metrics']
    }
    /** BenchmarkResponse */
    BenchmarkResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      data: components['schemas']['EvaluationBenchmarkItem']
    }
    /** BenchmarkResult */
    BenchmarkResult: {
      /** Result Id */
      result_id: string
      /** Metrics */
      metrics: {
        [key: string]: unknown
      } | null
      /** Paper */
      paper: string | null
      /** Paper Title */
      paper_title: string | null
      /** Paper Url */
      paper_url: string | null
      /** Paper Repo */
      paper_repo: string | null
      /** Model Name */
      model_name: string | null
      /** Evaluated At */
      evaluated_at: string | null
    }
    /** CategoryResponse */
    CategoryResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      data: components['schemas']['SotaCategory']
    }
    /** EvaluationBenchmarkItem */
    EvaluationBenchmarkItem: {
      /** Evaluation Id */
      evaluation_id: string
      /** Evaluation Name */
      evaluation_name: string
      /** Result List */
      result_list: components['schemas']['BenchmarkResult'][]
    }
    /** EvaluationItem */
    EvaluationItem: {
      /** Evaluation Id */
      evaluation_id: string
      /** Dataset */
      dataset: string
      /** Dataset Name */
      dataset_name: string | null
      /** Dataset Url */
      dataset_url: string | null
      /** Best Model */
      best_model: string | null
    }
    /** GPUSoftwareBenchmark */
    GPUSoftwareBenchmark: {
      /** Env */
      env: string
      /** Model */
      model: string
      /** List */
      list: components['schemas']['BenchmarkItem'][]
    }
    /** GPUSoftwareBenchmarkResponse */
    GPUSoftwareBenchmarkResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      /** Data */
      data: components['schemas']['GPUSoftwareBenchmark'][]
    }
    /** HTTPValidationError */
    HTTPValidationError: {
      /** Detail */
      detail?: components['schemas']['ValidationError'][]
    }
    /** Metrics */
    Metrics: {
      /** Request Throughput */
      'Request Throughput': number
      /** Output Token Throughput */
      'Output Token Throughput': number
      /** Total Token Throughput */
      'Total Token Throughput': number
    }
    /** NewsLink */
    NewsLink: {
      /** Link */
      link: string
      /** Title */
      title: string
      /** Site Name */
      site_name: string
    }
    /** NewsResponse */
    NewsResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      /** Data */
      data: components['schemas']['NewsTopicBase'][]
    }
    /** NewsTopicBase */
    NewsTopicBase: {
      /** Topic Id */
      topic_id: string
      /** Title */
      title: string
      /** Create At */
      create_at: string
      /** Created Timestamp */
      created_timestamp: number
      /** Is Hidden */
      is_hidden: number
      /** Sticky */
      sticky: boolean
      /** Snippet */
      snippet?: string | null
    }
    /** NewsTopicDetail */
    NewsTopicDetail: {
      /** Topic Id */
      topic_id: string
      /** Title */
      title: string
      /** Create At */
      create_at: string
      /** Created Timestamp */
      created_timestamp: number
      /** Is Hidden */
      is_hidden: number
      /** Sticky */
      sticky: boolean
      /** Snippet */
      snippet?: string | null
      /** Abstract */
      abstract: string
      /** Links */
      links: components['schemas']['NewsLink'][]
    }
    /** NewsTopicResponse */
    NewsTopicResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      data: components['schemas']['NewsTopicDetail']
    }
    /** PaperBase */
    PaperBase: {
      /** Paper Id */
      paper_id: string
      /** Title */
      title: string
      /** Authors */
      authors: string[]
      /** Fetch At */
      fetch_at: string
      /** Create At */
      create_at: string
      /** Created Timestamp */
      created_timestamp: number
      /** Img Url */
      img_url: string
      /** Snippet */
      snippet?: string | null
    }
    /** PaperDetail */
    PaperDetail: {
      /** Paper Id */
      paper_id: string
      /** Title */
      title: string
      /** Authors */
      authors: string[]
      /** Fetch At */
      fetch_at: string
      /** Create At */
      create_at: string
      /** Created Timestamp */
      created_timestamp: number
      /** Img Url */
      img_url: string
      /** Snippet */
      snippet?: string | null
      /** Paper Link */
      paper_link: string
      /** Github Link */
      github_link?: string | null
      /** Abstract */
      abstract: string
    }
    /** PaperDetailResponse */
    PaperDetailResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      data: components['schemas']['PaperDetail']
    }
    /** PapersResponse */
    PapersResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      /** Data */
      data: components['schemas']['PaperBase'][]
    }
    /** SotaAllCategory */
    SotaAllCategory: {
      /** Area Id */
      area_id: string
      /** Area Name */
      area_name: string
      /** Task All Num */
      task_all_num: number
      /** Abstract Area 100 */
      abstract_area_100?: string | null
      /** Task List */
      task_list: components['schemas']['TaskItem'][]
    }
    /** SotaCategory */
    SotaCategory: {
      /** Area Id */
      area_id: string
      /** Area Name */
      area_name: string
      /** Abstract Area 100 */
      abstract_area_100?: string | null
      /** Task List */
      task_list: components['schemas']['TaskItem'][]
    }
    /** SotaResponse */
    SotaResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      /** Data */
      data: components['schemas']['SotaAllCategory'][]
    }
    /** TaskInfoItem */
    TaskInfoItem: {
      /** Task Id */
      task_id: string
      /** Task Name */
      task_name: string
      /** Abstract Task 100 */
      abstract_task_100?: string | null
      /** Evaluation List */
      evaluation_list: components['schemas']['EvaluationItem'][]
    }
    /** TaskInfoResponse */
    TaskInfoResponse: {
      /**
       * R
       * @default 200
       */
      r: number
      /** Lang */
      lang: string
      data: components['schemas']['TaskInfoItem']
    }
    /** TaskItem */
    TaskItem: {
      /** Task Id */
      task_id: string
      /** Task Name */
      task_name: string
      /** Num Papers */
      num_papers: number
      /** Num Benchmarks */
      num_benchmarks: number
      /** Num Models */
      num_models: number
      /** Abstract Task 100 */
      abstract_task_100?: string | null
    }
    /** ValidationError */
    ValidationError: {
      /** Location */
      loc: (string | number)[]
      /** Message */
      msg: string
      /** Error Type */
      type: string
    }
  }
  responses: never
  parameters: never
  requestBodies: never
  headers: never
  pathItems: never
}
export type $defs = Record<string, never>
export interface operations {
  fetch_news_v1__lang__news_fetch_get: {
    parameters: {
      query?: {
        /** @description Page number */
        page_num?: number
        /** @description Items per page */
        page_size?: number
        /** @description Eidtor sticked news. */
        sticky_num?: number
      }
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['NewsResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_news_topic_v1__lang__news_topic__topic_id__get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description Topic ID */
        topic_id: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['NewsTopicResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_papers_v1__lang__papers_date__data_timestamp__get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description data_timestamp */
        data_timestamp: number
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PapersResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_papers_v1__lang__papers_latest_get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PapersResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_papers_v1__lang__papers_fetch_get: {
    parameters: {
      query?: {
        /** @description Page number */
        page_num?: number
        /** @description Items per page */
        page_size?: number
      }
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PapersResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_paper_detail_v1__lang__papers__paper_id__get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description Paper ID (arxiv ID, doi, etc.) */
        paper_id: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['PaperDetailResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_all_sota_v1__lang__sota_get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['SotaResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_sota_category_v1__lang__category__area_id__all_get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description Area ID */
        area_id: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['CategoryResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_task_info_v1__lang__task__task_id__info_get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description Task ID */
        task_id: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['TaskInfoResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_task_bench_v1__lang__task__task_id__benchmark__evaluation_id__get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
        /** @description Task ID */
        task_id: string
        /** @description Evaluation ID */
        evaluation_id: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['BenchmarkResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  get_gpu_software_benchmark_v1__lang__benchmark_software_get: {
    parameters: {
      query?: never
      header?: never
      path: {
        /** @description Language code ('cn', 'en', 'ja', 'fr', 'kr', 'ar', 'de') */
        lang: string
      }
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['GPUSoftwareBenchmarkResponse']
        }
      }
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': components['schemas']['HTTPValidationError']
        }
      }
    }
  }
  root__get: {
    parameters: {
      query?: never
      header?: never
      path?: never
      cookie?: never
    }
    requestBody?: never
    responses: {
      /** @description Successful Response */
      200: {
        headers: {
          [name: string]: unknown
        }
        content: {
          'application/json': unknown
        }
      }
    }
  }
}
