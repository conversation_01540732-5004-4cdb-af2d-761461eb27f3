// @ts-check
import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  reactStrictMode: true,
  output: 'standalone',
  poweredByHeader: false,
  images: {
    minimumCacheTTL: 31536000,

    // Enable AVIF format
    // https://nextjs.org/docs/api-reference/next/image#acceptable-formats
    formats: [
      // 'image/avif',
      'image/webp',
    ],

    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'hyper.ai',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'orion.hyper.ai',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'hyperai-backend.sg.ufileos.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // https://nextjs.org/docs/api-reference/next.config.js/redirects
  async redirects() {
    return [
      // 不再使用 Next.js 的转向处理，此功能的转向会转译/escape 导致冲突，例如：
      // https://hyper.ai/tracker/scrape?info_hash=%2BYX%05%13%A49%E6%E3%9E%18qH%C1%85%02%F2~%BB%BB
      // 通过本功能的转向会导致 hash 错误，因此需要在 nginx 中处理
      // {
      //   // Make sure torrent tracker still working
      //   source: '/tracker/:path*',
      //   // destination: 'https://t.orion.hyper.ai/:path*',
      //   destination: 'https://orion.hyper.ai/tracker/:path*',
      //   basePath: false,
      //   permanent: true,
      // },
      {
        // Redirect RSS
        source: '/feed/:path*',
        destination: 'https://orion.hyper.ai/feed/:path*',
        basePath: false,
        permanent: true,
      },
    ]
  },
}

export default nextConfig
