/**
 * Fetch all users from Logto API and save to CSV
 */

import * as fs from 'fs'
import * as path from 'path'

import { getLogtoAdminToken } from '@/utils/getLogtoAdminToken'

interface LogtoUser {
  id: string
  username?: string
  primaryEmail?: string
  primaryPhone?: string
  name?: string
  avatar?: string
  lastSignInAt?: number
  createdAt: number
  updatedAt: number
  isSuspended?: boolean
  hasPassword?: boolean
  [key: string]: any // For additional properties
}

/**
 * Convert array of users to CSV string
 */
function convertToCSV(users: LogtoUser[]): string {
  if (users.length === 0) {
    return ''
  }

  // Get all unique keys from all users to ensure we capture all possible fields
  const allKeys = new Set<string>()
  users.forEach(user => {
    Object.keys(user).forEach(key => allKeys.add(key))
  })

  // Create header row, filtering out nested objects
  const headerKeys = Array.from(allKeys).filter(key => typeof users[0]![key] !== 'object' || users[0]![key] === null)
  const header = headerKeys.join(',')

  // Create data rows
  const rows = users.map(user => {
    return headerKeys
      .map(key => {
        const value = user[key]

        // Handle different value types
        if (value === null || value === undefined) {
          return ''
        } else if (typeof value === 'object') {
          return '""' // Skip complex objects
        } else if (typeof value === 'string') {
          // Escape quotes and wrap in quotes
          return `"${value.replace(/"/g, '""')}"`
        } else {
          return String(value)
        }
      })
      .join(',')
  })

  return [header, ...rows].join('\n')
}

/**
 * Fetch all users from Logto API
 */
async function fetchAllUsers(): Promise<LogtoUser[]> {
  const tokenResponse = await getLogtoAdminToken()
  const accessToken = tokenResponse.access_token

  if (!accessToken) {
    throw new Error('Failed to get access token')
  }

  if (!process.env['LOGTO_ENDPOINT']) {
    throw new Error('LOGTO_ENDPOINT environment variable is required')
  }

  const baseUrl = `${process.env['LOGTO_ENDPOINT']}/api/users`
  let allUsers: LogtoUser[] = []
  let hasNextPage = true
  let page = 1
  const pageSize = 100

  console.log('Fetching users from Logto API...')

  while (hasNextPage) {
    // Construct proper URL for each page
    const url = page === 1 ? baseUrl : `${baseUrl}?page=${page}&page_size=${pageSize}`
    console.log(`Fetching ${url}`)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const users = (await response.json()) as LogtoUser[]
    allUsers = [...allUsers, ...users]

    console.log(`Fetched ${users.length} users (total: ${allUsers.length})`)

    // Extract Link header for pagination
    const linkHeader = response.headers.get('Link')

    if (linkHeader && linkHeader.includes('rel="next"')) {
      // Instead of using the next URL directly, we increment the page number
      page++
    } else {
      hasNextPage = false
    }
  }

  return allUsers
}

/**
 * Main function to fetch all users and save to CSV
 */
async function saveUsersToCSV(): Promise<void> {
  try {
    const users = await fetchAllUsers()
    const csvContent = convertToCSV(users)

    // Create output directory if it doesn't exist
    const outputDir = path.resolve(process.cwd(), 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const outputPath = path.join(outputDir, `logto-users-${timestamp}.csv`)

    fs.writeFileSync(outputPath, csvContent)
    console.log(`Successfully saved ${users.length} users to ${outputPath}`)
  } catch (error) {
    console.error('Error saving users to CSV:', error)
    process.exit(1)
  }
}

// Execute if run directly
if (require.main === module) {
  saveUsersToCSV()
}

export default saveUsersToCSV
