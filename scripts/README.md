# HyperAI Scripts

This directory contains utility scripts for the HyperAI project.

## Tag Management Scripts

### Find Missing Tags

The `find-missing-tags.ts` script compares tags from the JSON file with those in the TypeScript file and generates a new file with the missing tags.

```bash
# Make the script executable
chmod +x scripts/find-missing-tags.ts

# Run the script for datasets (default if no type is specified)
./scripts/find-missing-tags.ts

# Or specify a tag type
./scripts/find-missing-tags.ts datasets
./scripts/find-missing-tags.ts events
./scripts/find-missing-tags.ts tutorials
```

### Process All Tag Types at Once

The `get-all-missing-tags.ts` script runs the find-missing-tags script for all tag types (datasets, events, tutorials).

```bash
# Make the script executable
chmod +x scripts/get-all-missing-tags.ts

# Run the script
./scripts/get-all-missing-tags.ts
```

## Authentication Scripts

### Logto Token Generator

The `logto-get-token.ts` script helps generate authentication tokens for the Logto service.

```bash
# Make the script executable
chmod +x scripts/logto-get-token.ts

# Run the script
./scripts/logto-get-token.ts
```

### Get All Users

The `get-all-users.ts` script retrieves user information from the authentication system.

```bash
# Make the script executable
chmod +x scripts/get-all-users.ts

# Run the script
./scripts/get-all-users.ts
```

## Data Conversion Scripts

### GPU Benchmark Converter

The `convert-gpu-benchmark.ts` script converts GPU benchmark data from CSV format to a structured TypeScript file using the xlsx library.

#### Purpose

This script takes the raw GPU benchmark data from `data/gpu-benchmark.csv` and converts it into a well-structured TypeScript object in `data/gpu-benchmark.ts`. The conversion process:

1. Reads the CSV file from the data directory using SheetJS (xlsx)
2. Parses the CSV data into a JavaScript object structure
3. Cleans and normalizes field names and values to handle special characters and formatting issues
4. Automatically generates a TypeScript interface based on the actual data structure
5. Creates a type-safe array of GPU benchmark objects

#### Dependencies

- xlsx (SheetJS): Used for robust CSV parsing

#### Usage

```bash
# Make the script executable
chmod +x scripts/convert-gpu-benchmark.ts

# Run the script directly
./scripts/convert-gpu-benchmark.ts

# Or use the npm script
pnpm generate:gpu-data
```

#### Output

The script generates `data/gpu-benchmark.ts` with the following structure:

```typescript
// Generated from data/gpu-benchmark.csv
export interface GPUBenchmark {
  "GPU": string | null;
  "Release Date": string | null;
  "Architecture": string | null;
  "CUDA Cores": string | null;
  "GPU memory": string | null;
  "Total Graphics Power": string | null;
  "FP8": string | null;
  "FP16": string | null;
  "FP32": string | null;
  "FP64": string | null;
}

export const gpuBenchmarks: GPUBenchmark[] = [
  {
    "GPU": "NVIDIA Tesla V100 PCIe 16 GB",
    "Release Date": "2017.06",
    "Architecture": "NVIDIA Volta",
    // ... other properties
  },
  // ... other GPU entries
]
```

#### Data Cleaning

The script applies several data cleaning steps:
- Removes tab characters and whitespace from field names
- Normalizes field names for consistency (particularly "Total Graphics Power")
- Removes newlines and normalizes spacing in values
- Trims whitespace from all values
- Filters out empty rows

This ensures the output data is clean and consistent for use in the application.
