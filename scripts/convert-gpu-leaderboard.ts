#!/usr/bin/env bun
import { existsSync, readFileSync, writeFileSync } from 'fs'
import { join } from 'path'
import * as XLSX from 'xlsx'

// Set file paths
const csvFilePath = join(process.cwd(), 'data', 'gpu-leaderboard.csv')
const outputFilePath = join(process.cwd(), 'data', 'gpu-leaderboard.ts')

try {
  // Check if the CSV file exists
  if (!existsSync(csvFilePath)) {
    throw new Error(`CSV file not found at: ${csvFilePath}`)
  }

  console.log(`Reading CSV file from: ${csvFilePath}`)

  // First read the file as a string to check its content
  const fileContent = readFileSync(csvFilePath, 'utf8')
  if (!fileContent || fileContent.trim() === '') {
    throw new Error('CSV file is empty')
  }

  // Read the CSV file using xlsx
  const workbook = XLSX.read(fileContent, { type: 'string' })
  const sheetName = workbook.SheetNames[0]!
  const worksheet = workbook.Sheets[sheetName]!

  // Convert the worksheet to JSON with headers
  const rawData = XLSX.utils.sheet_to_json<Record<string, any>>(worksheet, { raw: false, defval: null })

  // Clean and format the data
  const gpuData = rawData
    .filter(row => row.GPU && row.GPU.trim() !== '') // Filter out empty rows
    .map(row => {
      const cleanedRow: Record<string, string | null> = {}

      // Process each field, cleaning up the data
      Object.entries(row).forEach(([key, value]) => {
        // Clean the key - normalize field names
        const cleanKey = key.trim().replace(/^\t+/, '').replace(/\s+/g, ' ')

        let cleanValue = value

        if (typeof cleanValue === 'string') {
          // Remove newlines, normalize quotes, and trim
          cleanValue = cleanValue.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim()
        }

        cleanedRow[cleanKey] = cleanValue || null
      })

      return cleanedRow
    })

  // Normalize field names in all rows to ensure consistency
  const normalizedData = gpuData.map(row => {
    const normalizedRow: Record<string, string | null> = {}
    Object.entries(row).forEach(([key, value]) => {
      // Normalize problematic field names
      if (key.includes('Total Graphics Power')) {
        normalizedRow['Total Graphics Power'] = value
      } else {
        normalizedRow[key] = value
      }
    })
    return normalizedRow
  })

  // Get corrected headers
  const cleanHeaders = Object.keys(normalizedData[0] || {})

  // Generate TypeScript interface from the headers
  const interfaceProps = cleanHeaders.map(key => `  ${JSON.stringify(key)}: string | null;`).join('\n')

  // Generate TypeScript content
  const tsContent = `// Generated from data/gpu-leaderboard.csv
export interface GPULeaderboard {
${interfaceProps}
}

export const gpuLeaderboard: GPULeaderboard[] = ${JSON.stringify(normalizedData, null, 2)}
`

  // Write to file
  writeFileSync(outputFilePath, tsContent)
  console.log(`Successfully converted GPU leaderboard data to ${outputFilePath}`)
  console.log(`Processed ${normalizedData.length} GPU entries`)
} catch (error) {
  console.error(`Error processing CSV file: ${error}`)
  process.exit(1)
}
