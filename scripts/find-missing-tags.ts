#!/usr/bin/env bun
import { readFileSync, writeFileSync } from 'fs'

// Get tag type from command line argument, default to 'datasets'
const tagType = process.argv[2] || 'datasets'
const validTypes = ['datasets', 'events', 'tutorials']

if (!validTypes.includes(tagType)) {
  console.error(`Error: Invalid tag type '${tagType}'. Valid types are: ${validTypes.join(', ')}`)
  process.exit(1)
}

// Construct file paths based on tag type
const jsonFilePath = `./data/tags-${tagType}.json`
const tsFilePath = `./data/tags-${tagType}.ts`
const outputFilePath = `./data/missing-tags-${tagType}.ts`

// Read both files
try {
  var jsonContent = JSON.parse(readFileSync(jsonFilePath, 'utf8')) as Array<{ name: string }>
  var tsFileContent = readFileSync(tsFilePath, 'utf8')
} catch (error) {
  console.error(`Error reading files: ${error}`)
  process.exit(1)
}

// Extract tag names from JSON file
const jsonTags: string[] = jsonContent.map(item => item.name)

// Extract tag keys from TS file - using a better pattern that matches the specific format
// Looking for patterns like: { key: { cn: 'value', ... }, },
const tsTagPattern = /{\s*((['"])([^'"]+)\2|([^:]+)):\s*{/g
const tsTags: string[] = []
let match: RegExpExecArray | null

while ((match = tsTagPattern.exec(tsFileContent)) !== null) {
  // Extract the tag name - either in quotes or not
  const tagName = match[3] || match[4]?.trim() || ''
  tsTags.push(tagName)
}

console.log(`[${tagType}] Found tags in TS file:`, tsTags.length)
console.log(`[${tagType}] Found tags in JSON file:`, jsonTags.length)

// Find missing tags - ones that are in JSON but not in TS
const missingTags: string[] = jsonTags.filter(tag => !tsTags.includes(tag))

// Generate new content
const newTagsContent = missingTags
  .map(tag => {
    return `  {
    ${JSON.stringify(tag)}: {
      cn: ${JSON.stringify(tag)},
      en: '',
      ja: '',
      kr: '',
      ar: '',
      fr: '',
      de: '',
    },
  },`
  })
  .join('\n')

// Create output file
const capitalizedTagType = tagType.charAt(0).toUpperCase() + tagType.slice(1)
const outputContent = `// Generated missing tags for ${tagType} from data/tags-${tagType}.json
import type { TranslatedTag } from '@/types'

export const missingTags${capitalizedTagType}: TranslatedTag[] = [
${newTagsContent}
]
`

// Write to file
writeFileSync(outputFilePath, outputContent)

// Log summary
console.log(`[${tagType}] Found ${missingTags.length} missing tags and saved to ${outputFilePath}`)
if (missingTags.length > 0) {
  console.log(`[${tagType}] Missing tags:`, missingTags.join(', '))
}
