#!/usr/bin/env bun
import { spawnSync } from 'child_process'

// List of tag types to process
const tagTypes = ['datasets', 'events', 'tutorials']

console.log('Finding missing tags for all types...\n')

// Process each tag type
tagTypes.forEach(type => {
  console.log(`=== Processing ${type} ===`)

  // Run the find-missing-tags script with the current tag type
  const result = spawnSync('./scripts/find-missing-tags.ts', [type], {
    stdio: 'inherit',
    encoding: 'utf-8',
  })

  if (result.status !== 0) {
    console.error(`Error processing ${type}. Exit code: ${result.status}`)
  }

  console.log('')
})

console.log('All tag types processed!')
console.log('Review the generated files:')
tagTypes.forEach(type => {
  console.log(`- data/missing-tags-${type}.ts`)
})
