{
  "files.associations": {
    "*.css": "tailwindcss"
  },
  // https://cva.style/docs/getting-started/installation
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "editor.quickSuggestions": {
    "strings": "on"
  },
  "tailwindCSS.emmetCompletions": true,
  "scss.lint.unknownAtRules": "ignore"
}
