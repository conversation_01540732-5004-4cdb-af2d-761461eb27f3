'use client'

import React from 'react'
import useS<PERSON>, { type Fetcher } from 'swr'
import type { LogtoContext } from '@logto/next/edge'
import { LogLevel, StatsigProvider, useClientAsyncInit } from '@statsig/react-bindings'
import { StatsigSessionReplayPlugin } from '@statsig/session-replay'
import { StatsigAutoCapturePlugin } from '@statsig/web-analytics'

const fetcher: Fetcher<LogtoContext> = async (...args: Parameters<typeof fetch>) => {
  const resp = await fetch(...args)
  if (!resp.ok) {
    throw new Error('An error occurred while fetching the user data')
  }
  return resp.json()
}

export default function MyStatsig({ children }: { children: React.ReactNode }) {
  const { data, isLoading } = useSWR('/api/auth/user', fetcher)

  // Build user object based on Logto authentication status
  const user = React.useMemo(() => {
    if (data?.isAuthenticated && data?.claims) {
      return {
        userID: data.claims.sub || 'anonymous', // 'sub' is the standard OIDC claim for user ID
        email: data.claims.email || undefined, // Convert null to undefined for Statsig compatibility
        custom: {
          name: data.claims.name || undefined,
          picture: data.claims.picture || undefined,
          emailVerified: data.claims.email_verified,
          // Add any other custom claims you want to track
        },
      }
    }

    // Default user for unauthenticated users
    return {
      userID: 'anonymous',
      custom: {
        isAuthenticated: false,
      },
    }
  }, [data])

  const { client } = useClientAsyncInit('client-wgbAi7W8qHVnEr2rFKTvInxaDB9H6rh7E3woPVKaH8U', user, {
    plugins: [new StatsigAutoCapturePlugin(), new StatsigSessionReplayPlugin()],
    logLevel: LogLevel.Debug,
  })

  // Don't initialize Statsig until we know the user's authentication status
  if (isLoading || !client) {
    return <>{children}</>
  }

  return <StatsigProvider client={client}>{children}</StatsigProvider>
}
