// https://github.com/logto-io/js/blob/master/packages/next-app-dir-sample/app/api/logto/user/get-user.ts
// app/api/logto/user/get-user.ts
import { cookies } from 'next/headers'
import { type LogtoContext } from '@logto/next'

import { logtoConfig } from '@/lib/logto'

// `server-only` guarantees any modules that import code in file
// will never run on the client. Even though this particular api
// doesn't currently use sensitive environment variables, it's
// good practise to add `server-only` preemptively.
import 'server-only'

export async function getUser() {
  const cookieStore = await cookies()
  const response = await fetch(`${logtoConfig.baseUrl}/api/auth/user`, {
    cache: 'no-store',
    headers: {
      cookie: cookieStore.toString(),
    },
  })

  if (!response.ok) {
    throw new Error('Something went wrong!')
  }

  const user: LogtoContext = await response.json()

  return user
}
