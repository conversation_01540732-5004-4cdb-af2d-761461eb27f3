import { NextRequest, NextResponse } from 'next/server'
import type { estypes } from '@elastic/elasticsearch'

import type { HyperAIDocument, SearchResponse } from '@/types/elasticsearch'

import { ES_BASE_URL, ES_SEARCH_INDEX_NAME } from '@/lib/constants'
import { rateLimit } from '@/lib/middleware/rateLimit'

// Environment variables (server-side only)
const ES_HOST = process.env['ES_HOST'] || ES_BASE_URL
const ES_API_KEY = process.env['ES_API_KEY'] || 'N2MyM2FaY0JTTjNIY20tNFRndkw6WlFmZGVNa3g4aDR3VW5xaWZ2Z2Nidw=='

// Simple in-memory cache (consider using Redis in production)
const searchCache = new Map<string, { data: SearchResponse; timestamp: number }>()
const CACHE_TTL = 60 * 1000 // 1 minute cache

interface SearchRequestBody {
  query: string
  locale: string
  size?: number
}

const limiter = rateLimit({ windowMs: 10 * 1000, max: 100 }) // 100 requests per 10 seconds

export async function POST(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = await limiter(request)
  if (rateLimitResult instanceof NextResponse) {
    return rateLimitResult
  }

  try {
    const body: SearchRequestBody = await request.json()
    const { query, locale, size = 10 } = body

    // Validate input
    if (!query || typeof query !== 'string') {
      return NextResponse.json({ error: 'Invalid query parameter' }, { status: 400 })
    }

    if (!locale || typeof locale !== 'string') {
      return NextResponse.json({ error: 'Invalid locale parameter' }, { status: 400 })
    }

    // Sanitize query to prevent injection
    const sanitizedQuery = query.trim().slice(0, 200) // Limit query length

    // Check cache first
    const cacheKey = `${sanitizedQuery}-${locale}-${size}`
    const cached = searchCache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return NextResponse.json(cached.data)
    }

    // Build index name
    const indexName = `${ES_SEARCH_INDEX_NAME}${locale}`

    // Build search request
    const searchBody: estypes.SearchRequest = {
      query: {
        bool: {
          must: [
            {
              bool: {
                should: [
                  { match: { title: { query: sanitizedQuery, boost: 2, minimum_should_match: '75%' } } },
                  { match: { tags: { query: sanitizedQuery, boost: 3, minimum_should_match: '100%' } } },
                  { match: { abstract: { query: sanitizedQuery, boost: 2, minimum_should_match: '100%' } } },
                ],
                minimum_should_match: 1,
              },
            },
          ],
          should: [
            { match: { title: { query: sanitizedQuery, boost: 3, operator: 'and' } } },
            { match: { content: { query: sanitizedQuery, boost: 1, operator: 'and' } } },
            { match: { full_text: { query: sanitizedQuery, boost: 1, operator: 'and' } } },
            {
              function_score: {
                query: { match_all: {} },
                functions: [
                  {
                    filter: { term: { 'title.keyword': sanitizedQuery } },
                    weight: 5,
                  },
                  {
                    filter: { term: { 'tags.keyword': sanitizedQuery } },
                    weight: 5,
                  },
                ],
                boost_mode: 'sum',
              },
            },
          ],
          minimum_should_match: 0,
        },
      },
      sort: [{ _score: { order: 'desc' } }, { createtime: 'desc' }],
      size: 10,
      aggs: {
        section_groups: {
          terms: {
            field: 'section',
            order: { _key: 'desc' },
            size: 8,
          },
          aggs: {
            top_docs: {
              top_hits: {
                size: 1,
                _source: ['id', 'section', 'title', 'tags', 'abstract', 'createtime'],
              },
            },
            section_count: {
              value_count: {
                field: 'section',
              },
            },
          },
        },
      },
      highlight: {
        fields: {
          title: { number_of_fragments: 1 },
          content: { number_of_fragments: 2, fragment_size: 150 },
          abstract: { number_of_fragments: 1, fragment_size: 200 },
        },
      },
    }

    // Make request to Elasticsearch
    const response = await fetch(`${ES_HOST}/${indexName}/_search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `ApiKey ${ES_API_KEY}`,
      },
      body: JSON.stringify(searchBody),
    })

    if (!response.ok) {
      console.error('Elasticsearch error:', response.statusText)
      return NextResponse.json({ error: 'Search service unavailable' }, { status: 503 })
    }

    const data = (await response.json()) as estypes.SearchResponse<HyperAIDocument>

    // Transform results
    const hits =
      data.hits?.hits?.map(hit => {
        const source = hit._source!
        return {
          objectID: hit._id || '',
          post_id: source.id,
          post_title: hit.highlight?.title?.[0] || source.title,
          content: hit.highlight?.content?.join(' ... ') || source.content,
          abstract: hit.highlight?.abstract?.[0] || source.abstract,
          post_type: source.section || 'post',
          post_type_label: source.section || 'Post',
          link: source.link,
          tags: source.tags,
        }
      }) || []

    const searchResponse: SearchResponse = { hits }

    // Cache the results
    searchCache.set(cacheKey, {
      data: searchResponse,
      timestamp: Date.now(),
    })

    // Clean up old cache entries periodically
    if (searchCache.size > 100) {
      const now = Date.now()
      for (const [key, value] of searchCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
          searchCache.delete(key)
        }
      }
    }

    return NextResponse.json(searchResponse, { headers: rateLimitResult.headers })
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Optional: Add GET method for health check
export async function GET() {
  return NextResponse.json({ status: 'ok' })
}
