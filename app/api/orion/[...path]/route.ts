import { NextRequest, NextResponse } from 'next/server'

import { WP_API_BASE } from '@/lib/constants'

// Force dynamic rendering to ensure fresh data
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest, props: { params: Promise<{ path: string[] }> }) {
  const params = await props.params;
  try {
    // Reconstruct the path
    const path = params.path.join('/')
    const searchParams = request.nextUrl.searchParams
    const queryString = searchParams.toString()

    // Construct the target URL
    const url = `${WP_API_BASE}/${path}${queryString ? `?${queryString}` : ''}`

    // Output the URL to the console when in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Orion Proxy:', url)
    }

    // Make the request to the WordPress API
    // Next.js automatically caches and deduplicates identical requests
    const response = await fetch(url, {
      headers: {
        // Forward relevant headers
        'User-Agent': request.headers.get('user-agent') || 'HyperAI-Proxy',
        'Accept': request.headers.get('accept') || 'application/json',
        'Accept-Language': request.headers.get('accept-language') || 'en-US,en;q=0.9',
      },
      // Control caching behavior
      next: {
        revalidate: 60, // Cache for 60 seconds
      },
    })

    if (!response.ok) {
      // Forward the error response
      const errorData = await response.text()
      return new NextResponse(errorData, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'Content-Type': response.headers.get('content-type') || 'application/json',
        },
      })
    }

    const data = await response.json()

    // Forward important headers from the WordPress API
    const respHeaders = new Headers({
      // Let CDN and browser cache the response
      'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300',
    })

    // Forward CORS and other important headers
    const headersToForward = [
      'x-wp-total',
      'x-wp-totalpages',
      // 'access-control-expose-headers',
      // 'access-control-allow-origin',
      // 'access-control-allow-credentials',
      // 'access-control-allow-methods',
      // 'access-control-allow-headers',
    ]

    headersToForward.forEach(header => {
      const value = response.headers.get(header)
      if (value) {
        respHeaders.set(header, value)
      }
    })

    return NextResponse.json(data, {
      headers: respHeaders,
    })
  } catch (error) {
    console.error('WordPress API proxy error:', error)
    return NextResponse.json({ error: 'Failed to fetch data from WordPress API' }, { status: 500 })
  }
}
