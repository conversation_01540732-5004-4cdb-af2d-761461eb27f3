import { NextRequest, NextResponse } from 'next/server'
import { tags } from '@/data/tags'

import type { HyperCustomTag } from '@/types'

import { WP_API_BASE } from '@/lib/constants'

// Create a more efficient lookup map from our tags array
const createTagsMap = () => {
  const map = new Map<string, Record<string, string>>()

  tags.forEach(tagObj => {
    const key = Object.keys(tagObj)[0]!
    map.set(key, tagObj[key as keyof typeof tagObj] as Record<string, string>)
  })

  return map
}

const tagsMap = createTagsMap()

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const postType = searchParams.get('post_type') || 'post'
  const perPage = searchParams.get('per_page') || '100'
  const lang = searchParams.get('lang') || 'cn'

  try {
    // Fetch tags from origin server
    const res = await fetch(`${WP_API_BASE}/wp-json/hyperai/v1/tags?post_type=${postType}&per_page=${perPage}`, {
      next: { revalidate: 3600 },
    })

    if (!res.ok) {
      throw new Error('Failed to fetch tags')
    }

    const originalTags: HyperCustomTag[] = await res.json()

    // Translate tag names based on language
    const translatedTags = originalTags.map(tag => {
      // Find the tag translation in our map
      const translations = tagsMap.get(tag.name)

      if (translations && translations[lang]) {
        return {
          ...tag,
          name: translations[lang],
        }
      }

      // Return the original tag if no translation is found
      return tag
    })

    return NextResponse.json(translatedTags)
  } catch (error) {
    console.error('Error fetching tags:', error)
    return NextResponse.json({ error: 'Failed to fetch and translate tags' }, { status: 500 })
  }
}
