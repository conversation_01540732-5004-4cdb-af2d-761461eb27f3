import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import TutorialList from './tutorial-list'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.tutorials.title,
    description: dictionary.nav.tutorials.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/tutorials`),
  }
}

export default function TutorialsPage() {
  return (
    <div>
      <TutorialList />
    </div>
  )
}
