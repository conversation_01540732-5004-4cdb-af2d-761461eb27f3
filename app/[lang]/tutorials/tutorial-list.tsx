'use client'

import { useEffect, useState } from 'react'

import type { HyperApiPost } from '@/types'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'
import useTags from '@/utils/useTags'

import Tag from '@/components/tag'
import TutorialItem from '@/components/tutorial-item'
import { Loading } from '@/components/ui/loading'
import { Pagination } from '@/components/ui/pagination'
import { Skeleton } from '@/components/ui/skeleton'

export default function TutorialList() {
  const [posts, setPosts] = useState<HyperApiPost[]>([])
  const [pageIndex, setPageIndex] = useState(1)
  const [pageTotal, setPageTotal] = useState(0)
  const [currentTag, setCurrentTag] = useState<number | undefined>()

  const { data, isLoading } = useRecentPosts({
    type: 'tutorials',
    page: pageIndex,
    per_page: 30,
    tags: currentTag,
    _embed: true,
    _fields: WP_FIELDS_POST_LIST,
  })
  const { data: dataTags, isLoading: isLoadingTags } = useTags({ type: 'tutorials' })

  const { t } = useTranslation()

  useEffect(() => {
    if (data?.json && data.json.length > 0) {
      setPosts(data.json)
    }

    if (data?.headers && data.headers.get('X-Wp-Totalpages')) {
      setPageTotal(Number(data.headers.get('X-Wp-Totalpages')))
    }
  }, [data])

  function handleTagSelect(tag: number) {
    if (currentTag === tag) {
      setCurrentTag(undefined)
    } else {
      setCurrentTag(tag)
      setPageIndex(1)
    }
  }

  return (
    <>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{t.nav.tutorials.title}</h1>
        <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.tutorials.longDesc}</p>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-24'>
        <div className='lg:col-span-17'>
          <div className='relative'>
            {isLoading && (
              <div className='bg-bg/50 absolute top-0 left-0 z-10 flex h-full w-full items-center justify-center'>
                <Loading />
              </div>
            )}

            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
              {posts && posts.length > 0 && posts.map(post => <TutorialItem data={post} key={post.id} />)}
            </div>

            {isLoading && posts.length === 0 && (
              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                {[...new Array(30)].map((_, idx) => (
                  <Skeleton key={idx} className='h-[92px] rounded-md' />
                ))}
              </div>
            )}
          </div>

          {pageTotal > 0 && (
            <div className='mt-8 flex justify-center'>
              <Pagination value={pageIndex} onPageChange={setPageIndex} total={pageTotal} disabled={isLoading} />
            </div>
          )}
        </div>

        <div className='relative lg:col-span-7'>
          <div className='flex flex-wrap gap-2'>
            {!isLoadingTags &&
              dataTags &&
              dataTags.map((tag, idx) => (
                <div key={idx} onClick={() => handleTagSelect(tag.term_id)} className='tagItem cursor-pointer'>
                  <Tag data={tag} key={idx} active={currentTag === tag.term_id} />
                </div>
              ))}

            {isLoadingTags &&
              [...new Array(100)].map((_, idx) => {
                const length = 60 + (idx % 10) * 10
                return (
                  <div key={idx} className='tagItem'>
                    <Skeleton className='h-7' style={{ width: `${length}px` }} />
                  </div>
                )
              })}
          </div>
        </div>
      </div>
    </>
  )
}
