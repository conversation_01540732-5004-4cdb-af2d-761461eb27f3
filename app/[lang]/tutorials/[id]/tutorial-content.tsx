// app/tutorials/[id]/components/tutorial-content.tsx
'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import {
  JobJupyterQueryDocument,
  JobReadmeQueryDocument,
  type JobJupyterQueryQuery,
  type JobReadmeQueryQuery,
} from '@/generated/graphql'
import { request } from 'graphql-request'
import useSWR from 'swr'
import { IconTerminal2 } from '@tabler/icons-react'

import { useTranslation } from '@/utils/i18n'
import { parseOpenBayesUrl } from '@/utils/parseOpenBayesUrl'

import PageBody from '@/components/page-body'
import RelatedPosts from '@/components/related-posts'
import { Button } from '@/components/ui/button'

import 'react-ipynb-renderer/dist/styles/default.css'

import type { HyperApiPost } from '@/types'

import { OB_API_BASE } from '@/lib/constants'
import { ConsoleOperationNameMap, type ConsoleOperationName } from '@/lib/graphql/graph'

const IpynbRenderer = dynamic(() => import('react-ipynb-renderer').then(mod => mod.IpynbRenderer), {
  ssr: false,
})

interface TutorialContentProps {
  data: HyperApiPost
}

export default function TutorialContent({ data }: TutorialContentProps) {
  const { t } = useTranslation()
  const [tutorialUrl, setTutorialUrl] = useState('')

  useEffect(() => {
    if (data?.acf?.openbayes_url) {
      setTutorialUrl(
        `${data.acf.openbayes_url}?utm_source=hyperai&utm_medium=action-button&utm_campaign=hyperai-tutorials`
      )
    }
  }, [data])

  const parsedUrl = parseOpenBayesUrl(tutorialUrl)
  const { base, username, reqType, job } = parsedUrl
  const validUrl = !!(base && username && reqType && job)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const gqFetcher = (obj: any) => {
    const query = obj[0]
    const variables = obj[1]
    return request(OB_API_BASE, query, variables)
  }

  const { data: openbayesData } = useSWR(
    validUrl
      ? [
          ConsoleOperationNameMap[reqType as ConsoleOperationName],
          {
            username: username,
            id: job,
          },
        ]
      : null,
    gqFetcher
  )

  const { data: openbayesDataReadme } = useSWR<JobReadmeQueryQuery>(
    // @ts-expect-error TODO
    openbayesData?.project?.latestJob?.hasJupyter
      ? [
          JobReadmeQueryDocument,
          {
            username: username,
            // @ts-expect-error TODO
            jobId: openbayesData.project.latestJob.id,
          },
        ]
      : null,
    // @ts-expect-error TODO
    gqFetcher
  )

  const { data: openbayesDataJupyter, isLoading: isLoadingJupyter } = useSWR<JobJupyterQueryQuery>(
    // @ts-expect-error TODO
    openbayesData?.project?.latestJob?.hasJupyter
      ? [
          JobJupyterQueryDocument,
          {
            username: username,
            // @ts-expect-error TODO
            jobId: openbayesData.project.latestJob.id,
          },
        ]
      : null,
    // @ts-expect-error TODO
    gqFetcher
  )

  return (
    <div className='space-y-6'>
      <div className='flex items-center gap-2'>
        <Button size='lg' tint={'accent'} asChild className='gap-2' disabled={!tutorialUrl}>
          <a href={tutorialUrl} target='_blank'>
            <IconTerminal2 className='size-4' />
            {t.tutorials.runOnline}
          </a>
        </Button>
      </div>

      {openbayesDataReadme && openbayesDataReadme.jobReadMe ? (
        <PageBody content={openbayesDataReadme.jobReadMe} className='prose max-w-none' />
      ) : (
        <PageBody content={data.content.rendered} className='prose max-w-none' />
      )}

      {!isLoadingJupyter && openbayesDataJupyter && openbayesDataJupyter.jobJupyter && (
        <>
          <div className='text-fg/60 text-sm'>{t.tutorials.notebookPreview}</div>
          <IpynbRenderer ipynb={JSON.parse(openbayesDataJupyter.jobJupyter)} syntaxTheme='solarizedlight' />
        </>
      )}

      <RelatedPosts postId={Number(data.id)} type='tutorials' name={t.common.contentTypes.tutorials} />
    </div>
  )
}
