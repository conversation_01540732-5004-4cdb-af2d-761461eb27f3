// app/tutorials/[id]/components/page-header.tsx
'use client'

import { useEffect, useState } from 'react'
import type { Dataset, Project } from '@/generated/graphql'
import { request } from 'graphql-request'
import useSWR from 'swr'

import type { HyperApiPost } from '@/types'

import { OB_API_BASE } from '@/lib/constants'
import { ConsoleObjectMap, ConsoleOperationNameMap, type ConsoleOperationName } from '@/lib/graphql/graph'

import { encodeTitle } from '@/utils/encodeTitle'
import { formatBytes } from '@/utils/formatBytes'
import { useTranslation } from '@/utils/i18n'
import { parseOpenBayesUrl } from '@/utils/parseOpenBayesUrl'

import Tag from '@/components/tag'
import Timestamp from '@/components/timestamp'
import { Skeleton } from '@/components/ui/skeleton'

interface PageHeaderProps {
  data: HyperApiPost
}

export default function PageHeader({ data }: PageHeaderProps) {
  const { t } = useTranslation()
  const [tutorialUrl, setTutorialUrl] = useState('')
  const [postData, setPostData] = useState<Project | Dataset | null>(null)

  useEffect(() => {
    if (data?.acf?.openbayes_url) {
      setTutorialUrl(
        `${data.acf.openbayes_url}?utm_source=hyperai&utm_medium=action-button&utm_campaign=hyperai-tutorials`
      )
    }
  }, [data])

  const parsedUrl = parseOpenBayesUrl(tutorialUrl)
  const { base, username, reqType, job } = parsedUrl
  const validUrl = !!(base && username && reqType && job)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const gqFetcher = (obj: any) => {
    const query = obj[0]
    const variables = obj[1]
    return request(OB_API_BASE, query, variables)
  }

  const { data: openbayesData, isLoading } = useSWR(
    validUrl
      ? [
          ConsoleOperationNameMap[reqType as ConsoleOperationName],
          {
            username: username,
            id: job,
          },
        ]
      : null,
    gqFetcher
  )

  useEffect(() => {
    const postType = ConsoleObjectMap[reqType as ConsoleOperationName]
    // @ts-expect-error TODO
    if (openbayesData && openbayesData?.[postType]) {
      console.log(`openbayesData`, openbayesData)

      // @ts-expect-error TODO
      setPostData(openbayesData[postType])
    }
  }, [openbayesData, reqType])

  return (
    <>
      <h1 className='mb-4'>{encodeTitle(data.title.rendered)}</h1>

      {!isLoading && postData && (
        <div className='flex flex-wrap justify-between gap-4'>
          <div className='space-y-1'>
            <p className='text-fg/60 text-sm'>{t.tutorials.metadata.date}</p>
            <div>
              <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />
            </div>
          </div>

          <div className='space-y-1'>
            <p className='text-fg/60 text-sm'>{t.tutorials.metadata.size}</p>
            <p>{formatBytes(postData.size)}</p>
          </div>

          {/* <div className='space-y-1'>
            <p className='text-fg/60 text-sm'>{t.tutorials.metadata.author}</p>
            <div className='flex items-center gap-2'>
              <Avatar className='size-6'>
                <picture>
                  <img src={postData.creator.imageUrl || ''} alt='avatar' />
                </picture>
              </Avatar>
              <span>{postData.creator.id}</span>
            </div>
          </div> */}

          {data.hyperai_tags && data.hyperai_tags.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.tutorials.metadata.tags}</p>
              <div className='flex flex-wrap items-center gap-2'>
                {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                {data.hyperai_tags.map((tag: any, idx: number) => (
                  <Tag key={idx} data={tag} enableLink='tags' />
                ))}
              </div>
            </div>
          )}

          {data?.acf?.license?.value ? (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.datasets.metadata.license}</p>
              <p>{data.acf.license.label}</p>
            </div>
          ) : null}

          {data?.acf?.github_url ? (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>GitHub</p>
              <p>
                <a href={data.acf.github_url} target='_blank'>
                  {new URL(data.acf.github_url).pathname.replace('/', '')}
                </a>
              </p>
            </div>
          ) : null}

          {data?.acf?.paper_url ? (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.common.contentTypes.papers}</p>
              <p>
                <a href={data.acf.paper_url} target='_blank'>
                  {new URL(data.acf.paper_url).pathname.replace('/abs/', '').replace('/pdf/', '')}
                </a>
              </p>
            </div>
          ) : null}
        </div>
      )}

      {(isLoading || postData === null) && (
        <div className='grid grid-cols-5 gap-4'>
          <Skeleton className='h-12' />
          <Skeleton className='h-12' />
          <Skeleton className='h-12' />
          <Skeleton className='h-12' />
          <Skeleton className='h-12' />
        </div>
      )}
    </>
  )
}
