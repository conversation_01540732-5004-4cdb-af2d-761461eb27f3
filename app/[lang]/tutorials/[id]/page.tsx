import { config } from '@/data/config'

import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import Sidebar from '@/components/sidebar'

import TutorialContent from './tutorial-content'
import PageHeader from './tutorial-header'

type Params = Promise<{ lang: Locale; id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'tutorials', locale: params.lang })
  const dictionary = await getDictionary(params.lang)

  return {
    title: `${encodeTitle(data.title.rendered)}${config.siteTitleSplitter}${dictionary.nav.tutorials.title}`,
    description: elementToString(data.excerpt.rendered),
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/tutorials/${params.id}`),
  }
}

export default async function TutorialPage(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'tutorials', locale: params.lang })

  return (
    <div>
      <div className='mb-4'>
        <PageHeader data={data} />
      </div>

      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-2/3'>
          <TutorialContent data={data} />
        </div>

        <div className='md:w-1/3'>
          <Sidebar />
        </div>
      </div>
    </div>
  )
}
