import { config } from '@/data/config'

import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import Author from '@/components/author'
import Category from '@/components/category'
import { FeaturedMedia, getFeaturedMediaUrl } from '@/components/featured-media'
import PageBody from '@/components/page-body'
import RelatedPosts from '@/components/related-posts'
import Tag from '@/components/tag'
import Timestamp from '@/components/timestamp'

type Params = Promise<{ lang: Locale; id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { locale: params.lang })
  const dictionary = await getDictionary(params.lang)

  return {
    title: `${encodeTitle(data.title.rendered)}${config.siteTitleSplitter}${dictionary.nav.news.title}`,
    description: elementToString(data.excerpt.rendered),
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/news/${params.id}`),
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { locale: params.lang })
  const t = await getDictionary(params.lang)

  const featuredImage =
    data._embedded && data._embedded['wp:featuredmedia'] && getFeaturedMediaUrl(data._embedded['wp:featuredmedia'][0])

  return (
    <div>
      <div className='mx-auto max-w-3xl space-y-4'>
        <h1 className='text-center text-3xl md:text-4xl'>{encodeTitle(data.title.rendered)}</h1>

        <div className='text-fg/60 flex flex-wrap items-center justify-center gap-2'>
          {/* `date_gmt` can be empty in some cases, ie. YARPP rest output */}
          <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />

          <div className='flex gap-2'>
            {data.hyperai_categories &&
              data.hyperai_categories.length > 0 &&
              data.hyperai_categories.map((cate, idx) => {
                return <Category data={cate} key={idx} />
              })}
          </div>

          <div className='flex flex-wrap justify-center gap-2'>
            {data.hyperai_tags &&
              data.hyperai_tags.length > 0 &&
              data.hyperai_tags.map((tag, idx) => {
                return <Tag data={tag} key={idx} enableLink={'tags'} />
              })}
          </div>

          <div className='flex gap-2'>
            {data?._embedded?.author &&
              data._embedded.author.length > 0 &&
              data._embedded.author.map((author, idx) => {
                return <Author data={author} key={idx} />
              })}
          </div>
        </div>

        <FeaturedMedia media={featuredImage} className='mx-auto max-w-4xl' autoHeight />

        <PageBody content={data.content.rendered} className='prose prose-lg max-w-none' />

        <RelatedPosts postId={Number(params.id)} type='post' name={t.common.contentTypes.news} />
      </div>
    </div>
  )
}
