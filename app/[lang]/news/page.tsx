import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import Sidebar from '@/components/sidebar'

import NewsList from './news-list'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.news.title,
    description: dictionary.nav.news.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/news`),
  }
}

export default function NewsPage() {
  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='lg:w-2/3'>
          <NewsList />
        </div>
        <div className='lg:w-1/3'>
          <Sidebar />
        </div>
      </div>
    </div>
  )
}
