'use client'

import { useEffect, useState } from 'react'

import type { HyperApiPost } from '@/types'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import useRecentPosts from '@/utils/useRecentPosts'

import { NewsItem } from '@/components/news-item'
import { Loading } from '@/components/ui/loading'
import { Pagination } from '@/components/ui/pagination'
import { Skeleton } from '@/components/ui/skeleton'

export default function NewsList() {
  const [posts, setPosts] = useState<HyperApiPost[]>([])
  const [pageIndex, setPageIndex] = useState(1)
  const [pageTotal, setPageTotal] = useState(0)

  const { data, isLoading } = useRecentPosts({
    type: 'posts',
    page: pageIndex,
    per_page: 30,
    _embed: true,
    _fields: WP_FIELDS_POST_LIST,
  })

  useEffect(() => {
    if (data?.json && data.json.length > 0) {
      setPosts(data.json)
    }

    if (data?.headers && data.headers.get('X-Wp-Totalpages')) {
      setPageTotal(Number(data.headers.get('X-Wp-Totalpages')))
    }
  }, [data])

  return (
    <>
      <div className='relative space-y-6'>
        {isLoading && (
          <div className='bg-bg/50 absolute top-0 left-0 z-10 flex h-full w-full items-center justify-center'>
            <Loading />
          </div>
        )}

        {posts &&
          posts.length > 0 &&
          posts.map(post => {
            return <NewsItem data={post} key={post.id} />
          })}

        {isLoading &&
          posts.length === 0 &&
          [...new Array(10)].map((post, idx) => {
            return (
              <div key={idx}>
                <div className='flex gap-4'>
                  <div className={'flex-1'}>
                    <div className='space-y-3 text-2xl'>
                      <Skeleton className='h-[32px] w-[50%]' />
                      <Skeleton className='h-[90px]' />
                    </div>
                  </div>
                  <Skeleton className='h-[160px] w-1/4' />
                </div>
              </div>
            )
          })}
      </div>

      {pageTotal > 0 && (
        <div className='mt-8 flex justify-center gap-x-1'>
          <Pagination value={pageIndex} onPageChange={setPageIndex} total={pageTotal} disabled={isLoading} />
        </div>
      )}
    </>
  )
}
