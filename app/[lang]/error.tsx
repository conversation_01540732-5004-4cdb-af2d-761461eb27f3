'use client'

import { usePathname } from 'next/navigation'
import { LogLevel } from '@axiomhq/logging'

import { useLogger } from '@/lib/axiom/client'

export default function ErrorPage({ error }: { error: Error & { digest?: string } }) {
  const pathname = usePathname()
  const log = useLogger()
  const status = error.message === 'Invalid URL' ? 404 : 500

  log.log(LogLevel.error, error.message, {
    error: error.name,
    cause: error.cause,
    stack: error.stack,
    digest: error.digest,
    request: {
      host: window.location.href,
      path: pathname,
      statusCode: status,
    },
  })

  return (
    <div className='p-8'>
      Ops! An Error has occurred: <p className='px-8 py-2 text-lg text-red-400'>`{error.message}`</p>
      <div className='mt-8 w-1/3'></div>
    </div>
  )
}
