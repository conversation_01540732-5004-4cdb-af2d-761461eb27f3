import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import PageBody from '@/components/page-body'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.about.title,
    description: dictionary.nav.about.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/about`),
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(237, { type: `pages`, locale: params.lang })

  return (
    <div>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{encodeTitle(data.title.rendered)}</h1>
      </div>

      <PageBody content={data.content.rendered} className='prose' />
    </div>
  )
}
