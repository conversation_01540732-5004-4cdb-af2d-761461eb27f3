'use client'

import Link from 'next/link'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import WikiItem from '@/components/wiki-item'

export default function HomeWiki() {
  const { t } = useTranslation()

  const { data: dataWiki, isLoading } = useRecentPosts({
    type: 'wiki',
    page: 1,
    per_page: 9,
    _embed: true,
    _fields: WP_FIELDS_POST_LIST,
  })

  return (
    <div className='space-y-3'>
      <div className='grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3'>
        {dataWiki?.json?.map((post, idx) => <WikiItem data={post} key={idx} />)}

        {isLoading && [...new Array(9)].map((_, idx) => <Skeleton key={idx} className='h-[140px] rounded-lg' />)}
      </div>

      <Button size='lg' asChild>
        <Link href='/wiki'>{t.common.moreLinks.wiki}</Link>
      </Button>
    </div>
  )
}
