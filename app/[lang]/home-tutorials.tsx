'use client'

import Link from 'next/link'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import TutorialItem from '@/components/tutorial-item'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

export default function HomeTutorials() {
  const { t } = useTranslation()

  const { data: dataTutorials, isLoading } = useRecentPosts({
    type: 'tutorials',
    page: 1,
    per_page: 6,
    _embed: true,
    _fields: WP_FIELDS_POST_LIST,
  })

  return (
    <div className='space-y-3'>
      <div className='relative grid grid-cols-1 gap-3 sm:grid-cols-2'>
        {dataTutorials?.json?.map((post, idx) => <TutorialItem data={post} key={idx} />)}

        {isLoading && [...new Array(6)].map((_, idx) => <Skeleton key={idx} className='h-[150px] rounded-lg' />)}
      </div>

      <Button size='lg' asChild>
        <Link href='/tutorials'>{t.common.moreLinks.tutorials}</Link>
      </Button>
    </div>
  )
}
