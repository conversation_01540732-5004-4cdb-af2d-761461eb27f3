'use client'

import { ThemeProvider } from 'next-themes'
import { ProgressProvider } from '@bprogress/next/app'

import type { Locale } from '@/types'

import { I18nProvider, type Dictionary } from '@/utils/i18n'

import { TooltipProvider } from '@/components/ui/tooltip'

const Providers = ({
  children,
  lang,
  dictionary,
}: {
  children: React.ReactNode
  lang: Locale
  dictionary: Dictionary
}) => {
  return (
    <ThemeProvider attribute='data-theme'>
      <ProgressProvider color='var(--color-ac)' options={{ showSpinner: false }} shallowRouting>
        <TooltipProvider delayDuration={300}>
          <I18nProvider dictionary={dictionary} locale={lang}>
            {children}
          </I18nProvider>
        </TooltipProvider>
      </ProgressProvider>
    </ThemeProvider>
  )
}

export default Providers
