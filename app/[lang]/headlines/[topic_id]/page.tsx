import Link from 'next/link'
import { notFound } from 'next/navigation'
import { IconArrowLeft, IconLink } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchNewsTopic } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import Sidebar from '@/components/sidebar'
import Timestamp from '@/components/timestamp'
import { Button } from '@/components/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface HeadlineDetailPageProps {
  params: Promise<{
    lang: Locale
    topic_id: string
  }>
}

type Params = Promise<{ lang: Locale; topic_id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)
  const response = await fetchNewsTopic(params.topic_id, { locale: params.lang })
  const headline = response.data

  // If the headline is marked as hidden, return minimal metadata
  if (headline.is_hidden) {
    return {
      title: dictionary.nav.headlines.title,
      metadataBase: new URL(BASE_URL),
    }
  }

  return {
    title: `${headline.title} | ${dictionary.nav.headlines.title}`,
    description: headline.abstract,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/headlines/${params.topic_id}`),
  }
}

export default async function HeadlineDetailPage(props: HeadlineDetailPageProps) {
  const params = await props.params;
  const { lang, topic_id } = params

  // Fetch headline detail
  try {
    const response = await fetchNewsTopic(topic_id, { locale: lang })
    const headline = response.data

    // If the headline is marked as hidden, return notFound
    if (headline.is_hidden) {
      return notFound()
    }

    return (
      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-2/3'>
          <Button variant='outline' asChild className='mb-6'>
            <Link href={`/${lang}/headlines`}>
              <IconArrowLeft className='mr-2 h-4 w-4' />
              Back to Headlines
            </Link>
          </Button>

          <article>
            <div className='mb-6'>
              <h1 className='mb-4 text-3xl font-bold'>{headline.title}</h1>
              <div className='mb-6 flex items-center'>
                <Timestamp date={headline.created_timestamp || headline.create_at} />
              </div>

              <div className='prose dark:prose-invert max-w-none'>
                <p className='text-lg leading-relaxed whitespace-pre-line'>{headline.abstract}</p>
              </div>
            </div>

            <h2 className='mb-4 flex items-center gap-2 text-xl font-semibold'>
              <IconLink className='h-5 w-5' />
              Related Links
            </h2>

            <div className='mb-8 grid grid-cols-1 gap-4 md:grid-cols-2'>
              {headline.links.map((link, index) => (
                <Card key={index} className='transition-shadow hover:shadow-xl'>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-base'>
                      <a href={link.link} target='_blank' rel='noopener noreferrer'>
                        {link.title}
                      </a>
                    </CardTitle>
                    <CardDescription>{link.site_name}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </article>
        </div>

        <div className='md:w-1/3'>
          <Sidebar />
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching headline:', error)
    return notFound()
  }
}
