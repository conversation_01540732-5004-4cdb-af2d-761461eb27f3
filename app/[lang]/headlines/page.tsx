import Link from 'next/link'
import { IconNews } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchNews } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import HeadlineItem from '@/components/headline-item'
import { Button } from '@/components/ui/button'

interface HeadlinesPageProps {
  params: Promise<{
    lang: Locale
  }>
  searchParams: Promise<{
    page?: string
  }>
}

// Generate metadata
export async function generateMetadata(props: { params: Promise<{ lang: Locale }> }) {
  const params = await props.params;
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.headlines.title,
    description: dictionary.nav.headlines.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl('/headlines'),
  }
}

export default async function HeadlinesPage(props: HeadlinesPageProps) {
  const searchParams = await props.searchParams;
  const params = await props.params;
  const { lang } = params
  const page = parseInt(searchParams.page || '1', 10)
  const pageSize = 10
  const dictionary = await getDictionary(lang)

  // Fetch headlines data
  const response = await fetchNews({
    locale: lang,
    page_num: page,
    page_size: pageSize,
  })

  const headlines = response.data
  const visibleHeadlines = headlines.filter(headline => !headline.is_hidden)

  return (
    <div className='container'>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{dictionary.nav.headlines.title}</h1>
        <div>{/* You can add filters or other controls here */}</div>
      </div>

      {visibleHeadlines.length > 0 ? (
        <div className='space-y-4'>
          {visibleHeadlines.map(headline => (
            <HeadlineItem key={headline.topic_id} headline={headline} locale={lang} variant='default' />
          ))}
        </div>
      ) : (
        <div className='flex flex-col items-center justify-center py-16 text-center'>
          <IconNews className='text-muted-foreground mb-4 h-16 w-16' />
          <h2 className='text-xl font-semibold'>No headlines available</h2>
          <p className='text-muted-foreground mt-2'>Check back later for updates.</p>
        </div>
      )}

      {/* Pagination */}
      <div className='mt-8 flex justify-center'>
        <div className='flex gap-2'>
          {page > 1 && (
            <Link href={`/${lang}/headlines${page > 2 ? `?page=${page - 1}` : ''}`}>
              <Button variant='outline'>Previous</Button>
            </Link>
          )}
          <Link href={`/${lang}/headlines?page=${page + 1}`}>
            <Button variant='outline'>Next</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
