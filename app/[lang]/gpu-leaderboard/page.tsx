import { gpuLeaderboard } from '@/data/gpu-leaderboard'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchGpuSoftwareBenchmark, type GPUSoftwareBenchmarkResponse } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { GPUCompare } from '@/app/[lang]/gpu-leaderboard/components/gpu-compare'
import { GPULeaderboardCharts } from '@/app/[lang]/gpu-leaderboard/components/gpu-leaderboard-charts'
import { GPULeaderboardTable } from '@/app/[lang]/gpu-leaderboard/components/gpu-leaderboard-table'

import GpuBenchmarks from '../sota/gpu-benchmarks'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.gpuLeaderboard.title,
    description: dictionary.nav.gpuLeaderboard.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/gpu-leaderboard`),
  }
}

export default async function Page({ params }: { params: Promise<{ lang: Locale }> }) {
  const { lang } = await params
  const t = await getDictionary(lang)

  // Fetch data in parallel
  const [gpuSoftwareData] = await Promise.all([
    fetchGpuSoftwareBenchmark({ locale: lang }).catch(
      () => ({ r: 200, lang, data: [] }) satisfies GPUSoftwareBenchmarkResponse
    ),
  ])

  return (
    <div className='flex flex-col gap-8 lg:flex-row'>
      <div>
        <div className='my-20 space-y-4'>
          <h1 className='font-logo leading-none'>{t.nav.gpuLeaderboard.title}</h1>
          <div className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.gpuLeaderboard.desc}</div>
        </div>

        <div className='space-y-6'>
          <GPUCompare data={gpuLeaderboard} dictionary={t} />

          <GPULeaderboardCharts data={gpuLeaderboard} dictionary={t} />

          <GPULeaderboardTable data={gpuLeaderboard} dictionary={t} />

          {/* GPU Benchmarks Section */}
          <div className='mt-6 space-y-2'>
            <h2 className='text-3xl'>{t.sota.gpuBenchmark.title}</h2>
            <p className='text-fg/60 text-lg'>{t.sota.gpuBenchmark.description}</p>
          </div>
          <GpuBenchmarks softwareData={gpuSoftwareData.data || []} />
        </div>
      </div>
    </div>
  )
}
