import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function Loading() {
  return (
    <div className='container mx-auto space-y-8 py-8'>
      <div className='space-y-2'>
        <Skeleton className='h-8 w-64' />
        <Skeleton className='h-4 w-96' />
      </div>

      {/* Chart skeleton */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className='h-6 w-48' />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className='h-[400px] w-full' />
        </CardContent>
      </Card>

      {/* Compare skeleton */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className='h-6 w-32' />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <Skeleton className='h-10 w-full' />
              <Skeleton className='h-10 w-full' />
            </div>
            <Skeleton className='h-[300px] w-full' />
          </div>
        </CardContent>
      </Card>

      {/* Table skeleton */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className='h-6 w-64' />
          </CardTitle>
          <div className='flex items-center py-4'>
            <Skeleton className='h-10 w-64' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-2'>
            <Skeleton className='h-10 w-full' />
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className='h-12 w-full' />
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
