'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import type { GPULeaderboard } from '@/data/gpu-leaderboard'
import type { Dictionary } from '@/lib/dictionaries-server'

import { generateGPUSlug } from '@/utils/gpu-utils'

import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'

// Helper to extract and parse numeric values from benchmark strings
const extractNumericValue = (value: string | null): number => {
  if (!value || value === 'Not supported') return 0
  const match = value.match(/(\d+(\.\d+)?)/)
  return match && match[1] ? parseFloat(match[1]) : 0
}

// Calculate the percentage difference between two values
const calculateDifference = (value1: number, value2: number): number => {
  if (value2 === 0) return 0
  return ((value1 - value2) / value2) * 100
}

export function GPUCompare({ data, dictionary }: { data: GPULeaderboard[]; dictionary: Dictionary }) {
  const [gpu1, setGpu1] = useState<string>(data[0]?.GPU || '')
  const [gpu2, setGpu2] = useState<string>(data[1]?.GPU || '')

  const selectedGpu1 = data.find(gpu => gpu.GPU === gpu1)
  const selectedGpu2 = data.find(gpu => gpu.GPU === gpu2)

  // Metrics to compare
  const metrics = [
    { key: 'FP8', label: dictionary.nav.gpuLeaderboard.fp8Performance },
    { key: 'FP16', label: dictionary.nav.gpuLeaderboard.fp16Performance },
    { key: 'FP32', label: dictionary.nav.gpuLeaderboard.fp32Performance },
    { key: 'FP64', label: dictionary.nav.gpuLeaderboard.fp64Performance },
    { key: 'CUDA Cores', label: dictionary.nav.gpuLeaderboard.cudaCores },
    { key: 'Total Graphics Power', label: dictionary.nav.gpuLeaderboard.powerConsumption },
  ]

  return (
    <div>
      <div className='grid grid-cols-1 gap-3 md:grid-cols-2'>
        <div className='space-y-2'>
          {/* <Label htmlFor='gpu1'>Select First GPU</Label> */}
          <Select value={gpu1} onValueChange={setGpu1}>
            <SelectTrigger id='gpu1'>
              <SelectValue placeholder={dictionary.nav.gpuLeaderboard.selectGpu}>
                {gpu1 && (
                  <div className='flex items-center gap-2'>
                    {selectedGpu1?.thumbnail && (
                      <Image
                        src={`/static/gpu/${selectedGpu1.thumbnail}.png`}
                        alt={selectedGpu1.GPU || ''}
                        width={32}
                        height={32}
                        className='rounded object-contain'
                      />
                    )}
                    <span>{gpu1}</span>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {data.map(gpu => (
                <SelectItem key={gpu.GPU} value={gpu.GPU || ''}>
                  <div className='flex items-center gap-2'>
                    {gpu.thumbnail && (
                      <Image
                        src={`/static/gpu/${gpu.thumbnail}.png`}
                        alt={gpu.GPU || ''}
                        width={32}
                        height={32}
                        className='rounded object-contain'
                      />
                    )}
                    <span>{gpu.GPU}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className='space-y-2'>
          {/* <Label htmlFor='gpu2'>Select Second GPU</Label> */}
          <Select value={gpu2} onValueChange={setGpu2}>
            <SelectTrigger id='gpu2'>
              <SelectValue placeholder={dictionary.nav.gpuLeaderboard.selectGpu}>
                {gpu2 && (
                  <div className='flex items-center gap-2'>
                    {selectedGpu2?.thumbnail && (
                      <Image
                        src={`/static/gpu/${selectedGpu2.thumbnail}.png`}
                        alt={selectedGpu2.GPU || ''}
                        width={32}
                        height={32}
                        className='rounded object-contain'
                      />
                    )}
                    <span>{gpu2}</span>
                  </div>
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {data.map(gpu => (
                <SelectItem key={gpu.GPU} value={gpu.GPU || ''}>
                  <div className='flex items-center gap-2'>
                    {gpu.thumbnail && (
                      <Image
                        src={`/static/gpu/${gpu.thumbnail}.png`}
                        alt={gpu.GPU || ''}
                        width={32}
                        height={32}
                        className='rounded object-contain'
                      />
                    )}
                    <span>{gpu.GPU}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedGpu1 && selectedGpu2 && (
        <div className='mt-6'>
          <Separator className='my-4' />

          <div className='font-logo mb-4 grid grid-cols-2 gap-3 font-medium'>
            <div className='flex flex-col items-center gap-3'>
              <Link
                href={`/gpu-leaderboard/${generateGPUSlug(selectedGpu1.GPU || '')}`}
                className='flex flex-col gap-3 text-center'
              >
                {selectedGpu1.thumbnail && (
                  <Image
                    src={`/static/gpu/${selectedGpu1.thumbnail}.png`}
                    alt={selectedGpu1.GPU || ''}
                    width={256}
                    height={256}
                    className='rounded object-contain'
                  />
                )}
                {selectedGpu1.GPU}
              </Link>
            </div>
            <div className='flex flex-col items-center gap-3'>
              <Link
                href={`/gpu-leaderboard/${generateGPUSlug(selectedGpu2.GPU || '')}`}
                className='flex flex-col gap-3 text-center'
              >
                {selectedGpu2.thumbnail && (
                  <Image
                    src={`/static/gpu/${selectedGpu2.thumbnail}.png`}
                    alt={selectedGpu2.GPU || ''}
                    width={256}
                    height={256}
                    className='rounded object-contain'
                  />
                )}
                {selectedGpu2.GPU}
              </Link>
            </div>
          </div>

          <div className='space-y-4'>
            {/* Basic Information */}
            <div className='font-logo grid grid-cols-2 gap-3 text-2xl font-semibold'>
              <div>
                <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.architecture}</div>
                {selectedGpu1.Architecture}
              </div>
              <div>
                <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.architecture}</div>
                {selectedGpu2.Architecture}
              </div>
            </div>

            <div className='font-logo grid grid-cols-2 gap-3 text-2xl font-semibold'>
              <div>
                <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.releaseDate}</div>
                <div>{selectedGpu1['Release Date']}</div>
              </div>

              <div>
                <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.releaseDate}</div>
                <div>{selectedGpu2['Release Date']}</div>
              </div>
            </div>

            <div>
              <div className='font-logo grid grid-cols-2 gap-3 text-2xl font-semibold'>
                <div>
                  <div>
                    <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.memory}</div>
                    <div
                      className={(() => {
                        const mem1 = extractNumericValue(selectedGpu1['GPU memory'] as string)
                        const mem2 = extractNumericValue(selectedGpu2['GPU memory'] as string)
                        return mem1 > mem2 ? 'font-medium text-emerald-500' : ''
                      })()}
                    >
                      {selectedGpu1['GPU memory']}
                    </div>
                  </div>
                </div>

                <div>
                  <div>
                    <div className='text-sm font-normal'>{dictionary.nav.gpuLeaderboard.memory}</div>
                    <div
                      className={(() => {
                        const mem1 = extractNumericValue(selectedGpu1['GPU memory'] as string)
                        const mem2 = extractNumericValue(selectedGpu2['GPU memory'] as string)
                        return mem2 > mem1 ? 'font-medium text-emerald-500' : ''
                      })()}
                    >
                      {selectedGpu2['GPU memory']}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Separator className='my-2' />

            {/* Performance Metrics */}
            {metrics.map(metric => {
              const valueA =
                metric.key === 'CUDA Cores' || metric.key === 'Total Graphics Power'
                  ? selectedGpu1[metric.key]
                  : selectedGpu1[metric.key as keyof GPULeaderboard]

              const valueB =
                metric.key === 'CUDA Cores' || metric.key === 'Total Graphics Power'
                  ? selectedGpu2[metric.key]
                  : selectedGpu2[metric.key as keyof GPULeaderboard]

              const numA = extractNumericValue(valueA as string)
              const numB = extractNumericValue(valueB as string)
              const diff = calculateDifference(numA, numB)
              const isPowerMetric = metric.key === 'Total Graphics Power'
              const isCudaCores = metric.key === 'CUDA Cores'

              return (
                <div key={metric.key} className='font-logo grid grid-cols-2 gap-3 text-2xl font-semibold'>
                  <div>
                    <div>
                      <div className='flex flex-wrap items-center gap-x-1 text-sm font-normal'>
                        {metric.label}
                        {(() => {
                          if (isPowerMetric && diff < 0 && numA > 0) {
                            return (
                              <Badge size={'sm'} variant={'solid'} tint={'emerald'}>
                                {dictionary.nav.gpuLeaderboard.percentLess.replace(
                                  '{{percent}}',
                                  Math.abs(diff).toFixed(1)
                                )}
                              </Badge>
                            )
                          } else if (!isPowerMetric && diff > 0 && numA > 0) {
                            const label = isCudaCores
                              ? dictionary.nav.gpuLeaderboard.percentMore
                              : dictionary.nav.gpuLeaderboard.percentFaster
                            return (
                              <Badge size={'sm'} variant={'solid'} tint={'emerald'}>
                                {label.replace('{{percent}}', Math.abs(diff).toFixed(1))}
                              </Badge>
                            )
                          }
                          return null
                        })()}
                      </div>
                      <div
                        className={
                          diff > 0 && !isPowerMetric
                            ? 'font-medium text-emerald-500'
                            : diff < 0 && isPowerMetric
                              ? 'font-medium text-emerald-500'
                              : ''
                        }
                      >
                        {valueA}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div>
                      <div className='flex flex-wrap items-center gap-x-1 text-sm font-normal'>
                        {metric.label}
                        {(() => {
                          if (isPowerMetric && diff > 0 && numB > 0) {
                            return (
                              <Badge size={'sm'} variant={'solid'} tint={'emerald'}>
                                {dictionary.nav.gpuLeaderboard.percentLess.replace(
                                  '{{percent}}',
                                  Math.abs(diff).toFixed(1)
                                )}
                              </Badge>
                            )
                          } else if (!isPowerMetric && diff < 0 && numB > 0) {
                            const label = isCudaCores
                              ? dictionary.nav.gpuLeaderboard.percentMore
                              : dictionary.nav.gpuLeaderboard.percentFaster
                            return (
                              <Badge size={'sm'} variant={'solid'} tint={'emerald'}>
                                {label.replace('{{percent}}', Math.abs(diff).toFixed(1))}
                              </Badge>
                            )
                          }
                          return null
                        })()}
                      </div>
                      <div
                        className={
                          diff < 0 && !isPowerMetric
                            ? 'font-medium text-emerald-500'
                            : diff > 0 && isPowerMetric
                              ? 'font-medium text-emerald-500'
                              : ''
                        }
                      >
                        {valueB}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
