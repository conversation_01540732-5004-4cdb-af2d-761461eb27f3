'use client'

import { useState } from 'react'
import type { GPULeaderboard } from '@/data/gpu-leaderboard'
import { IconArrowDown, IconArrowsSort, IconArrowUp, IconSearch } from '@tabler/icons-react'
import type { Dictionary } from '@/lib/dictionaries-server'
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type ColumnFiltersState,
  type HeaderContext,
  type SortingState,
} from '@tanstack/react-table'

import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

export function GPULeaderboardTable({ data, dictionary }: { data: GPULeaderboard[]; dictionary: Dictionary }) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])

  // Helper function for creating sortable headers
  const createSortableHeader = (title: string) => {
    function SortableHeader(props: HeaderContext<GPULeaderboard, unknown>) {
      const { column } = props
      return (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {title}
          {column.getIsSorted() === 'asc' ? (
            <IconArrowUp className='ml-2 size-4' />
          ) : column.getIsSorted() === 'desc' ? (
            <IconArrowDown className='ml-2 size-4' />
          ) : (
            <IconArrowsSort className='ml-2 size-4 opacity-50' />
          )}
        </div>
      )
    }

    SortableHeader.displayName = `SortableHeader(${title})`
    return SortableHeader
  }

  const columns: ColumnDef<GPULeaderboard>[] = [
    {
      accessorKey: 'GPU',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.gpuModel),
    },
    {
      accessorKey: 'Release Date',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.releaseDate),
    },
    {
      accessorKey: 'Architecture',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.architecture),
    },
    {
      accessorKey: 'CUDA Cores',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.cudaCores),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('CUDA Cores')) || 0
        const valueB = Number(rowB.getValue('CUDA Cores')) || 0
        return valueA - valueB
      },
    },
    {
      accessorKey: 'GPU memory',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.memory),
    },
    {
      accessorKey: 'Total Graphics Power',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.tgp),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('Total Graphics Power')?.toString().replace('W', '')) || 0
        const valueB = Number(rowB.getValue('Total Graphics Power')?.toString().replace('W', '')) || 0
        return valueA - valueB
      },
    },
    {
      accessorKey: 'FP8',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.fp8),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('FP8')) || 0
        const valueB = Number(rowB.getValue('FP8')) || 0
        return valueA - valueB
      },
    },
    {
      accessorKey: 'FP16',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.fp16),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('FP16')) || 0
        const valueB = Number(rowB.getValue('FP16')) || 0
        return valueA - valueB
      },
    },
    {
      accessorKey: 'FP32',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.fp32),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('FP32')) || 0
        const valueB = Number(rowB.getValue('FP32')) || 0
        return valueA - valueB
      },
    },
    {
      accessorKey: 'FP64',
      header: createSortableHeader(dictionary.nav.gpuLeaderboard.fp64),
      sortingFn: (rowA, rowB) => {
        const valueA = Number(rowA.getValue('FP64')) || 0
        const valueB = Number(rowB.getValue('FP64')) || 0
        return valueA - valueB
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <div className='space-y-3'>
      <h3>{dictionary.nav.gpuLeaderboard.benchmarkComparison}</h3>
      <div className='flex items-center'>
        <Input
          placeholder={dictionary.nav.gpuLeaderboard.filterGpus}
          value={(table.getColumn('GPU')?.getFilterValue() as string) ?? ''}
          onChange={e => table.getColumn('GPU')?.setFilterValue(e.target.value)}
          className='max-w-sm'
          leftSection={<IconSearch className='ml-2.5 size-4' />}
          leftSectionClassName='pointer-events-none'
        />
      </div>

      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className='h-24 text-center'>
                  {dictionary.nav.gpuLeaderboard.noResults}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
