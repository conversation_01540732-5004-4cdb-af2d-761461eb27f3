'use client'

import { useState } from 'react'
import type { GPULeaderboard } from '@/data/gpu-leaderboard'
import { Bar, BarChart, CartesianGrid, LabelList, Legend, XAxis, YAxis } from 'recharts'
import type { Props as LabelProps } from 'recharts/types/component/Label'
import type { Dictionary } from '@/lib/dictionaries-server'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

type MetricKey = 'FP8' | 'FP16' | 'FP32' | 'FP64'

// Function to convert performance strings to numbers
const extractValue = (value: string | null): number => {
  if (value === null || value === 'Not supported') return 0
  const match = value.match(/^(\d+(\.\d+)?)/)
  return match && match[1] ? parseFloat(match[1]) : 0
}

// Format data for Recharts
const formatDataForChart = (gpus: GPULeaderboard[], metric: MetricKey) => {
  return gpus.map(gpu => ({
    name: gpu.GPU || 'Unknown',
    [metric]: extractValue(gpu[metric]),
    fullName: gpu.GPU,
    architecture: gpu.Architecture,
    releaseDate: gpu['Release Date'],
    metricValue: gpu[metric],
  }))
}

// Chart config with colors - must be inside the component to access dictionary
const getChartConfig = (dictionary: Dictionary) => ({
  FP8: {
    label: dictionary.nav.gpuLeaderboard.fp8Performance,
    color: 'color-mix(in oklch, var(--color-purple-500) 30%, transparent)',
  },
  FP16: {
    label: dictionary.nav.gpuLeaderboard.fp16Performance,
    color: 'color-mix(in oklch, var(--color-blue-500) 30%, transparent)',
  },
  FP32: {
    label: dictionary.nav.gpuLeaderboard.fp32Performance,
    color: 'color-mix(in oklch, var(--color-teal-500) 30%, transparent)',
  },
  FP64: {
    label: dictionary.nav.gpuLeaderboard.fp64Performance,
    color: 'color-mix(in oklch, var(--color-orange-500) 30%, transparent)',
  },
})

export function GPULeaderboardCharts({ data, dictionary }: { data: GPULeaderboard[]; dictionary: Dictionary }) {
  const [metric, setMetric] = useState<MetricKey>('FP16')
  const chartConfig = getChartConfig(dictionary)

  // Sort GPUs by selected metric performance (high to low)
  const sortedData = [...data].sort((a, b) => {
    const valueA = extractValue(a[metric])
    const valueB = extractValue(b[metric])
    return valueB - valueA
  })

  // Prepare data for recharts
  const chartData = formatDataForChart(sortedData, metric)

  // Custom label component for displaying GPU names on bars
  const renderCustomLabel = (props: LabelProps) => {
    const { x, y, height, index } = props
    const gpuName = index !== undefined ? chartData[index]?.name || '' : ''

    return (
      <text
        x={Number(x) + 5}
        y={Number(y) + Number(height) / 2}
        fill='var(--color-fg)'
        textAnchor='start'
        dominantBaseline='middle'
        className='text-sm font-medium'
      >
        {gpuName}
      </text>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.nav.gpuLeaderboard.performanceVisualization}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue='FP16' onValueChange={value => setMetric(value as MetricKey)}>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='FP8'>{dictionary.nav.gpuLeaderboard.fp8}</TabsTrigger>
            <TabsTrigger value='FP16'>{dictionary.nav.gpuLeaderboard.fp16}</TabsTrigger>
            <TabsTrigger value='FP32'>{dictionary.nav.gpuLeaderboard.fp32}</TabsTrigger>
            <TabsTrigger value='FP64'>{dictionary.nav.gpuLeaderboard.fp64}</TabsTrigger>
          </TabsList>
          <TabsContent value={metric} className='pt-4'>
            <div className='w-full'>
              <ChartContainer config={chartConfig}>
                <BarChart
                  data={chartData}
                  accessibilityLayer
                  layout='vertical'
                  margin={{ top: 0, right: 0, left: 0, bottom: 20 }}
                  barCategoryGap='5%'
                  barGap={4}
                >
                  <CartesianGrid strokeDasharray='3 3' />
                  <XAxis
                    type='number'
                    domain={[0, 'auto']}
                    label={{ value: dictionary.nav.gpuLeaderboard.tflops, position: 'insideBottom', offset: -20 }}
                  />
                  <YAxis type='category' dataKey='name' tick={false} width={0} />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Legend verticalAlign='top' />
                  <Bar
                    dataKey={metric}
                    name={chartConfig[metric].label}
                    fill={chartConfig[metric].color}
                    radius={[0, 4, 4, 0]}
                    maxBarSize={50}
                  >
                    <LabelList content={renderCustomLabel} position='inside' />
                  </Bar>
                </BarChart>
              </ChartContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
