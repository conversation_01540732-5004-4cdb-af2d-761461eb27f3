import type { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { config } from '@/data/config'
import { gpuLeaderboard } from '@/data/gpu-leaderboard'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'
import { findGPUBySlug } from '@/utils/gpu-utils'

import { Badge } from '@/components/ui/badge'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'

interface PageProps {
  params: Promise<{
    gpu: string
    lang: Locale
  }>
}

export async function generateMetadata(props: PageProps): Promise<Metadata> {
  const params = await props.params
  const gpu = findGPUBySlug(params.gpu, gpuLeaderboard)
  const dictionary = await getDictionary(params.lang)

  if (!gpu) {
    return {
      title: 'GPU Not Found',
    }
  }

  return {
    title: `${gpu.GPU}${config.siteTitleSplitter}${dictionary.nav.gpuLeaderboard.title}`,
    description: `Detailed specifications and performance metrics for ${gpu.GPU}`,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/gpu-leaderboard/${params.gpu}`),
  }
}

export async function generateStaticParams() {
  return gpuLeaderboard
    .filter(gpu => gpu.GPU)
    .map(gpu => ({
      gpu: gpu
        .GPU!.toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '')
        .replace(/-+/g, '-')
        .trim(),
    }))
}

export default async function GPUDetailsPage(props: PageProps) {
  const params = await props.params
  const gpu = findGPUBySlug(params.gpu, gpuLeaderboard)
  const t = await getDictionary(params.lang)

  if (!gpu) {
    notFound()
  }

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb className='mb-6'>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/${params.lang}`} className='flex items-center gap-1'>
                <IconHome className='h-4 w-4' />
                {t.nav.home.title}
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/${params.lang}/gpu-leaderboard`}>{t.nav.gpuLeaderboard.title}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{gpu.GPU}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* GPU Image and Title */}
      <div className='mb-8 text-center'>
        {gpu.thumbnail && (
          <div className='mx-auto mb-6 max-w-md'>
            <Image
              src={`/static/gpu/${gpu.thumbnail}.png`}
              alt={gpu.GPU || ''}
              width={512}
              height={512}
              className='h-auto w-full object-contain'
            />
          </div>
        )}
        <h1 className='font-logo text-4xl font-bold'>{gpu.GPU}</h1>
      </div>

      <Separator className='my-8' />

      {/* Specifications */}
      <div className='space-y-6'>
        <div>
          <h2 className='mb-4 text-2xl font-semibold'>Specifications</h2>
          <div className='space-y-4'>
            <div className='font-logo grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
              <div>
                <div className='text-sm font-normal'>Architecture</div>
                <div>{gpu.Architecture || 'N/A'}</div>
              </div>
              <div>
                <div className='text-sm font-normal'>Release Date</div>
                <div>{gpu['Release Date'] || 'N/A'}</div>
              </div>
            </div>

            <div className='font-logo grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
              <div>
                <div className='text-sm font-normal'>CUDA Cores</div>
                <div>{gpu['CUDA Cores'] || 'N/A'}</div>
              </div>
              <div>
                <div className='text-sm font-normal'>Total Graphics Power</div>
                <div>{gpu['Total Graphics Power'] || 'N/A'}</div>
              </div>
            </div>

            <div className='font-logo text-2xl font-semibold'>
              <div className='text-sm font-normal'>GPU Memory</div>
              <div>{gpu['GPU memory'] || 'N/A'}</div>
            </div>
          </div>
        </div>

        <Separator className='my-6' />

        {/* Performance Metrics */}
        <div>
          <h2 className='mb-4 text-2xl font-semibold'>Performance Metrics</h2>
          <div className='font-logo grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
            <div>
              <div className='text-sm font-normal'>FP8 Performance</div>
              <div>
                {gpu.FP8 === 'Not supported' ? (
                  <Badge variant='dot' tint='default' size='sm'>
                    Not supported
                  </Badge>
                ) : (
                  gpu.FP8 || 'N/A'
                )}
              </div>
            </div>
            <div>
              <div className='text-sm font-normal'>FP16 Performance</div>
              <div>
                {gpu.FP16 === 'Not supported' ? (
                  <Badge variant='dot' tint='default' size='sm'>
                    Not supported
                  </Badge>
                ) : (
                  gpu.FP16 || 'N/A'
                )}
              </div>
            </div>
          </div>

          <div className='font-logo mt-4 grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
            <div>
              <div className='text-sm font-normal'>FP32 Performance</div>
              <div>
                {gpu.FP32 === 'Not supported' ? (
                  <Badge variant='dot' tint='default' size='sm'>
                    Not supported
                  </Badge>
                ) : (
                  gpu.FP32 || 'N/A'
                )}
              </div>
            </div>
            <div>
              <div className='text-sm font-normal'>FP64 Performance</div>
              <div>
                {gpu.FP64 === 'Not supported' ? (
                  <Badge variant='dot' tint='default' size='sm'>
                    Not supported
                  </Badge>
                ) : (
                  gpu.FP64 || 'N/A'
                )}
              </div>
            </div>
          </div>
        </div>

        <Separator className='my-6' />

        {/* Key Features */}
        <div>
          <h2 className='mb-4 text-2xl font-semibold'>Key Features</h2>
          <div className='text-muted-foreground space-y-3'>
            {gpu.Architecture && (
              <p>
                Built on the <span className='text-foreground font-medium'>{gpu.Architecture}</span> architecture, this
                GPU features <span className='text-foreground font-medium'>{gpu['CUDA Cores']}</span> CUDA cores and{' '}
                <span className='text-foreground font-medium'>{gpu['GPU memory']}</span> of memory.
              </p>
            )}
            {gpu['Total Graphics Power'] && (
              <p>
                With a TGP of <span className='text-foreground font-medium'>{gpu['Total Graphics Power']}</span>, this
                GPU is designed for{' '}
                {parseInt(gpu['Total Graphics Power']) > 400
                  ? 'high-performance computing and AI workloads'
                  : 'efficient computing with balanced performance'}
                .
              </p>
            )}
            {gpu['Release Date'] && (
              <p>
                Released in <span className='text-foreground font-medium'>{gpu['Release Date']}</span>, this GPU
                represents{' '}
                {new Date().getFullYear() - parseInt(gpu['Release Date'].split('.')[0]) <= 2
                  ? 'cutting-edge'
                  : 'proven'}{' '}
                technology in the GPU market.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
