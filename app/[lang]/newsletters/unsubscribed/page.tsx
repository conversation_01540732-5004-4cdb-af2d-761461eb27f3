import { config } from '@/data/config'

import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { getDictionary } from '@/lib/dictionaries-server'

import PageBody from '@/components/page-body'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)
  const data = await getPostById(6121, { type: 'pages', locale: params.lang })

  return {
    title: `${data.title.rendered}${config.siteTitleSplitter}${dictionary.common.title}`,
    description: dictionary.common.slogan,
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(6121, { type: 'pages', locale: params.lang })

  return (
    <div className='space-y-3'>
      <h1>{data.title.rendered}</h1>

      <PageBody content={data.content.rendered} className='prose' />
    </div>
  )
}
