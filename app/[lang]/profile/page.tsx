// import { getRbacProtectedResource } from './api/auth/rbac-protected-resource/get-rbac-protected-resource';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'

import { getUser } from '../../api/auth/user/get-user'

const Page = async () => {
  const user = await getUser()
  // const rbacProtectedResource = await getRbacProtectedResource();

  // Handle identities data safely if it exists
  const identities = user.claims?.identities
    ? typeof user.claims.identities === 'object'
      ? Object.keys(user.claims.identities as Record<string, unknown>)
      : []
    : []

  return (
    <div className='py-10'>
      <header className='mb-8'>
        <h1 className='text-4xl font-bold'>User Profile</h1>
      </header>

      {user.isAuthenticated && user.claims && (
        <div className='grid gap-6 md:grid-cols-[300px_1fr] lg:grid-cols-[300px_1fr]'>
          <div>
            <Card>
              <CardHeader className='space-y-4'>
                <div className='flex justify-center'>
                  <Avatar className='h-24 w-24'>
                    <AvatarImage src={user.claims.picture as string} alt={(user.claims.name as string) || ''} />
                    <AvatarFallback>{((user.claims.name as string) || '').substring(0, 2)}</AvatarFallback>
                  </Avatar>
                </div>
                <div className='space-y-2 text-center'>
                  <CardTitle>{user.claims.name || 'User'}</CardTitle>
                  <CardDescription>{user.claims.email || ''}</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {user.claims.username && (
                    <div className='space-y-2'>
                      <div className='text-sm font-medium'>Username</div>
                      <div>{user.claims.username}</div>
                    </div>
                  )}
                  {identities.length > 0 && (
                    <div className='space-y-2'>
                      <div className='text-sm font-medium'>Connected Accounts</div>
                      <div className='flex flex-wrap gap-2'>
                        {identities.map(identity => (
                          <Badge key={identity}>{identity}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle>User Claims</CardTitle>
                <CardDescription>Information associated with your user account</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className='h-[400px]'>
                  <div className='space-y-4'>
                    {Object.entries(user.claims)
                      .filter(([key]) => !['name', 'email', 'picture', 'username', 'identities'].includes(key))
                      .map(([key, value]) => (
                        <div key={key} className='space-y-1'>
                          <div className='flex justify-between'>
                            <div className='font-medium'>{key}</div>
                            <Badge>{typeof value}</Badge>
                          </div>
                          <div className='text-muted-foreground text-sm break-all'>
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </div>
                          <Separator className='mt-2' />
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {!user.isAuthenticated && (
        <Card>
          <CardHeader>
            <CardTitle>Not Authenticated</CardTitle>
            <CardDescription>Please log in to view your profile</CardDescription>
          </CardHeader>
        </Card>
      )}
    </div>
  )
}

export default Page
