import type { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchPaginatedPapers } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { BackgroundBoxes } from '@/components/ui/background-boxes'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'

// Import the RecentPapers component from sota to reuse it
import RecentPapers from '../sota/recent-papers'

interface Props {
  params: Promise<{
    lang: Locale
  }>
  searchParams: Promise<{
    page?: string
  }>
}

export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.sota.papers.title,
    description: dictionary.sota.papers.description,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl('/papers'),
  }
}

export default async function PapersListPage(props: Props) {
  const searchParams = await props.searchParams
  const params = await props.params
  const { lang } = params
  const t = await getDictionary(lang)

  // Parse the page number from the search params
  const currentPage = parseInt(searchParams.page || '1', 10)
  const pageSize = 30

  // Fetch papers with pagination
  const papersData = await fetchPaginatedPapers({
    locale: lang,
    page_num: currentPage,
    page_size: pageSize,
  }).catch(() => ({ r: 200, lang, data: [] }))

  // For this example, assume we have a total of 100 papers
  // In a real implementation, the API should return the total count
  const totalPages = 10

  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='w-full'>
          <div className='flex flex-col gap-4'>
            <Breadcrumb className='mb-2'>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}`} className='flex items-center gap-1'>
                      <IconHome className='h-4 w-4' />
                      {t.nav.home.title}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{t.sota.papers.title}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
              <BackgroundBoxes />
              <div className='pointer-events-none relative z-20 space-y-2'>
                <h1 className='text-3xl md:text-4xl'>{t.sota.papers.title}</h1>
                <p className='text-fg/60 text-lg'>{t.sota.papers.description}</p>
              </div>
            </div>

            {/* Update RecentPapers component to work with the top-level route */}
            <RecentPapers papers={papersData.data || []} basePath={`/${lang}/papers`} />

            <div className='mt-6 flex justify-between'>
              {currentPage > 1 && (
                <Button asChild>
                  <Link href={`/${lang}/papers?page=${currentPage - 1}`}>{t.common.previous}</Link>
                </Button>
              )}

              {currentPage > 1 && currentPage < totalPages && <div className='flex-1'></div>}

              {currentPage < totalPages && (
                <Button asChild>
                  <Link href={`/${lang}/papers?page=${currentPage + 1}`}>{t.common.next}</Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
