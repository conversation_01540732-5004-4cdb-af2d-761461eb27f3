'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'

import type { Locale } from '@/types'
import type { components } from '@/types/scraper'

import { useTranslation } from '@/utils/i18n'

import Timestamp from '@/components/timestamp'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Lens } from '@/components/ui/lens'

type PaperBase = components['schemas']['PaperBase']

interface RecentPapersProps {
  papers: PaperBase[]
  basePath?: string
}

export default function RecentPapers({ papers, basePath }: RecentPapersProps) {
  const params = useParams<{ lang: Locale }>()
  const lang = params?.lang || 'cn'

  const { t } = useTranslation()

  // Use the provided basePath or default to the sota path
  const paperLinkBasePath = basePath || `/${lang}/sota/papers`

  if (!papers.length) {
    return (
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>{t.sota.papers.noData}</CardTitle>
          <CardDescription>{t.sota.papers.noAvailableData}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className='group/recent-papers mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
      {papers.map(paper => (
        <Link href={`${paperLinkBasePath}/${paper.paper_id}`} key={paper.paper_id}>
          <Card className='h-full transition-shadow hover:shadow-xl'>
            {paper.img_url && (
              <Lens zoomFactor={2} lensSize={200}>
                <div className='relative aspect-video w-full overflow-hidden rounded-t-lg'>
                  <picture>
                    <img
                      src={paper.img_url}
                      alt={paper.title}
                      sizes='(max-width: 768px) 100vw, 50vw'
                      className='object-cover object-top'
                      loading='lazy'
                    />
                  </picture>
                </div>
              </Lens>
            )}
            <CardHeader className='pb-2'>
              <div className='h-[2.5lh]'>
                <CardTitle className='line-clamp-2'>{paper.title}</CardTitle>
              </div>
              <CardDescription>
                {paper.authors.slice(0, 3).join(', ')}
                {paper.authors.length > 3 && ', et al.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-fg/60 flex text-sm'>
                <Timestamp date={paper.created_timestamp} />
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}
