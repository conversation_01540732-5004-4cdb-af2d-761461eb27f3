'use client'

import {
  IconArrowDown,
  IconArrowsSort,
  IconArrowUp,
  IconBrandGithubFilled,
  IconFileDescription,
} from '@tabler/icons-react'
import type { ColumnDef } from '@tanstack/react-table'

import type { BenchmarkResult } from '@/lib/scraper-api'

import { useTranslation } from '@/utils/i18n'

import { BackgroundBoxes } from '@/components/ui/background-boxes'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'

interface BenchmarkTableProps {
  benchmarkName: string
  metrics: string[]
  results: BenchmarkResult[]
}

export function BenchmarkTable({ benchmarkName, metrics, results }: BenchmarkTableProps) {
  const { t } = useTranslation()

  // Create columns dynamically based on metrics
  const columns: ColumnDef<BenchmarkResult>[] = [
    {
      accessorKey: 'model_name',
      header: ({ column }) => {
        return (
          <div
            className='flex cursor-pointer items-center'
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t.sota.modelName}
            {column.getIsSorted() === 'asc' ? (
              <IconArrowUp className='ml-2 size-4' />
            ) : column.getIsSorted() === 'desc' ? (
              <IconArrowDown className='ml-2 size-4' />
            ) : (
              <IconArrowsSort className='ml-2 size-4 opacity-50' />
            )}
          </div>
        )
      },
      cell: ({ row }) => {
        return row.original.model_name || `${t.sota.gpuBenchmarks.model} ${row.index + 1}`
      },
    },
    ...metrics.map(
      (metric): ColumnDef<BenchmarkResult> => ({
        id: metric,
        accessorFn: row => row.metrics?.[metric],
        header: ({ column }) => {
          return (
            <div
              className='flex cursor-pointer items-center'
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              {metric}
              {column.getIsSorted() === 'asc' ? (
                <IconArrowUp className='ml-2 size-4' />
              ) : column.getIsSorted() === 'desc' ? (
                <IconArrowDown className='ml-2 size-4' />
              ) : (
                <IconArrowsSort className='ml-2 size-4 opacity-50' />
              )}
            </div>
          )
        },
        cell: ({ getValue }) => {
          const value = getValue()
          if (value !== undefined && value !== null) {
            if (typeof value === 'number') {
              return Number(value).toFixed(4)
            } else if (typeof value === 'object' && Object.keys(value).length === 0) {
              return '-'
            } else {
              return String(value)
            }
          }
          return '-'
        },
        sortingFn: (rowA, rowB) => {
          const valueA = rowA.original.metrics?.[metric]
          const valueB = rowB.original.metrics?.[metric]

          // Handle null/undefined values
          if (valueA === null || valueA === undefined) return 1
          if (valueB === null || valueB === undefined) return -1

          // Convert to numbers if possible
          const numA = typeof valueA === 'number' ? valueA : typeof valueA === 'string' ? parseFloat(valueA) : NaN
          const numB = typeof valueB === 'number' ? valueB : typeof valueB === 'string' ? parseFloat(valueB) : NaN

          if (!isNaN(numA) && !isNaN(numB)) {
            return numA - numB
          }

          // Fallback to string comparison
          return String(valueA).localeCompare(String(valueB))
        },
        enableSorting: true,
      })
    ),
    {
      accessorKey: 'paper_title',
      header: 'Paper Title',
      cell: ({ row }) => {
        const paperTitle = row.original.paper_title
        const paperUrl = row.original.paper_url

        if (paperTitle && paperUrl) {
          return (
            <a href={paperUrl} target='_blank' rel='noopener noreferrer' className='text-ac flex items-start gap-x-1'>
              <IconFileDescription className='size-5 shrink-0' />
              {paperTitle}
            </a>
          )
        }
        return paperTitle || '-'
      },
    },
    {
      accessorKey: 'paper_repo',
      header: 'Repository',
      cell: ({ row }) => {
        const repo = row.original.paper_repo
        if (repo) {
          return (
            <a href={repo} target='_blank' rel='noopener noreferrer' aria-label='GitHub Repository'>
              <IconBrandGithubFilled className='size-5 shrink-0' />
            </a>
          )
        }
        return '-'
      },
    },
  ]

  return (
    <div>
      <div className='bg-ac/10 relative mb-6 overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
        <BackgroundBoxes />
        <div className='pointer-events-none relative z-20 space-y-2'>
          <h1 className='text-3xl md:text-4xl'>{benchmarkName}</h1>
          {metrics.length > 0 && (
            <div className='mt-4'>
              <h3 className='font-medium'>{t.sota.metrics}</h3>
              <div className='mt-2 flex flex-wrap gap-2'>
                {metrics.map((metric, idx) => (
                  <Badge key={idx} tint={'orange'}>
                    {metric}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <h2 className='mt-6 mb-2 text-3xl'>{t.sota.results}</h2>
      <p className='text-fg/60 text-lg'>{t.sota.resultsDescription}</p>

      {results.length > 0 ? (
        <DataTable
          columns={columns}
          data={results}
          options={{
            initialState: {
              pagination: {
                pageSize: 20,
              },
            },
          }}
          filterColumn='model_name'
          filterPlaceholder='Search models...'
        />
      ) : (
        <div className='border-fg/10 rounded-md border p-8 text-center'>
          <h3 className='text-lg font-medium'>{t.sota.gpuBenchmarks.noData}</h3>
          <p className='text-fg/60 mt-2'>{t.sota.noResultsData}</p>
        </div>
      )}
    </div>
  )
}
