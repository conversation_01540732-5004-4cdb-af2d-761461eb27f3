import type { Metadata } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { config } from '@/data/config'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchTaskBench, fetchTaskInfo } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

import { BenchmarkTable } from './benchmark-table'

type Props = {
  params: Promise<{ lang: Locale; task_id: string; bench_id: string }>
}

// Generate metadata for the page
export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;
  const { lang, task_id, bench_id } = params
  const dictionary = await getDictionary(params.lang)

  try {
    const benchmarkData = await fetchTaskBench(task_id, bench_id, { locale: lang })
    return {
      title: `${benchmarkData.data.evaluation_name}${config.siteTitleSplitter}${dictionary.nav.sota.title}`,
      description: dictionary.sota.resultsDescription,
      metadataBase: new URL(BASE_URL),
      alternates: generateCanonicalUrl(`/sota/tasks/${task_id}/benchmark/${bench_id}`),
    }
  } catch {
    return {
      title: dictionary.nav.sota.title,
    }
  }
}

export default async function BenchmarkDetailPage(props: Props) {
  const params = await props.params;
  const { lang, task_id, bench_id } = params
  const t = await getDictionary(lang)

  // Fetch data from APIs
  let taskInfoResponse
  let benchmarkResponse

  try {
    ;[taskInfoResponse, benchmarkResponse] = await Promise.all([
      fetchTaskInfo(task_id, { locale: lang }),
      fetchTaskBench(task_id, bench_id, { locale: lang }),
    ])
  } catch {
    notFound()
  }

  // Get task name for breadcrumb and title
  const taskName = taskInfoResponse.data?.task_name || ''
  const evaluationData = benchmarkResponse.data

  // Extract metrics from the first result that has metrics
  const metricsObj = evaluationData.result_list.find(r => r.metrics)?.metrics || {}
  const metrics = Object.keys(metricsObj || {})

  return (
    <div>
      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='w-full'>
          <div className='flex flex-col gap-4'>
            {/* Breadcrumb Navigation */}
            <Breadcrumb className='mb-2'>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}`} className='flex items-center gap-1'>
                      <IconHome className='h-4 w-4' />
                      {t.nav.home.title}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}/sota`}>SOTA</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}/sota/tasks/${task_id}`}>{taskName}</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{evaluationData.evaluation_name}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Sortable Benchmark Table */}
            <BenchmarkTable
              benchmarkName={evaluationData.evaluation_name}
              metrics={metrics}
              results={evaluationData.result_list}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
