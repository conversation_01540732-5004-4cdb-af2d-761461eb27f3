import type { Metadata } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { config } from '@/data/config'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchTaskInfo } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { BackgroundBoxes } from '@/components/ui/background-boxes'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'

import BenchmarksList from './benchmarks-list'

type Props = {
  params: Promise<{ lang: Locale; task_id: string }>
}

// Generate metadata for the page
export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;
  const { lang, task_id } = params
  const dictionary = await getDictionary(params.lang)

  try {
    // Get task info from the API
    const taskInfoResult = await fetchTaskInfo(task_id, { locale: lang })

    return {
      title: `${taskInfoResult.data.task_name}${config.siteTitleSplitter}${dictionary.nav.sota.title}`,
      description: dictionary.sota.allBenchmarksForTask,
      metadataBase: new URL(BASE_URL),
      alternates: generateCanonicalUrl(`/sota/tasks/${task_id}`),
    }
  } catch {
    return {
      title: dictionary.sota.taskNotFound,
    }
  }
}

export default async function TaskBenchmarkPage(props: Props) {
  const params = await props.params;
  const { lang, task_id } = params
  const t = await getDictionary(lang)

  // Fetch task info from the API
  const taskInfoData = await fetchTaskInfo(task_id, { locale: lang })

  // If no data, 404
  if (!taskInfoData.data) {
    notFound()
  }

  const taskName = taskInfoData.data.task_name

  return (
    <div>
      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='w-full'>
          <div className='flex flex-col gap-4'>
            {/* Breadcrumb Navigation */}
            <Breadcrumb className='mb-2'>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}`} className='flex items-center gap-1'>
                      <IconHome className='h-4 w-4' />
                      {t.nav.home.title}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}/sota`}>SOTA</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{taskName}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Header Section */}
            <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
              <BackgroundBoxes />
              <div className='pointer-events-none relative z-20 space-y-2'>
                <h1 className='text-3xl md:text-4xl'>{taskName}</h1>
                <p className='text-fg/60 text-lg'>{taskInfoData.data.abstract_task_100}</p>
              </div>
            </div>

            <BenchmarksList taskId={task_id} benchmarks={taskInfoData.data.evaluation_list} />
          </div>
        </div>
      </div>
    </div>
  )
}
