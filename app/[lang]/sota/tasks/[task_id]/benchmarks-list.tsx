'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { IconCrown } from '@tabler/icons-react'

import type { EvaluationItem } from '@/lib/scraper-api'

import { useTranslation } from '@/utils/i18n'

import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Tooltip } from '@/components/ui/tooltip'

interface BenchmarksListProps {
  taskId: string
  benchmarks: EvaluationItem[]
}

export default function BenchmarksList({ taskId, benchmarks }: BenchmarksListProps) {
  const params = useParams<{ lang: string }>()
  const lang = params?.lang || 'cn'
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')

  // Filter benchmarks based on search query
  const filteredBenchmarks = benchmarks.filter(benchmark => {
    const searchLower = searchQuery.toLowerCase().replaceAll(' ', '')
    return (
      benchmark.dataset_name?.replaceAll(' ', '').toLowerCase().includes(searchLower) ||
      (benchmark.best_model && benchmark.best_model.replaceAll(' ', '').toLowerCase().includes(searchLower))
    )
  })

  if (!benchmarks.length) {
    return (
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>{t.sota.gpuBenchmarks.noData}</CardTitle>
          <CardDescription>{t.sota.noBenchmarkData}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <>
      <Input
        type='text'
        placeholder={t.sota.benchmarkDetails.searchPlaceholder}
        value={searchQuery}
        onChange={e => setSearchQuery(e.target.value)}
        className='max-w-96'
      />

      {filteredBenchmarks.length === 0 && searchQuery ? (
        <Card className='mb-6'>
          <CardHeader>
            <CardTitle>{t.sota.benchmarkDetails.noResults}</CardTitle>
            <CardDescription>{t.sota.benchmarkDetails.tryDifferentSearch}</CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className='sota-benchmarks-list mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
          {filteredBenchmarks.map(benchmark => (
            <Link
              key={benchmark.evaluation_id}
              href={`/${lang}/sota/tasks/${taskId}/benchmark/${benchmark.evaluation_id}`}
            >
              <Card className='h-full transition-shadow hover:shadow-xl'>
                <CardHeader>
                  <CardTitle>{benchmark.dataset_name}</CardTitle>
                  <CardDescription>
                    {benchmark.best_model ? (
                      <div className='flex items-start gap-x-1'>
                        <Tooltip label={t.sota.benchmarkDetails.bestModel}>
                          <IconCrown className='size-5 fill-current text-amber-500' />
                        </Tooltip>
                        {benchmark.best_model}
                      </div>
                    ) : null}
                  </CardDescription>
                </CardHeader>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </>
  )
}
