import type { Metadata } from 'next'
import Link from 'next/link'
import { IconBrandGithub, IconExternalLink, IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchPaperDetail, type PaperDetailResponse } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import Sidebar from '@/components/sidebar'
import { Alert } from '@/components/ui/alert'
import { BackgroundBoxes } from '@/components/ui/background-boxes'
import { Badge } from '@/components/ui/badge'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Props {
  params: Promise<{
    lang: Locale
    paper_id: string
  }>
}

export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;
  const { lang, paper_id } = params
  const dictionary = await getDictionary(lang)

  let title = dictionary.sota.papers.title
  let description = dictionary.sota.papers.description

  try {
    const paperData = await fetchPaperDetail(paper_id, { locale: lang })
    if (paperData?.data) {
      title = `${paperData.data.title} | ${dictionary.sota.papers.title}`
      description = paperData.data.abstract.slice(0, 160) + '...'
    }
  } catch (error) {
    console.error('Failed to fetch paper details for metadata:', error)
  }

  return {
    title,
    description,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/sota/papers/${paper_id}`),
  }
}

export default async function PaperDetailPage(props: Props) {
  const params = await props.params;
  const { lang, paper_id } = params
  const t = await getDictionary(lang)

  let paperData: PaperDetailResponse | null = null
  let error = null

  try {
    paperData = await fetchPaperDetail(paper_id, { locale: lang })
  } catch (e) {
    error = e
    console.error('Failed to fetch paper details:', e)
  }

  const formatTimestamp = (timestamp: number) => {
    try {
      const date = new Date(timestamp * 1000)
      return date.toLocaleDateString()
    } catch {
      return String(timestamp)
    }
  }

  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='lg:w-2/3'>
          <div className='flex flex-col gap-4'>
            <Breadcrumb className='mb-2'>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}`} className='flex items-center gap-1'>
                      <IconHome className='h-4 w-4' />
                      {t.nav.home.title}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}/sota`}>SOTA</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{t.sota.papers.title}</BreadcrumbPage>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{paperData?.data?.title || paper_id}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {error ? (
              <Alert tint='danger' label={t.sota.papers.noData}>
                {t.sota.papers.noAvailableData}
              </Alert>
            ) : !paperData?.data ? (
              <Alert label={t.sota.papers.noData}>{t.sota.papers.noAvailableData}</Alert>
            ) : (
              <>
                <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
                  <BackgroundBoxes />
                  <div className='pointer-events-none relative z-20 space-y-2'>
                    <h1 className='text-3xl md:text-4xl'>{paperData.data.title}</h1>
                    <div className='text-fg/60 text-lg'>{paperData.data.authors.join(', ')}</div>
                    <div className='flex flex-wrap gap-2 pt-2'>
                      <Badge variant='default' tint='blue'>
                        {t.sota.papers.releaseDate}: {formatTimestamp(paperData.data.created_timestamp)}
                      </Badge>
                      {/* <Badge variant='default' tint='green'>
                        {t.sota.papers.retrievalDate}: {formatDate(paperData.data.fetch_at)}
                      </Badge> */}
                    </div>
                  </div>
                </div>

                {paperData.data.img_url && (
                  <div className='relative mt-4 h-96 w-full overflow-hidden rounded-lg'>
                    <picture>
                      <img
                        src={paperData.data.img_url}
                        alt={paperData.data.title}
                        sizes='(max-width: 1024px) 100vw, 66vw'
                        className='object-contain'
                        loading='lazy'
                      />
                    </picture>
                  </div>
                )}

                <Card className='mt-4'>
                  <CardHeader>
                    <CardTitle>{t.sota.papers.abstract}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className='whitespace-pre-line'>{paperData.data.abstract}</p>
                  </CardContent>
                </Card>

                <div className='mt-4 flex flex-wrap gap-4'>
                  <Button asChild>
                    <a href={paperData.data.paper_link} target='_blank' rel='noopener noreferrer'>
                      <IconExternalLink className='mr-2 h-4 w-4' />
                      {t.sota.papers.viewDetails}
                    </a>
                  </Button>
                  {paperData.data.github_link && (
                    <Button variant='outline' asChild>
                      <a href={paperData.data.github_link} target='_blank' rel='noopener noreferrer'>
                        <IconBrandGithub className='mr-2 h-4 w-4' />
                        {'View Code'}
                      </a>
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        <div className='lg:w-1/3'>
          <Sidebar />
        </div>
      </div>
    </div>
  )
}
