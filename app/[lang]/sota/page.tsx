import Link from 'next/link'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchAllSota, type SotaResponse } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'
import nf from '@/utils/numberFormat'

import { SotaCategoryItem } from '@/components/sota-category-item'
import { BackgroundBoxes } from '@/components/ui/background-boxes'

export async function generateMetadata(props: { params: Promise<{ lang: Locale }> }) {
  const params = await props.params;
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.sota.title,
    description: dictionary.nav.sota.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/sota`),
  }
}

export default async function Page({ params }: { params: Promise<{ lang: Locale }> }) {
  const { lang } = await params
  const t = await getDictionary(lang)

  // Fetch data in parallel
  const [sotaData] = await Promise.all([
    fetchAllSota({ locale: lang }).catch(() => ({ r: 200, lang, data: [] }) satisfies SotaResponse),
  ])

  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='w-full'>
          <div className='flex flex-col gap-4'>
            {/* Header Section */}
            <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
              <BackgroundBoxes />
              <div className='pointer-events-none relative z-20 space-y-2'>
                <h1 className='text-3xl md:text-4xl'>{t.sota.pageTitle}</h1>
                <p className='text-fg/60 text-lg'>{t.sota.pageDescription}</p>
              </div>
            </div>

            {/* Categories Section */}
            <div className='mt-6 space-y-2'>
              <h2 className='text-3xl'>{t.sota.categories.title}</h2>
              <p className='text-fg/60 text-lg'>{t.sota.categories.description}</p>
            </div>
            <div className='group/sota-categories mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
              {sotaData.data.map(category => (
                <div
                  key={category.area_id}
                  className='flex flex-col rounded-lg border transition-shadow hover:shadow-xl'
                >
                  <SotaCategoryItem category={category} lang={lang} taskCountText={t.sota.categories.taskCount} />

                  {/* Task Preview for this Category */}
                  <div className='divide-border divide-y'>
                    {category.task_list.map(task => (
                      <Link
                        key={task.task_id}
                        href={`/${lang}/sota/tasks/${task.task_id}`}
                        className='hover:text-ac block px-3 py-2 transition-colors'
                      >
                        <h4 className='text-sm font-medium'>{task.task_name}</h4>
                        <p className='text-fg/60 text-xs'>
                          {nf.format(task.num_benchmarks)} {t.sota.taskList.benchmarks}, {nf.format(task.num_models)}{' '}
                          {t.sota.taskList.models}, {nf.format(task.num_papers)} {t.sota.taskList.papers}
                        </p>
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
