'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'

import type { TaskItem } from '@/lib/scraper-api'

import { useTranslation } from '@/utils/i18n'

import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

interface TaskListProps {
  tasks: TaskItem[]
}

export default function TaskList({ tasks }: TaskListProps) {
  const params = useParams<{ lang: string }>()
  const lang = params?.lang || 'cn'
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')

  // Filter tasks based on search query
  const filteredTasks = tasks.filter(task => {
    const searchLower = searchQuery.toLowerCase().replaceAll(' ', '')
    return task.task_name.replaceAll(' ', '').toLowerCase().includes(searchLower)
  })

  if (!tasks.length) {
    return (
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>{t.sota.taskList.noData}</CardTitle>
          <CardDescription>{t.sota.taskList.noTaskData}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <>
      <Input
        type='text'
        placeholder={t.sota.taskList.searchPlaceholder || 'Search tasks...'}
        value={searchQuery}
        onChange={e => setSearchQuery(e.target.value)}
        className='max-w-96'
      />

      {filteredTasks.length === 0 && searchQuery ? (
        <Card className='mb-6'>
          <CardHeader>
            <CardTitle>{t.sota.taskList.noResults || 'No results found'}</CardTitle>
            <CardDescription>{t.sota.taskList.tryDifferentSearch || 'Try a different search term'}</CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className='group/task-list mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
          {filteredTasks.map(task => (
            <Link key={task.task_id} href={`/${lang}/sota/tasks/${task.task_id}`}>
              <Card className='transition-shadow hover:shadow-xl'>
                <div className='space-y-1 p-2'>
                  <h3 className='text-lg'>{task.task_name}</h3>
                  <div className='text-fg/60 text-sm'>
                    {task.num_papers !== undefined && `${task.num_papers}${t.sota.taskList.papers}`}
                    {task.num_papers !== undefined && task.num_benchmarks !== undefined && ' | '}
                    {task.num_benchmarks !== undefined && `${task.num_benchmarks}${t.sota.taskList.benchmarks}`}
                  </div>
                </div>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </>
  )
}
