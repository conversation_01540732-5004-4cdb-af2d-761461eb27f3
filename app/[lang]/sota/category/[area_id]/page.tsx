import Link from 'next/link'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'
import { fetchSotaCategory, type CategoryResponse } from '@/lib/scraper-api'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { BackgroundBoxes } from '@/components/ui/background-boxes'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import TaskList from './task-list'

export async function generateMetadata(props: { params: Promise<{ lang: Locale; area_id: string }> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)
  const categoryData = await fetchSotaCategory(params.area_id, { locale: params.lang }).catch(
    () =>
      ({
        r: 200,
        lang: params.lang,
        data: { area_id: params.area_id, area_name: params.area_id, task_list: [] },
      }) satisfies CategoryResponse
  )

  return {
    title: `${categoryData.data.area_name} - ${dictionary.nav.sota.title}`,
    description: dictionary.nav.sota.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/sota/category/${params.area_id}`),
  }
}

export default async function CategoryPage({ params }: { params: Promise<{ lang: Locale; area_id: string }> }) {
  const { lang, area_id } = await params
  const t = await getDictionary(lang)

  const categoryData = await fetchSotaCategory(area_id, { locale: lang }).catch(
    () =>
      ({
        r: 200,
        lang,
        data: {
          area_id,
          area_name: area_id,
          task_list: [],
        },
      }) as unknown as CategoryResponse
  )

  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='w-full'>
          <div className='flex flex-col gap-4'>
            {/* Breadcrumb Navigation */}
            <Breadcrumb className='mb-2'>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}`} className='flex items-center gap-1'>
                      <IconHome className='h-4 w-4' />
                      {t.nav.home.title}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link href={`/${lang}/sota`}>SOTA</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>{categoryData.data.area_name}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Header Section */}
            <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-7 md:px-10 md:py-14'>
              <BackgroundBoxes />
              <div className='pointer-events-none relative z-20 space-y-2'>
                <h1 className='text-3xl md:text-4xl'>{categoryData.data.area_name}</h1>
                <p className='text-fg/60 text-lg'>{categoryData.data.abstract_area_100}</p>
              </div>
            </div>

            {categoryData.data.task_list && categoryData.data.task_list.length > 0 ? (
              <TaskList tasks={categoryData.data.task_list} />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>{t.sota.taskList.noData}</CardTitle>
                  <CardDescription>{t.sota.taskList.noTaskData}</CardDescription>
                </CardHeader>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
