'use client'

import { useState } from 'react'
import { Bar, BarChart, CartesianGrid, Legend, XAxis, <PERSON>Axi<PERSON> } from 'recharts'
import { IconChartBar, IconTable } from '@tabler/icons-react'

import type { components } from '@/types/scraper'

import { useTranslation } from '@/utils/i18n'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from '@/components/ui/chart'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'

type GPUSoftwareBenchmark = components['schemas']['GPUSoftwareBenchmark']

type MetricType = 'request' | 'output' | 'total' | 'all'

interface GpuBenchmarksProps {
  softwareData: GPUSoftwareBenchmark[]
}

export default function GpuBenchmarks({ softwareData }: GpuBenchmarksProps) {
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart')
  const [selectedMetrics, setSelectedMetrics] = useState<Record<number, MetricType>>(
    Array.isArray(softwareData) ? softwareData.reduce((acc, _, index) => ({ ...acc, [index]: 'all' }), {}) : {}
  )

  const { t } = useTranslation()

  const hasSoftwareData = Array.isArray(softwareData) && softwareData.length > 0

  const updateSelectedMetric = (index: number, metric: MetricType) => {
    setSelectedMetrics(prev => ({
      ...prev,
      [index]: metric,
    }))
  }

  // Define chart configs
  const softwareChartConfig: ChartConfig = {
    request: {
      label: t.sota.gpuBenchmarks.requestThroughput,
      color: 'var(--color-purple-500)',
    },
    output: {
      label: t.sota.gpuBenchmarks.outputTokenThroughput,
      color: 'var(--color-teal-500)',
    },
    total: {
      label: t.sota.gpuBenchmarks.totalTokenThroughput,
      color: 'var(--color-blue-500)',
    },
  }

  if (!hasSoftwareData) {
    return (
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle>{t.sota.gpuBenchmarks.noData}</CardTitle>
          <CardDescription>{t.sota.gpuBenchmarks.noGpuData}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className='mb-6'>
      <div className='mb-4 flex flex-wrap items-center justify-between gap-4'>
        <h3 className='font-medium'>{t.sota.gpuBenchmarks.softwarePerformance}</h3>

        <ToggleGroup
          type='single'
          value={viewMode}
          onValueChange={value => value && setViewMode(value as 'chart' | 'table')}
        >
          <ToggleGroupItem value='chart' aria-label={t.sota.gpuBenchmarks.showChartView}>
            <IconChartBar className='h-4 w-4' />
          </ToggleGroupItem>
          <ToggleGroupItem value='table' aria-label={t.sota.gpuBenchmarks.showTableView}>
            <IconTable className='h-4 w-4' />
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {hasSoftwareData ? (
        <div className='space-y-4'>
          {softwareData.map((benchmark, index) => {
            // Transform software benchmark data for charts
            const softwareChartData =
              benchmark.list?.map(item => ({
                'name': item.gpu,
                'request': item.metrics?.['Request Throughput'],
                'output': item.metrics?.['Output Token Throughput'],
                'total': item.metrics?.['Total Token Throughput'],
                'Max Context Length': item['Max Context Length'],
                'Max Sequences': item['Max Sequences'],
              })) || []

            const currentMetric = selectedMetrics[index] || 'all'

            return (
              <Card key={index}>
                <CardHeader className='flex flex-col space-y-1.5'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <CardTitle>{benchmark.model || t.sota.gpuBenchmarks.model}</CardTitle>
                      <CardDescription>
                        {t.sota.gpuBenchmarks.environment}: {benchmark.env || t.sota.gpuBenchmarks.unknown}
                      </CardDescription>
                    </div>
                    {viewMode === 'chart' && (
                      <Select
                        value={currentMetric}
                        onValueChange={value => updateSelectedMetric(index, value as MetricType)}
                      >
                        <SelectTrigger className='w-[180px]'>
                          <SelectValue placeholder={t.sota.gpuBenchmarks.selectMetric} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>{t.sota.gpuBenchmarks.allMetrics}</SelectItem>
                          <SelectItem value='request'>{t.sota.gpuBenchmarks.requestThroughput}</SelectItem>
                          <SelectItem value='output'>{t.sota.gpuBenchmarks.outputTokenThroughput}</SelectItem>
                          <SelectItem value='total'>{t.sota.gpuBenchmarks.totalTokenThroughput}</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {viewMode === 'table' ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>GPU</TableHead>
                          <TableHead>{t.sota.gpuBenchmarks.maxContextLength}</TableHead>
                          <TableHead>{t.sota.gpuBenchmarks.maxSequences}</TableHead>
                          <TableHead>{t.sota.gpuBenchmarks.requestThroughput}</TableHead>
                          <TableHead>{t.sota.gpuBenchmarks.outputTokenThroughput}</TableHead>
                          <TableHead>{t.sota.gpuBenchmarks.totalTokenThroughput}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {benchmark.list?.map((item, idx) => (
                          <TableRow key={idx}>
                            <TableCell>{item.gpu}</TableCell>
                            <TableCell className='font-mono'>{item['Max Context Length']}</TableCell>
                            <TableCell className='font-mono'>{item['Max Sequences']}</TableCell>
                            <TableCell className='font-mono'>{item.metrics?.['Request Throughput']}</TableCell>
                            <TableCell className='font-mono'>{item.metrics?.['Output Token Throughput']}</TableCell>
                            <TableCell className='font-mono'>{item.metrics?.['Total Token Throughput']}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className='w-full'>
                      <ChartContainer config={softwareChartConfig}>
                        <BarChart data={softwareChartData} accessibilityLayer>
                          <CartesianGrid strokeDasharray='3 3' />
                          <XAxis dataKey='name' textAnchor='end' />
                          <YAxis
                            scale={currentMetric === 'request' ? 'log' : 'auto'}
                            domain={['auto', 'auto']}
                            allowDataOverflow
                          />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Legend />
                          {(currentMetric === 'all' || currentMetric === 'request') && (
                            <Bar
                              dataKey='request'
                              name={t.sota.gpuBenchmarks.requestThroughput}
                              fill='var(--color-request)'
                            />
                          )}
                          {(currentMetric === 'all' || currentMetric === 'output') && (
                            <Bar
                              dataKey='output'
                              name={t.sota.gpuBenchmarks.outputTokenThroughput}
                              fill='var(--color-output)'
                            />
                          )}
                          {(currentMetric === 'all' || currentMetric === 'total') && (
                            <Bar
                              dataKey='total'
                              name={t.sota.gpuBenchmarks.totalTokenThroughput}
                              fill='var(--color-total)'
                            />
                          )}
                        </BarChart>
                      </ChartContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{t.sota.gpuBenchmarks.noData}</CardTitle>
            <CardDescription>{t.sota.gpuBenchmarks.noSoftwareData}</CardDescription>
          </CardHeader>
        </Card>
      )}
    </div>
  )
}
