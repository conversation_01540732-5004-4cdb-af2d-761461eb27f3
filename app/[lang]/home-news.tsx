'use client'

import Link from 'next/link'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import { NewsItem } from '@/components/news-item'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

export default function HomeNews() {
  const { t } = useTranslation()

  const { data: dataPosts, isLoading: isLoadingPosts } = useRecentPosts({
    type: 'posts',
    page: 1,
    per_page: 5,
    _embed: true,
    _fields: WP_FIELDS_POST_LIST,
  })

  return (
    <div className='space-y-6'>
      {dataPosts?.json?.map((post, idx) => (
        <NewsItem data={post} key={idx} />
      ))}

      {isLoadingPosts &&
        [...new Array(5)].map((_, idx) => (
          <div key={idx} className='flex gap-4'>
            <div className='flex-1'>
              <div className='text-2xl first:mt-0'>
                <Skeleton className='mb-4 h-[32px] w-3/4' />
                <Skeleton className='h-[100px]' />
              </div>
            </div>
            <Skeleton className='mb-4 h-[148px] w-1/4' />
          </div>
        ))}

      <Button size='lg' asChild>
        <Link href='/news'>{t.common.moreLinks.news}</Link>
      </Button>
    </div>
  )
}
