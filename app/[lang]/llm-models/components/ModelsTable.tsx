'use client'

import Link from 'next/link'
import { useParams } from 'next/navigation'
import { IconArrowDown, IconArrowsSort, IconArrowUp, IconCoin } from '@tabler/icons-react'
import type { ColumnDef } from '@tanstack/react-table'

import type { ModelWithMeta } from '@/types/ai'

import { modelProviderMap } from '@/lib/ai/model-meta'
import { customModels } from '@/lib/ai/models'

import { getModelInfo } from '@/utils/getModelInfo'
import { useTranslation } from '@/utils/i18n'
import { generateModelSlug } from '@/utils/model-utils'

import { ModelIcon } from '@/components/model-icon'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'

interface ModelsTableProps {
  initialModels?: ModelWithMeta[]
}

export function ModelsTable({ initialModels }: ModelsTableProps) {
  const { t, translate } = useTranslation()
  const params = useParams()
  const lang = params.lang as string

  // Use provided models or default to all custom models
  const models = initialModels || customModels

  const columns: ColumnDef<ModelWithMeta>[] = [
    {
      accessorKey: 'id',
      header: ({ column }) => {
        return (
          <div
            className='flex cursor-pointer items-center'
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            {t.llmModelsPage.modelColumn}
            {column.getIsSorted() === 'asc' ? (
              <IconArrowUp className='ml-2 size-4' />
            ) : column.getIsSorted() === 'desc' ? (
              <IconArrowDown className='ml-2 size-4' />
            ) : (
              <IconArrowsSort className='ml-2 size-4 opacity-50' />
            )}
          </div>
        )
      },
      cell: ({ row }) => {
        const model = row.original
        const { modelProvider, modelNameUI } = getModelInfo(model)
        const providerInfo = modelProviderMap[modelProvider] || { name: modelProvider, logo: '' }
        const modelSlug = generateModelSlug(model.id)

        return (
          <Link href={`/${lang}/llm-models/${modelSlug}`} className='flex items-center gap-2 hover:underline'>
            <ModelIcon model={modelNameUI} size={32} />
            <div>
              <div className='font-medium'>{modelNameUI}</div>
              <div className='text-muted-foreground flex flex-wrap items-center gap-1 text-sm'>
                <span>{providerInfo.name.replace('LAPLACE', 'Xiaosuan')}</span>
              </div>
            </div>
          </Link>
        )
      },
    },
    {
      accessorKey: 'inputPrice',
      header: ({ column }) => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <IconCoin className='mr-1 size-4' />
          {t.llmModelsPage.inputPrice}
          {column.getIsSorted() === 'asc' ? (
            <IconArrowUp className='ml-2 size-4' />
          ) : column.getIsSorted() === 'desc' ? (
            <IconArrowDown className='ml-2 size-4' />
          ) : (
            <IconArrowsSort className='ml-2 size-4 opacity-50' />
          )}
        </div>
      ),
      cell: ({ row }) => {
        const pricing = row.original.pricing
        if (!pricing || typeof pricing.inputCostPerMil === 'undefined') return t.llmModelsPage.noData

        return translate('llmModelsPage.pricePerMillion', { price: pricing.inputCostPerMil })
      },
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.pricing?.inputCostPerMil || 0
        const valueB = rowB.original.pricing?.inputCostPerMil || 0
        return valueA - valueB
      },
      enableHiding: true,
    },
    {
      accessorKey: 'outputPrice',
      header: ({ column }) => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <IconCoin className='mr-1 size-4' />
          {t.llmModelsPage.outputPrice}
          {column.getIsSorted() === 'asc' ? (
            <IconArrowUp className='ml-2 size-4' />
          ) : column.getIsSorted() === 'desc' ? (
            <IconArrowDown className='ml-2 size-4' />
          ) : (
            <IconArrowsSort className='ml-2 size-4 opacity-50' />
          )}
        </div>
      ),
      cell: ({ row }) => {
        const pricing = row.original.pricing
        if (!pricing || typeof pricing.outputCostPerMil === 'undefined') return t.llmModelsPage.noData

        return translate('llmModelsPage.pricePerMillion', { price: pricing.outputCostPerMil })
      },
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.pricing?.outputCostPerMil || 0
        const valueB = rowB.original.pricing?.outputCostPerMil || 0
        return valueA - valueB
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: 'maxLength',
      header: ({ column }) => (
        <div
          className='flex cursor-pointer items-center'
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t.llmModelsPage.contextLength}
          {column.getIsSorted() === 'asc' ? (
            <IconArrowUp className='ml-2 size-4' />
          ) : column.getIsSorted() === 'desc' ? (
            <IconArrowDown className='ml-2 size-4' />
          ) : (
            <IconArrowsSort className='ml-2 size-4 opacity-50' />
          )}
        </div>
      ),
      cell: ({ row }) => {
        const maxLength = row.original.parameters?.maximumLength?.value
        if (!maxLength) return t.llmModelsPage.noData

        // Format number with commas
        return maxLength.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      sortingFn: (rowA, rowB) => {
        const valueA = rowA.original.parameters?.maximumLength?.value || 0
        const valueB = rowB.original.parameters?.maximumLength?.value || 0
        return valueA - valueB
      },
      enableHiding: true,
    },
    {
      accessorKey: 'features',
      header: t.llmModelsPage.features,
      cell: ({ row }) => {
        const features = row.original.features
        if (!features || Object.keys(features).length === 0) return t.llmModelsPage.noData

        return (
          <div className='flex flex-wrap gap-1'>
            {Object.entries(features)
              .filter(([value]) => !!value)
              .map(([key]) => (
                <Badge key={key} variant='default' size='sm'>
                  {key.replace(/_/g, ' ')}
                </Badge>
              ))}
          </div>
        )
      },
      enableHiding: true,
    },
    // {
    //   accessorKey: 'description',
    //   header: t.llmModelsPage.description,
    //   cell: ({ row }) => row.original.description || t.llmModelsPage.noData,
    //   enableHiding: true,
    // },
  ]

  return (
    <DataTable
      columns={columns}
      data={models}
      options={{
        initialState: {
          pagination: {
            pageSize: 50,
          },
        },
      }}
      filterColumn='id'
      filterPlaceholder={t.llmModelsPage.searchModels}
    />
  )
}
