import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { config } from '@/data/config'
import { IconHome } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { modelProviderMap } from '@/lib/ai/model-meta'
import { customModels } from '@/lib/ai/models'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'
import { getModelInfo } from '@/utils/getModelInfo'
import { findModelBySlug, generateModelSlug } from '@/utils/model-utils'

import { ModelIcon } from '@/components/model-icon'
import { Badge } from '@/components/ui/badge'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'

interface PageProps {
  params: Promise<{
    model: string
    lang: Locale
  }>
}

export async function generateMetadata(props: PageProps): Promise<Metadata> {
  const params = await props.params;
  const model = findModelBySlug(params.model, customModels)
  const dictionary = await getDictionary(params.lang)

  if (!model) {
    return {
      title: 'Model Not Found',
    }
  }

  const { modelNameUI } = getModelInfo(model)

  return {
    title: `${modelNameUI}${config.siteTitleSplitter}${dictionary.nav.llmModels.title}`,
    description: model.description || `Detailed information about ${modelNameUI} AI model`,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/llm-models/${params.model}`),
  }
}

export async function generateStaticParams() {
  return customModels
    .filter(model => !model.tags?.includes('hidden'))
    .map(model => ({
      model: generateModelSlug(model.id),
    }))
}

export default async function ModelDetailsPage(props: PageProps) {
  const params = await props.params;
  const model = findModelBySlug(params.model, customModels)
  const t = await getDictionary(params.lang)

  if (!model) {
    notFound()
  }

  const { modelProvider, modelNameUI } = getModelInfo(model)
  const providerInfo = modelProviderMap[modelProvider] || { name: modelProvider, logo: '' }

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <Breadcrumb className='mb-6'>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/${params.lang}`} className='flex items-center gap-1'>
                <IconHome className='h-4 w-4' />
                {t.nav.home.title}
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href={`/${params.lang}/llm-models`}>{t.nav.llmModels.title}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{modelNameUI}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      {/* Model Icon and Title */}
      <div className='mb-8 text-center'>
        <div className='mx-auto mb-6 flex justify-center'>
          <ModelIcon model={modelNameUI} size={128} />
        </div>
        <h1 className='font-logo text-4xl font-bold'>{modelNameUI}</h1>
        <p className='text-muted-foreground mt-2 text-lg'>{providerInfo.name.replace('LAPLACE', 'Xiaosuan')}</p>
        {model.description && <p className='text-muted-foreground mx-auto mt-4 max-w-2xl'>{model.description}</p>}
      </div>
      <Separator className='my-8' />
      {/* Model Information */}
      <div className='space-y-6'>
        {/* Basic Information */}
        <div>
          <h2 className='mb-4 text-2xl font-semibold'>Basic Information</h2>
          <div className='space-y-4'>
            <div className='font-logo grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
              <div>
                <div className='text-sm font-normal'>Model ID</div>
                <div className='text-lg break-all'>{model.id}</div>
              </div>
              <div>
                <div className='text-sm font-normal'>Owner</div>
                <div>{model.owned_by || 'N/A'}</div>
              </div>
            </div>

            {model.tags && model.tags.length > 0 && (
              <div className='font-logo text-2xl font-semibold'>
                <div className='text-sm font-normal'>Tags</div>
                <div className='mt-2 flex flex-wrap gap-2'>
                  {model.tags.map((tag: string) => (
                    <Badge
                      key={tag}
                      variant='solid'
                      tint={
                        tag === 'pro'
                          ? 'violet'
                          : tag === 'enterprise'
                            ? 'amber'
                            : tag === 'deprecated'
                              ? 'red'
                              : 'default'
                      }
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator className='my-6' />

        {/* Pricing Information */}
        {model.pricing && (
          <>
            <div>
              <h2 className='mb-4 text-2xl font-semibold'>Pricing</h2>
              <div className='font-logo grid grid-cols-1 gap-4 text-2xl font-semibold md:grid-cols-2'>
                <div>
                  <div className='text-sm font-normal'>Input Price</div>
                  <div>${model.pricing.inputCostPerMil} / 1M tokens</div>
                </div>
                <div>
                  <div className='text-sm font-normal'>Output Price</div>
                  <div>${model.pricing.outputCostPerMil} / 1M tokens</div>
                </div>
              </div>
            </div>
            <Separator className='my-6' />
          </>
        )}

        {/* Parameters */}
        {model.parameters && (
          <>
            <div>
              <h2 className='mb-4 text-2xl font-semibold'>Parameters</h2>
              <div className='space-y-4'>
                {model.parameters.maximumLength && (
                  <div className='font-logo text-2xl font-semibold'>
                    <div className='text-sm font-normal'>Context Length</div>
                    <div>{model.parameters.maximumLength.value.toLocaleString()} tokens</div>
                  </div>
                )}
              </div>
            </div>
            <Separator className='my-6' />
          </>
        )}

        {/* Features */}
        {model.features && Object.keys(model.features).length > 0 && (
          <>
            <div>
              <h2 className='mb-4 text-2xl font-semibold'>Features</h2>
              <div className='flex flex-wrap gap-2'>
                {Object.entries(model.features)
                  .filter(([, value]) => !!value)
                  .map(([key]) => (
                    <Badge key={key} variant='solid' tint='emerald' size='lg'>
                      {key.replace(/_/g, ' ')}
                    </Badge>
                  ))}
              </div>
            </div>
            <Separator className='my-6' />
          </>
        )}

        {/* Links */}
        {model.links && Object.keys(model.links).length > 0 && (
          <div>
            <h2 className='mb-4 text-2xl font-semibold'>Links</h2>
            <div className='flex flex-wrap gap-4'>
              {model.links.website && (
                <a
                  href={model.links.website}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-500 hover:underline'
                >
                  Website
                </a>
              )}
              {model.links.pricingUrl && (
                <a
                  href={model.links.pricingUrl}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-500 hover:underline'
                >
                  Pricing Page
                </a>
              )}
              {model.links.modelUrl && (
                <a
                  href={model.links.modelUrl}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-500 hover:underline'
                >
                  Documentation
                </a>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
