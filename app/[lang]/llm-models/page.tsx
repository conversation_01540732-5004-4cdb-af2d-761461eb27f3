import type { Locale } from '@/types'

import { customModels } from '@/lib/ai/models'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { ModelsTable } from './components/ModelsTable'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.llmModels.title,
    description: dictionary.nav.llmModels.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/llm-models`),
  }
}

export const runtime = 'edge'

export default async function Page(props: { params: Promise<{ lang: Locale }> }) {
  const params = await props.params;

  const {
    lang
  } = params;

  const t = await getDictionary(lang)

  return (
    <div>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{t.nav.llmModels.title}</h1>
        <div className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.llmModels.desc}</div>
      </div>

      <ModelsTable initialModels={customModels} />
    </div>
  )
}
