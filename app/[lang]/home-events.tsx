'use client'

import Link from 'next/link'

import type { HyperApiPostEvent } from '@/types'

import { WP_FIELDS_EVENT_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import EventItem from '@/components/event-item'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

export default function HomeEvents() {
  const { t } = useTranslation()

  const { data: dataEvents, isLoading } = useRecentPosts<HyperApiPostEvent[]>({
    type: 'events',
    page: 1,
    per_page: 6,
    _embed: true,
    _fields: WP_FIELDS_EVENT_LIST,
  })

  return (
    <div className='space-y-3'>
      <div className='relative grid grid-cols-2 gap-3 sm:grid-cols-3'>
        {dataEvents?.json?.map((post, idx) => <EventItem data={post} key={idx} />)}

        {isLoading && [...new Array(6)].map((_, idx) => <Skeleton key={idx} className='h-[265px] rounded-lg' />)}
      </div>

      <Button size='lg' asChild>
        <Link href='/events'>{t.common.moreLinks.events}</Link>
      </Button>
    </div>
  )
}
