'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { IconArrowRight } from '@tabler/icons-react'

import type { Locale } from '@/types'
import type { components } from '@/types/scraper'

import { fetchLatestPapers } from '@/lib/scraper-api'

import { useTranslation } from '@/utils/i18n'

import Timestamp from '@/components/timestamp'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

type PaperBase = components['schemas']['PaperBase']

export default function HomePapers() {
  const params = useParams<{ lang: Locale }>()
  const lang = params.lang || 'cn'
  const [papers, setPapers] = useState<PaperBase[]>([])
  const [loading, setLoading] = useState(true)

  const { t } = useTranslation()

  useEffect(() => {
    const loadPapers = async () => {
      try {
        setLoading(true)
        const response = await fetchLatestPapers({ locale: lang })
        if (response?.data) {
          // Get only the first 3 papers for the homepage
          setPapers(response.data.slice(0, 6))
        }
      } catch (error) {
        console.error('Failed to fetch papers:', error)
      } finally {
        setLoading(false)
      }
    }

    loadPapers()
  }, [lang])

  return (
    <div className='space-y-3'>
      {loading ? (
        // Loading skeleton
        <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
          {[1, 2, 3].map(i => (
            <Card key={i} className='overflow-hidden'>
              <div className='h-48 w-full'>
                <Skeleton className='h-full w-full' />
              </div>
              <CardHeader className='pb-2'>
                <Skeleton className='h-6 w-3/4' />
                <Skeleton className='h-4 w-1/2' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-8 w-20' />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : papers.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>{t.sota.papers.noData}</CardTitle>
            <CardDescription>{t.sota.papers.noAvailableData}</CardDescription>
          </CardHeader>
          <CardFooter>
            <Link href={`/${lang}/papers`} className='ml-auto flex items-center'>
              <IconArrowRight className='h-4 w-4' />
            </Link>
          </CardFooter>
        </Card>
      ) : (
        <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
          {papers.map(paper => (
            <Link href={`/${lang}/papers/${paper.paper_id}`} key={paper.paper_id}>
              <Card className='h-full transition-shadow hover:shadow-xl'>
                {paper.img_url && (
                  <div className='relative h-48 w-full overflow-hidden rounded-t-lg'>
                    <picture>
                      <img
                        src={paper.img_url}
                        alt={paper.title}
                        sizes='(max-width: 768px) 100vw, 33vw'
                        className='h-full w-full object-cover object-top'
                        loading='lazy'
                      />
                    </picture>
                  </div>
                )}
                <CardHeader className='pb-2'>
                  <CardTitle className='line-clamp-2 text-lg'>{paper.title}</CardTitle>
                  <CardDescription>
                    {paper.authors.slice(0, 2).join(', ')}
                    {paper.authors.length > 2 && ', et al.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='text-fg/60 flex text-sm'>
                    <Timestamp date={paper.created_timestamp} />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
      <Button size='lg' asChild>
        <Link href='/papers'>{t.common.moreLinks.papers}</Link>
      </Button>
    </div>
  )
}
