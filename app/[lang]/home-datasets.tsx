'use client'

import Link from 'next/link'

import { WP_FIELDS_DATASET_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import DatasetItem from '@/components/dataset-item'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

export default function HomeDatasets() {
  const { t } = useTranslation()

  const { data: dataDatasets, isLoading } = useRecentPosts({
    type: 'datasets',
    page: 1,
    per_page: 6,
    _embed: true,
    _fields: WP_FIELDS_DATASET_LIST,
  })

  return (
    <div className='space-y-3'>
      <div className='relative grid grid-cols-1 gap-3 sm:grid-cols-2'>
        {dataDatasets?.json?.map((post, idx) => <DatasetItem data={post} key={idx} />)}

        {isLoading && [...new Array(6)].map((_, idx) => <Skeleton key={idx} className='h-[75px] rounded-lg' />)}
      </div>

      <Button size='lg' asChild>
        <Link href='/datasets'>{t.common.moreLinks.datasets}</Link>
      </Button>
    </div>
  )
}
