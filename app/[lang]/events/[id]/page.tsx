import { config } from '@/data/config'
import { IconExternalLink } from '@tabler/icons-react'

import type { HyperApiPostEvent, Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'
import nf from '@/utils/numberFormat'

import Category from '@/components/category'
import { EventTimeline } from '@/components/event-timeline'
import EventTimestamp from '@/components/event-timestamp'
import { FeaturedMedia, getFeaturedMediaUrl } from '@/components/featured-media'
import Filelist from '@/components/filelist'
import Location from '@/components/location'
import MapContainer from '@/components/MapContainer'
import PageBody from '@/components/page-body'
import RelatedPosts from '@/components/related-posts'
import Sidebar from '@/components/sidebar'
import Tag from '@/components/tag'
import { Button } from '@/components/ui/button'

type Params = Promise<{ lang: Locale; id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById<HyperApiPostEvent>(Number(params.id), {
    type: 'events',
    locale: params.lang,
  })
  const dictionary = await getDictionary(params.lang)

  return {
    title: `${encodeTitle(data.title.rendered)}${config.siteTitleSplitter}${dictionary.nav.events.title}`,
    description: elementToString(data.excerpt.rendered),
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/events/${params.id}`),
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById<HyperApiPostEvent>(Number(params.id), {
    type: 'events',
    locale: params.lang,
  })
  const t = await getDictionary(params.lang)

  const featuredImage =
    data._embedded &&
    data._embedded['wp:featuredmedia'] &&
    getFeaturedMediaUrl(data._embedded['wp:featuredmedia'][0], true)

  return (
    <div>
      <div className='mb-4'>
        <h1 className='mb-4'>{encodeTitle(data.title.rendered)}</h1>

        <div className='flex flex-wrap justify-between gap-4'>
          <div className='space-y-1'>
            <p className='text-fg/60 text-sm'>{t.events.metadata.date}</p>
            <div>
              {/* `date_gmt` can be empty in some cases, ie. YARPP rest output */}
              <EventTimestamp event={data} />
            </div>
          </div>

          {data.acf.venue && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.events.metadata.venue}</p>
              <Location venue={data.acf.venue} />
            </div>
          )}

          {data.acf.website && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.events.metadata.website}</p>
              <p>
                <a className='text-ac hover:underline' href={data.acf.website} target='_blank'>
                  {new URL(data.acf.website).hostname}
                </a>
              </p>
            </div>
          )}

          {data.acf.h5_index && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.events.metadata.h5Index}</p>
              <p>{data.acf.h5_index}</p>
            </div>
          )}

          {data.acf.ccf_level && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.events.metadata.ccfLevel}</p>
              <p>{data.acf.ccf_level}</p>
            </div>
          )}

          {data.hyperai_tags && data.hyperai_tags.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.tutorials.metadata.tags}</p>
              <div className='flex flex-wrap items-center gap-2'>
                {data.hyperai_tags.map((tag, idx) => {
                  return <Tag key={idx} data={tag} enableLink={'tags'} />
                })}
              </div>
            </div>
          )}

          {data.hyperai_categories && data.hyperai_categories.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{t.datasets.metadata.categories}</p>
              <div className='flex gap-2'>
                {data.hyperai_categories.map((cate, idx) => {
                  return <Category key={idx} data={cate} enableLink={'areas'} />
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-2/3'>
          <div className='space-y-6'>
            {data.acf.website && (
              <div className='flex items-center gap-2'>
                <Button size='lg' variant='outline' asChild className='gap-2'>
                  <a href={data.acf.website} target='_blank'>
                    <IconExternalLink className='size-4' />
                    {t.events.visitWebsite}
                  </a>
                </Button>
              </div>
            )}

            {featuredImage && <FeaturedMedia media={featuredImage} autoHeight />}

            <PageBody content={data.content.rendered} className='prose max-w-none' />

            {typeof data.acf_extended?.torrent_stats?.name === 'string' && (
              <div className='space-y-3'>
                <div className='flex flex-wrap justify-between gap-3'>
                  <div className='font-bold'>{data.acf_extended.torrent_stats.name}.torrent</div>
                  <div className='flex flex-wrap gap-2'>
                    <span className='text-blue-500'>
                      {t.datasets.stats.seeding} {nf.format(data.acf_extended.torrent_stats.seeders)}
                    </span>
                    <span className='text-rose-500'>
                      {t.datasets.stats.downloading} {nf.format(data.acf_extended.torrent_stats.leechers)}
                    </span>
                    <span className='text-emerald-500'>
                      {t.datasets.stats.completed} {nf.format(data.acf_extended.torrent_stats.completed)}
                    </span>
                    <span className='text-fg/60'>
                      {t.datasets.stats.totalDownloads} {nf.format(data.acf_extended.torrent_stats.hits)}
                    </span>
                  </div>
                </div>
                <Filelist data={JSON.parse(data.acf_extended.torrent_stats.files)} />
              </div>
            )}

            <RelatedPosts postId={Number(params.id)} type='events' name={t.common.contentTypes.events} />
          </div>
        </div>

        <div className='md:w-1/3'>
          <div className='space-y-6'>
            <EventTimeline event={data} />

            {data.acf.venue?.lat && data.acf.venue?.lng ? (
              <MapContainer lat={data.acf.venue.lat} lng={data.acf.venue.lng} />
            ) : null}

            <Sidebar />
          </div>
        </div>
      </div>
    </div>
  )
}
