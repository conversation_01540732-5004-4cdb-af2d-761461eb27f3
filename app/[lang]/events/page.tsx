import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import EventList from './event-list'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.events.title,
    description: dictionary.nav.events.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/events`),
  }
}

export default function EventsPage() {
  return (
    <div>
      <EventList />
    </div>
  )
}
