import type { MetadataRoute } from 'next'

import { calculateSitemapIndex } from '@/lib/sitemap/calculateSitemapIndex'
import { generateSitemapItems } from '@/lib/sitemap/generateSitemapItems'

// This disables SSG
export const runtime = 'edge'

export async function generateSitemaps() {
  return await calculateSitemapIndex('events')
}

export default async function sitemap({ id }: { id: number }): Promise<MetadataRoute.Sitemap> {
  return await generateSitemapItems('events', id)
}
