'use client'

import { useTranslation } from '@/utils/i18n'

import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'

interface EventControlsProps {
  orderBy: string
  setOrderBy: (value: string) => void
  reverseOrder: boolean
  setReverseOrder: (value: boolean) => void
}

export default function EventControls({ orderBy, setOrderBy, reverseOrder, setReverseOrder }: EventControlsProps) {
  const { t } = useTranslation()

  return (
    <div className='mb-4'>
      <RadioGroup value={orderBy} onValueChange={setOrderBy} className='flex items-center gap-4'>
        <div className='flex items-center gap-2'>
          <RadioGroupItem value='end_date' id='end_date' />
          <Label htmlFor='end_date'>{t.events.controls.deadline}</Label>
        </div>

        <div className='flex items-center gap-2'>
          <RadioGroupItem value='publish_date' id='publish_date' />
          <Label htmlFor='publish_date'>{t.events.controls.results}</Label>
        </div>

        <div className='flex items-center gap-2'>
          <RadioGroupItem value='start_date' id='start_date' />
          <Label htmlFor='start_date'>{t.events.controls.eventDate}</Label>
        </div>

        <Separator orientation='vertical' className='h-6' />

        <div className='flex items-center gap-2'>
          <Checkbox
            id='reverse-order'
            checked={reverseOrder}
            onChange={event => setReverseOrder(event.target.checked)}
          />
          <Label htmlFor='reverse-order'>{t.events.controls.reverseOrder}</Label>
        </div>
      </RadioGroup>
    </div>
  )
}
