import Link from 'next/link'
import { IconSearch } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

// import { getRecentPosts } from '@/lib/api'

import Sidebar from '@/components/sidebar'
import { BackgroundBoxes } from '@/components/ui/background-boxes'
import { Button } from '@/components/ui/button'

import HomeDatasets from './home-datasets'
import HomeEvents from './home-events'
import HomeNews from './home-news'
import HomePapers from './home-papers'
import HomeTutorials from './home-tutorials'
import HomeWiki from './home-wiki'

type Params = Promise<{ lang: Locale; id: string }>

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.common.title,
    description: dictionary.common.slogan,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/`),
  }
}

// Server side pre-fetching for hero sliders
// async function getHeroSliders() {
//   const data = await getRecentPosts({
//     type: 'hero-sliders',
//   })
//   return data
// }

export default async function HomePage(props: { params: Promise<{ lang: Locale }> }) {
  const params = await props.params;

  const {
    lang
  } = params;

  const t = await getDictionary(lang)

  return (
    <div>
      <div className='flex flex-col gap-8 lg:flex-row'>
        <div className='lg:w-2/3'>
          <div className='flex flex-col gap-4'>
            {/* Hero Section */}
            <div className='bg-ac/10 relative overflow-hidden rounded-lg px-5 py-9 md:px-10 md:py-18'>
              <BackgroundBoxes />
              <div className='pointer-events-none relative z-20 space-y-2'>
                <h1 className='font-logo text-3xl text-balance md:text-5xl'>{t.common.slogan}</h1>
                {/* <p className='text-lg text-fg/60'>最新的人工智能模型性能比较、GPU基准测试以及最新前沿论文</p> */}
              </div>
              <div className='relative mt-4 inline-flex gap-2'>
                <Button
                  size='lg'
                  asChild
                  variant='solid'
                  className='font-bold text-balance whitespace-normal'
                  leftSection={<IconSearch />}
                >
                  <Link href='/search'>{t.common.heroButton}</Link>
                </Button>
              </div>
            </div>

            {/* News Section */}
            <h2 className='text-3xl'>{t.nav.news.title}</h2>
            <HomeNews />

            {/* Papers Section */}
            <div className='space-y-2'>
              <h2 className='mt-6 text-3xl'>{t.sota.papers.title}</h2>
              <p className='text-fg/60 max-w-160 text-lg text-balance'>
                {t.sota.papers.description}
              </p>
            </div>
            <HomePapers />

            {/* Tutorials Section */}
            <div className='space-y-2'>
              <h2 className='mt-6 text-3xl'>{t.nav.tutorials.title}</h2>
              <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.tutorials.longDesc}</p>
            </div>
            <HomeTutorials />

            {/* Datasets Section */}
            <div className='space-y-2'>
              <h2 className='mt-6 text-3xl'>{t.nav.datasets.title}</h2>
              <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.datasets.longDesc}</p>
            </div>
            <HomeDatasets />

            {/* Events Section */}
            <div className='space-y-2'>
              <h2 className='mt-6 text-3xl'>{t.nav.events.title}</h2>
              <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.events.longDesc}</p>
            </div>
            <HomeEvents />

            {/* Wiki Section */}
            <div className='space-y-2'>
              <h2 className='mt-6 text-3xl'>{t.nav.wiki.title}</h2>
              <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.wiki.longDesc}</p>
            </div>
            <HomeWiki />
          </div>
        </div>

        <div className='lg:w-1/3'>
          <Sidebar />
        </div>
      </div>
    </div>
  )
}
