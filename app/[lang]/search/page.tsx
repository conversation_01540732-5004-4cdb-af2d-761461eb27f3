import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import { SearchComponents } from './components-algolia'

// Make this page dynamic to ensure we get fresh search results on each request
export const dynamic = 'force-dynamic'

export async function generateMetadata(props: { params: Promise<{ lang: Locale }> }) {
  const params = await props.params;
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.search.title,
    description: dictionary.nav.search.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/search`),
  }
}

export default function SearchPage() {
  return (
    <div>
      <SearchComponents />
    </div>
  )
}
