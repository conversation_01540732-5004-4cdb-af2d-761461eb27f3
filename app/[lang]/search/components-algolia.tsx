'use client'

import { useEffect, useState } from 'react'
import { liteClient as algoliasearch } from 'algoliasearch/lite'
import { InstantSearch, SearchBox } from 'react-instantsearch'

import { ALGOLIA_PROXY_BASE } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'

import { AlgoliaHierarchicalMenu, AlgoliaHits, AlgoliaPagination, AlgoliaRefinementList } from '@/components/algolia'

// Initialize Algolia search client
const searchClient = algoliasearch('STWAK05FUV', '********************************', {
  hosts: [
    {
      url: ALGOLIA_PROXY_BASE,
      accept: 'read',
      protocol: 'https',
    },
  ],
})

export function SearchComponents() {
  const [mounted, setMounted] = useState(false)
  const { t } = useTranslation()

  // Wait for client-side hydration to complete
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div>Loading...</div>
  }

  return (
    <InstantSearch
      indexName='wp_searchable_posts'
      searchClient={searchClient}
      future={{
        preserveSharedStateOnUnmount: true,
      }}
      // routing
    >
      <div className='grid grid-cols-1 gap-6 md:grid-cols-24'>
        <div className='md:col-span-7'>
          <SearchBox className='searchBox searchPage rounded-md border' placeholder={t.command.label} />

          <AlgoliaHierarchicalMenu title={t.search.contentType} attributes={['post_type_label']} />

          <AlgoliaHierarchicalMenu title={t.search.categories} attributes={['taxonomies_hierarchical.category.lvl0']} />

          <AlgoliaRefinementList title={t.search.tags} attribute='taxonomies.post_tag' limit={30} />

          <AlgoliaRefinementList
            title={t.search.datasetCategories}
            attribute='taxonomies.datasets-category'
            limit={30}
          />

          <AlgoliaRefinementList title={t.search.organizations} attribute='taxonomies.organization' limit={30} />
        </div>

        <div className='md:col-span-17'>
          <AlgoliaHits />

          <AlgoliaPagination />
        </div>
      </div>
    </InstantSearch>
  )
}
