import '@/styles/globals.css'
import '@/styles/algolia.css'

import React from 'react'
import PlausibleProvider from 'next-plausible'
import Script from 'next/script'
// import { Space_Grotesk } from 'next/font/google'
// import localFont from 'next/font/local'
import { GoogleAnalytics } from '@next/third-parties/google'

import type { Locale } from '@/types'

import { WebVitals } from '@/lib/axiom/client'
import { getDictionary } from '@/lib/dictionaries-server'

import { baseUrl, createMetadata } from '@/utils/metadata'

import SidebarLayout from '@/components/sidebar-layout'

import MyStatsig from '../my-statsig'
import Providers from './providers'

// const space_grotesk = Space_Grotesk({
//   subsets: ['latin'],
//   display: 'swap',
//   variable: '--font-space-grotesk',
// })

// const Syne = localFont({
//   src: [
//     // {
//     //   path: '../../fonts/Syne-Regular.ttf',
//     //   weight: '400',
//     //   style: 'normal',
//     // },
//     {
//       path: '../../fonts/Syne-ExtraBold.ttf',
//       weight: '700',
//       style: 'bold',
//     },
//   ],
//   variable: '--font-syne',
// })

// last resort fallback
// Generate metadata dynamically based on language
export async function generateMetadata({ params }: { params: Promise<{ lang: Locale }> }) {
  const { lang } = await params
  const dictionary = await getDictionary(lang)

  return createMetadata(
    {
      title: {
        template: `%s | ${dictionary.common.title}`,
        default: dictionary.common.title,
      },
      description: dictionary.common.slogan,
      metadataBase: baseUrl,
    },
    dictionary
  )
}

export default async function RootLayout(props: { children: React.ReactNode; params: Promise<{ lang: Locale }> }) {
  const params = await props.params

  const { lang } = params

  const {
    // Layouts must accept a children prop.
    // This will be populated with nested layouts or pages
    children,
  } = props

  const dictionary = await getDictionary(lang)

  return (
    <html
      // TODO: no solution atm: https://github.com/vercel/next.js/discussions/49415
      // lang='en'
      lang={lang}
      dir={lang === 'ar' ? 'rtl' : 'ltr'}
      // className={`${Syne.variable} ${space_grotesk.variable} overflow-hidden`}
      className={'overflow-hidden'}
    >
      <WebVitals />
      <head>
        <link rel='shortcut icon' href='/favicon.svg' />
        <meta name='viewport' content='minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no' />
        <PlausibleProvider
          domain='hyper.ai'
          customDomain='https://get.openbayes.net'
          selfHosted={true}
          trackOutboundLinks={true}
          trackFileDownloads={true}
          trackLocalhost={true}
          enabled={true}
        />
        <GoogleAnalytics gaId='G-YY2E0ZQRP8' />
      </head>
      <body>
        <MyStatsig>
          <Providers lang={lang} dictionary={dictionary}>
            <SidebarLayout lang={lang}>{children}</SidebarLayout>
          </Providers>
        </MyStatsig>
        <Script
          src='https://app.rybbit.io/api/script.js'
          data-site-id='1233'
          data-web-vitals='true'
          data-session-replay='true'
          data-tracking-errors='true'
          strategy='afterInteractive'
        />
      </body>
    </html>
  )
}
