import Link from 'next/link'
import { config } from '@/data/config'
import { IconDownload, IconHelpCircle, IconMagnet } from '@tabler/icons-react'

import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'
import nf from '@/utils/numberFormat'

import Category from '@/components/category'
import { FeaturedMedia, getFeaturedMediaUrl } from '@/components/featured-media'
import Filelist from '@/components/filelist'
import Organization from '@/components/organization'
import PageBody from '@/components/page-body'
import RelatedPosts from '@/components/related-posts'
import Sidebar from '@/components/sidebar'
import Tag from '@/components/tag'
import Timestamp from '@/components/timestamp'
import { Button } from '@/components/ui/button'

type Params = Promise<{ lang: Locale; id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'datasets', locale: params.lang })
  const dictionary = await getDictionary(params.lang)

  return {
    title: `${encodeTitle(data.title.rendered)}${config.siteTitleSplitter}${dictionary.nav.datasets.title}`,
    description: elementToString(data.excerpt.rendered),
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/datasets/${params.id}`),
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'datasets', locale: params.lang })
  const dictionary = await getDictionary(params.lang)

  const featuredImage =
    data._embedded &&
    data._embedded['wp:featuredmedia'] &&
    getFeaturedMediaUrl(data._embedded['wp:featuredmedia'][0], true)

  return (
    <div>
      <div className='mb-4'>
        <h1 className='mb-4'>{encodeTitle(data.title.rendered)}</h1>

        <div className='flex flex-wrap justify-between gap-4'>
          <div className='space-y-1'>
            <p className='text-fg/60 text-sm'>{dictionary.tutorials.metadata.date}</p>
            <div>
              {/* `date_gmt` can be empty in some cases, ie. YARPP rest output */}
              <Timestamp date={data.date_gmt || data.date} modified={data.modified_gmt} />
            </div>
          </div>

          {data.acf?.related_torrents && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.tutorials.metadata.size}</p>
              <div>{data.acf_extended?.torrent_size}</div>
            </div>
          )}

          {data.hyperai_organizations && data.hyperai_organizations.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.datasets.metadata.organization}</p>
              <div className='flex gap-2'>
                {data.hyperai_organizations.map((org, idx) => {
                  return <Organization key={idx} data={org} enableLink />
                })}
              </div>
            </div>
          )}

          {data.acf?.publish_url && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.datasets.metadata.publishUrl}</p>
              <p>
                <a className='text-ac hover:underline' href={data.acf.publish_url} target='_blank'>
                  {new URL(data.acf.publish_url).hostname}
                </a>
              </p>
            </div>
          )}

          {data.acf?.license && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.datasets.metadata.license}</p>
              <p>{data.acf.license.label}</p>
            </div>
          )}

          {data.hyperai_tags && data.hyperai_tags.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.tutorials.metadata.tags}</p>
              <div className='flex flex-wrap items-center gap-2'>
                {data.hyperai_tags.map((tag, idx) => {
                  return <Tag key={idx} data={tag} enableLink={'tags'} />
                })}
              </div>
            </div>
          )}

          {data.hyperai_categories && data.hyperai_categories.length > 0 && (
            <div className='space-y-1'>
              <p className='text-fg/60 text-sm'>{dictionary.datasets.metadata.categories}</p>
              <div className='flex gap-2'>
                {data.hyperai_categories.map((cate, idx) => {
                  return <Category key={idx} data={cate} enableLink={'areas'} />
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className='flex flex-col gap-8 md:flex-row'>
        <div className='md:w-2/3'>
          <div className='space-y-6'>
            <div className='flex flex-wrap items-center gap-2'>
              {data.acf?.related_torrents ? (
                <>
                  <Button size='lg' tint='accent' asChild className='gap-2'>
                    <a href={`https://orion.hyper.ai/tracker/download?torrent=${data.acf.related_torrents}`}>
                      <IconDownload className='size-4' />
                      {dictionary.datasets.download}
                    </a>
                  </Button>

                  <Button size='lg' variant='outline' asChild className='gap-2'>
                    <a href={`https://orion.hyper.ai/tracker/magnet?torrent=${data.acf.related_torrents}`}>
                      <IconMagnet className='size-4' />
                      {dictionary.datasets.magnetLink}
                    </a>
                  </Button>
                </>
              ) : (
                <Button size='lg' disabled>
                  {dictionary.datasets.noDownload}
                </Button>
              )}

              <Button size='lg' variant='outline' asChild>
                <Link href={'/datasets/about'}>
                  <IconHelpCircle className='size-4' />
                  {dictionary.datasets.downloadHelp}
                </Link>
              </Button>
            </div>

            {data && data?._embedded && data?._embedded['wp:featuredmedia'] && (
              <FeaturedMedia media={featuredImage} autoHeight />
            )}

            <PageBody content={data.content.rendered} className='prose max-w-none' />

            {typeof data.acf_extended?.torrent_stats?.name === 'string' && (
              <div className='space-y-3'>
                <div className='flex flex-wrap justify-between gap-3'>
                  <div className='font-bold'>{data.acf_extended.torrent_stats.name}.torrent</div>
                  <div className='flex flex-wrap gap-2'>
                    <span className='text-blue-500'>
                      {dictionary.datasets.stats.seeding} {nf.format(data.acf_extended.torrent_stats.seeders)}
                    </span>
                    <span className='text-rose-500'>
                      {dictionary.datasets.stats.downloading} {nf.format(data.acf_extended.torrent_stats.leechers)}
                    </span>
                    <span className='text-emerald-500'>
                      {dictionary.datasets.stats.completed} {nf.format(data.acf_extended.torrent_stats.completed)}
                    </span>
                    <span className='text-fg/60'>
                      {dictionary.datasets.stats.totalDownloads} {nf.format(data.acf_extended.torrent_stats.hits)}
                    </span>
                  </div>
                </div>
                <Filelist data={JSON.parse(data.acf_extended.torrent_stats.files)} />
              </div>
            )}

            <RelatedPosts postId={Number(params.id)} type='datasets' name={dictionary.common.contentTypes.datasets} />
          </div>
        </div>

        <div className='md:w-1/3'>
          <div className='space-y-6'>
            <Sidebar />
          </div>
        </div>
      </div>
    </div>
  )
}
