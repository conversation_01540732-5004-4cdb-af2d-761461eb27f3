'use client'

import { IconHelpCircle } from '@tabler/icons-react'

import { useTranslation } from '@/utils/i18n'
import nf from '@/utils/numberFormat'
import useTorrentStats from '@/utils/useTorrentStats'

import { Skeleton } from '@/components/ui/skeleton'
import { Tooltip } from '@/components/ui/tooltip'

export default function DatasetStats() {
  const { t } = useTranslation()
  const { data: dataStats, isLoading: isLoadingStats } = useTorrentStats()

  return (
    <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
      <div className='space-y-0'>
        {dataStats && !isLoadingStats && (
          <p className='font-title text-4xl font-bold'>{nf.format(dataStats.seeding)}</p>
        )}
        {!dataStats && isLoadingStats && <Skeleton className='h-10 w-[4ch]' />}
        <div className='text-fg/60 flex items-center gap-1'>
          <span>{t.datasets.stats.seeding}</span>
          <Tooltip label={t.datasets.stats.seedingTooltip}>
            <IconHelpCircle size={20} />
          </Tooltip>
        </div>
      </div>

      <div className='space-y-0'>
        {dataStats && !isLoadingStats && (
          <p className='font-title text-4xl font-bold'>{nf.format(dataStats.downloading)}</p>
        )}
        {!dataStats && isLoadingStats && <Skeleton className='h-10 w-[3ch]' />}
        <div className='text-fg/60 flex items-center gap-1'>
          <span>{t.datasets.stats.downloading}</span>
          <Tooltip label={t.datasets.stats.downloadingTooltip}>
            <IconHelpCircle size={20} />
          </Tooltip>
        </div>
      </div>

      <div className='space-y-0'>
        {dataStats && !isLoadingStats && (
          <p className='font-title text-4xl font-bold'>{nf.format(dataStats.completed)}</p>
        )}
        {!dataStats && isLoadingStats && <Skeleton className='h-10 w-[6ch]' />}
        <div className='text-fg/60 flex items-center gap-1'>
          <span>{t.datasets.stats.completed}</span>
          <Tooltip label={t.datasets.stats.completedTooltip}>
            <IconHelpCircle size={20} />
          </Tooltip>
        </div>
      </div>

      <div className='space-y-0'>
        {dataStats && !isLoadingStats && <p className='font-title text-4xl font-bold'>{dataStats.traffic}</p>}
        {!dataStats && isLoadingStats && <Skeleton className='h-10 w-[8ch]' />}
        <span className='text-fg/60'>{t.datasets.stats.traffic}</span>
      </div>
    </div>
  )
}
