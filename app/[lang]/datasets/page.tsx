import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import DatasetList from './dataset-list'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.datasets.title,
    description: dictionary.nav.datasets.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/datasets`),
  }
}

export default function DatasetsPage() {
  return (
    <div>
      <DatasetList />
    </div>
  )
}
