'use client'

import { useEffect, useState } from 'react'

import type { HyperApiPost } from '@/types'

import { WP_FIELDS_DATASET_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'
import useTags from '@/utils/useTags'

import DatasetItem from '@/components/dataset-item'
import Tag from '@/components/tag'
import { Loading } from '@/components/ui/loading'
import { Pagination } from '@/components/ui/pagination'
import { Skeleton } from '@/components/ui/skeleton'

import DatasetStats from './dataset-stats'

export default function DatasetList() {
  const [posts, setPosts] = useState<HyperApiPost[]>([])
  const [pageIndex, setPageIndex] = useState(1)
  const [pageTotal, setPageTotal] = useState(0)
  const [currentTag, setCurrentTag] = useState<number | undefined>()

  const { t } = useTranslation()

  const { data, isLoading } = useRecentPosts({
    type: 'datasets',
    page: pageIndex,
    per_page: 30,
    tags: currentTag,
    _embed: true,
    _fields: WP_FIELDS_DATASET_LIST,
  })

  const { data: dataTags, isLoading: isLoadingTags } = useTags({ type: 'datasets' })

  useEffect(() => {
    if (data?.json && data.json.length > 0) {
      setPosts(data.json)
    }

    if (data?.headers && data.headers.get('X-Wp-Totalpages')) {
      setPageTotal(Number(data.headers.get('X-Wp-Totalpages')))
    }
  }, [data])

  function handleTagSelect(tag: number) {
    if (currentTag === tag) {
      setCurrentTag(undefined)
    } else {
      setCurrentTag(tag)
      setPageIndex(1)
    }
  }

  return (
    <>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{t.nav.datasets.title}</h1>
        <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.datasets.longDesc}</p>

        <DatasetStats />
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-24'>
        <div className='lg:col-span-17'>
          <div>
            <div className='relative'>
              {isLoading && (
                <div className='bg-bg/50 absolute top-0 left-0 z-10 flex h-full w-full items-center justify-center'>
                  <Loading />
                </div>
              )}

              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                {posts && posts.length > 0 && posts.map(post => <DatasetItem data={post} key={post.id} />)}
              </div>

              {isLoading && posts.length === 0 && (
                <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
                  {[...new Array(30)].map((_, idx) => (
                    <Skeleton key={idx} className='h-[60px] rounded-md' />
                  ))}
                </div>
              )}
            </div>

            {pageTotal > 0 && (
              <div className='mt-8 flex justify-center'>
                <Pagination value={pageIndex} onPageChange={setPageIndex} total={pageTotal} disabled={isLoading} />
              </div>
            )}
          </div>
        </div>

        <div className='relative lg:col-span-7'>
          <div className='flex flex-wrap gap-2'>
            {!isLoadingTags &&
              dataTags &&
              dataTags.map((tag, idx) => (
                <div key={idx} onClick={() => handleTagSelect(tag.term_id)} className='cursor-pointer'>
                  <Tag data={tag} key={idx} active={currentTag === tag.term_id} />
                </div>
              ))}

            {isLoadingTags &&
              [...new Array(100)].map((_, idx) => {
                const length = 50 + (idx % 10) * 10
                return (
                  <div key={idx}>
                    <Skeleton className='h-7' style={{ width: `${length}px` }} />
                  </div>
                )
              })}
          </div>
        </div>
      </div>
    </>
  )
}
