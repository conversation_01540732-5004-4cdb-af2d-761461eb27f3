import { config } from '@/data/config'

const dictionary = {
  news: {
    label: '资讯',
  },
  time: {
    days: '天',
    hours: 'h',
    minutes: 'm',
    seconds: 's',
    ended: '已结束',
  },
  timeObj: {
    days: '{{count}} 天',
    hours: '{{count}}h',
    minutes: '{{count}}m',
    seconds: '{{count}}s',
  },
  theme: {
    system: '跟随系统',
    dark: '黑夜',
    light: '白天',
  },
  common: {
    title: 'HyperAI超神经',
    slogan: '学习、理解、实践，与社区一起构建人工智能的未来',
    heroButton: '搜索数据集、教程与 AI 案例',
    publishedAt: '发布于',
    moreLinks: {
      news: '查看更多资讯',
      tutorials: '查看更多教程',
      datasets: '查看更多数据集',
      events: '查看更多顶会',
      wiki: '查看更多百科',
      sota: '查看更多 SOTA',
      projects: '查看更多开源项目',
      papers: '查看更多论文',
    },
    relatedContent: '相关{{type}}',
    contentTypes: {
      news: '资讯',
      posts: '文章',
      tutorials: '教程',
      datasets: '数据集',
      papers: '论文',
      events: '顶会',
      wiki: '百科条目',
    },
    previous: '上一页',
    next: '下一页',
    login: '登录',
    logout: '登出',
    accountInfo: '账号信息',
  },
  tutorials: {
    metadata: {
      date: '日期',
      size: '大小',
      author: '作者',
      tags: '标签',
      latestVersion: '最新版本',
    },
    runOnline: '在线运行此教程',
    notebookPreview: 'Notebook 预览',
  },
  datasets: {
    metadata: {
      organization: '机构',
      publishUrl: '发布地址',
      license: '许可协议',
      categories: '分类',
    },
    download: '数据集下载',
    magnetLink: '磁力链',
    noDownload: '暂无可用下载',
    downloadHelp: '下载帮助',
    stats: {
      seeding: '做种',
      seedingTooltip: '最近 1 小时内做种的客户端数量',
      downloading: '正在下载',
      downloadingTooltip: '最近 1 小时内正在下载的客户端数量',
      completed: '已完成',
      completedTooltip: '总共完成的下载次数，每 24 小时更新，只统计下载进度达到 100% 的下载',
      traffic: '贡献流量',
      totalDownloads: '总下载次数',
    },
  },
  events: {
    controls: {
      deadline: '截稿日期',
      results: '结果公布',
      eventDate: '举办日期',
      reverseOrder: '倒序',
    },
    metadata: {
      date: '日期',
      venue: '地点',
      website: '官网',
      h5Index: 'h5 指数',
      ccfLevel: 'CCF 等级',
    },
    timeline: {
      deadline: '截稿',
      deadlineDesc: '会议或活动提交提案或文章的最后期限',
      results: '结果公布',
      resultsDesc: '组织者会通知投稿者他们的提案或文章是否被接受',
      eventStart: '举办会议',
      eventStartDesc: '会议或活动正式举行的时期',
      eventEnd: '会议结束',
      eventEndDesc: '会议或活动的结束时间',
    },
    visitWebsite: '访问官网',
    conference: '会议',
    ongoing: '举办中',
    tbd: '待定',
  },
  sota: {
    pageTitle: 'AI SOTA 基准测试',
    pageDescription: '最新的人工智能模型性能比较、GPU 基准测试以及最新前沿论文',
    categories: {
      title: '类别',
      description: '按类别浏览任务',
      taskCount: '个任务',
    },
    taskNotFound: '任务不存在',
    benchmarksList: '基准测试列表',
    allBenchmarksForTask: '该任务相关的所有基准测试列表',
    noBenchmarkData: '该任务下暂无可用的基准测试数据',
    metrics: '评估指标',
    results: '评测结果',
    resultsDescription: '各个模型在此基准测试上的表现结果',
    comparisonTable: '比较表格',
    modelName: '模型名称',
    noResultsData: '该基准测试暂无详细结果数据',
    taskList: {
      papers: ' 篇论文',
      benchmarks: ' 个基准测试',
      models: ' 个模型',
      noData: '暂无数据',
      noTaskData: '没有可用的任务数据',
      searchPlaceholder: '搜索任务...',
      noResults: '没有找到结果',
      tryDifferentSearch: '尝试不同的搜索词',
    },
    modelBenchmark: {
      title: 'AI 模型性能基准',
      description: '主流 AI 模型在各任务上的性能指标比较，展示最前沿的技术水平',
    },
    gpuBenchmark: {
      title: 'GPU 基准测试',
      description: '最新的 GPU 硬件和软件性能评测，帮助您做出明智的硬件选择',
    },
    gpuBenchmarks: {
      softwarePerformance: '软件性能',
      hardwarePerformance: '硬件性能',
      showChartView: '图表视图',
      showTableView: '表格视图',
      performance: '性能',
      requestThroughput: '请求吞吐量',
      outputTokenThroughput: '输出 token 吞吐量',
      totalTokenThroughput: '总 token 吞吐量',
      noData: '暂无数据',
      noGpuData: '没有可用的 GPU 基准测试数据',
      noHardwareData: '没有可用的 GPU 硬件基准测试数据',
      noSoftwareData: '没有可用的 GPU 软件基准测试数据',
      gpuHardwareBenchmark: 'GPU 硬件基准',
      manufacturer: '制造商',
      unknown: '未知',
      metric: '指标',
      value: '数值',
      model: '模型',
      environment: '环境',
      selectMetric: '选择指标',
      allMetrics: '所有指标',
      maxContextLength: '最大上下文长度',
      maxSequences: '最大序列数',
    },
    benchmarkDetails: {
      viewDetails: '查看详情',
      bestModel: '最佳模型',
      searchPlaceholder: '搜索基准测试...',
      noResults: '没有找到结果',
      tryDifferentSearch: '尝试不同的搜索词',
    },
    papers: {
      title: '最新论文',
      abstract: '摘要',
      description: '每日更新的前沿 AI 研究论文，助您把握人工智能最新动向',
      noData: '暂无数据',
      noAvailableData: '没有可用的论文数据',
      releaseDate: '发布日期',
      retrievalDate: '获取日期',
      viewDetails: '查看论文详情',
    },
  },
  sidebar: {
    newsletter: {
      title: '订阅我们的最新资讯',
      description: '我们会在北京时间',
      time: '每周一的上午九点',
      delivery: '向您的邮箱投递本周内的最新更新',
      emailPlaceholder: '输入邮箱地址',
      submitButton: '订阅',
      provider: '邮件发送服务由 {{provider}} 提供',
    },
    live: {
      loading: '载入直播信息…',
      streaming: '正在直播：',
      offline: 'Hyper.AI 尚未开播',
    },
    openbayes: {
      alt: 'OpenBayes 注册即获得免费算力',
    },
    tvm: {
      alt: 'TVM 中文网',
    },
  },
  command: {
    nav: '导航',
    searchResult: '搜索结果',
    placeholder: '输入关键词进行搜索…',
    label: '全站搜索…',
  },
  footer: {
    about: {
      title: '关于',
      aboutUs: '关于我们',
      datasetHelp: '数据集帮助',
    },
    products: {
      title: '产品',
    },
    links: {
      title: '链接',
    },
  },
  nav: {
    home: {
      title: '首页',
      desc: `访问 ${config.siteTitle} 主页`,
    },
    news: {
      title: '资讯',
      desc: 'AI 相关的最新资讯',
    },
    tutorials: {
      title: '教程',
      desc: '数百个精选的机器学习相关教程',
      longDesc:
        '我们共收集了数百个精选的机器学习相关教程，并整理成 Jupyter 记事本的形式。您可以免费的在我们合作的机器学习平台 OpenBayes 运行这些记事本',
    },
    datasets: {
      title: '数据集',
      desc: '上千个公开数据集资源',
      longDesc: '我们共收集了上千个公开数据集资源，整理并提供相应的存储服务。免费提供给相关从业人员进行下载',
    },
    events: {
      title: '顶会',
      desc: '一站式追踪人工智能学术顶会，不错过任何一个 deadline',
      longDesc: '一站式追踪人工智能学术顶会，不错过任何一个 deadline',
    },
    wiki: {
      title: '百科',
      desc: '数百条人工智能相关的词条帮助您理解人工智能',
      longDesc: '我们编汇了数百条相关词条，帮助您理解「人工智能」',
    },
    sota: {
      title: 'SOTA',
      desc: '最新人工智能模型性能指标、GPU基准测试及前沿论文',
      longDesc: '最新人工智能模型性能指标、GPU基准测试及前沿论文',
    },
    projects: {
      title: '开源项目',
      desc: '本站维护的开源翻译项目',
      tvm: {
        label: 'TVM 中文',
        desc: 'Apache TVM 是一个端到端的深度学习编译框架，适用于 CPU、GPU 和各种机器学习加速芯片',
      },
      triton: {
        label: 'Triton 中文',
        desc: 'Triton 是一种用于并行编程的语言和编译器，能够在现代 GPU 硬件上以最大吞吐量运行',
      },
      vllm: {
        label: 'vLLM 中文',
        desc: 'vLLM 是一个快速且易于使用的库，专为大型语言模型 (LLM) 的推理和部署而设计',
      },
    },
    search: {
      title: '全站搜索',
      desc: '全站搜索',
    },
    llmModels: {
      title: 'LLM 模型天梯',
      desc: '探索可用的大语言模型天梯排行榜',
    },
    gpuLeaderboard: {
      title: 'GPU 天梯',
      desc: '比较不同 NVIDIA GPU 上 AI 工作负载的性能指标',
      selectGpu: '选择 GPU',
      architecture: '架构',
      releaseDate: '发布日期',
      memory: '显存',
      gpuModel: 'GPU 型号',
      cudaCores: 'CUDA 核心',
      tgp: 'TGP',
      powerConsumption: '功耗',
      fp8Performance: 'FP8 性能',
      fp16Performance: 'FP16 性能',
      fp32Performance: 'FP32 性能',
      fp64Performance: 'FP64 性能',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'GPU 性能可视化',
      benchmarkComparison: 'GPU 基准测试对比',
      filterGpus: '筛选 GPU...',
      noResults: '无结果。',
      percentLess: '低 {{percent}}%',
      percentMore: '多 {{percent}}%',
      percentFaster: '快 {{percent}}%',
    },
    headlines: {
      title: '头条',
      desc: '从各种来源汇总的最新 AI 头条和新闻',
      viewAll: '查看所有头条',
    },
    about: {
      title: '关于',
      desc: '关于本站',
    },
  },
  search: {
    contentType: '类型',
    categories: '文章分类',
    tags: '标签',
    datasetCategories: '数据集分类',
    organizations: '机构',
    placeholder: '搜索标题、内容、标签...',
    sortBy: '排序方式',
    relevance: '相关性',
    newest: '时间（最新）',
    oldest: '时间（最早）',
    section: '栏目',
    createTime: '创建时间',
    displayTags: '展示标签',
    noTitle: '无标题',
    uncategorized: '未分类',
    last7Days: '最近7天',
    last7To30Days: '7-30天前',
    last30To90Days: '30-90天前',
    last90DaysTo1Year: '90天-1年前',
    over1Year: '1年前',
    noResults: '未找到相关结果',
    searchError: '搜索时出现错误',
    loading: '搜索中...',
  },
  llmModelsPage: {
    modelColumn: '模型',
    inputPrice: '输入价格',
    outputPrice: '输出价格',
    contextLength: '上下文长度',
    features: '功能',
    description: '描述',
    pricePerMillion: '${{price}}/百万 token',
    searchModels: '按名称搜索模型',
    noData: '-',
  },
}

export default dictionary
