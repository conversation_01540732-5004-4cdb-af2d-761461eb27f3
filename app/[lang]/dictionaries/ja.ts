import { config } from '@/data/config'

const dictionary = {
  news: {
    label: 'ニュース',
  },
  time: {
    days: '日',
    hours: '時間',
    minutes: '分',
    seconds: '秒',
    ended: '終了',
  },
  timeObj: {
    days: '{{count}}日',
    hours: '{{count}}時',
    minutes: '{{count}}分',
    seconds: '{{count}}秒',
  },
  theme: {
    system: 'システム',
    dark: 'ダーク',
    light: 'ライト',
  },
  common: {
    title: 'HyperAI超神経',
    slogan: '学習、理解、実践、コミュニティと共に人工知能の未来を構築する',
    heroButton: 'データセット、チュートリアル、AIケースを検索する',
    publishedAt: '公開日',
    moreLinks: {
      news: 'ニュースをもっと見る',
      tutorials: 'チュートリアルをもっと見る',
      datasets: 'データセットをもっと見る',
      events: '学会をもっと見る',
      wiki: '百科をもっと見る',
      sota: 'SOTAをもっと見る',
      projects: 'オープンソースをもっと見る',
      papers: '論文をもっと見る',
    },
    relatedContent: '関連{{type}}',
    contentTypes: {
      news: 'ニュース',
      posts: '記事',
      tutorials: 'チュートリアル',
      datasets: 'データセット',
      papers: '論文',
      events: '学会',
      wiki: '百科項目',
    },
    login: 'ログイン',
    logout: 'ログアウト',
    accountInfo: 'アカウント情報',
    previous: '前へ',
    next: '次へ',
  },
  tutorials: {
    metadata: {
      date: '日付',
      size: 'サイズ',
      author: '作成者',
      tags: 'タグ',
      latestVersion: '最新バージョン',
    },
    runOnline: 'オンラインでこのチュートリアルを実行',
    notebookPreview: 'ノートブックプレビュー',
  },
  datasets: {
    metadata: {
      organization: '組織',
      publishUrl: '公開URL',
      license: 'ライセンス',
      categories: 'カテゴリ',
    },
    download: 'データセットダウンロード',
    magnetLink: 'マグネットリンク',
    noDownload: '利用可能なダウンロードがありません',
    downloadHelp: 'ダウンロードヘルプ',
    stats: {
      seeding: 'シーディング',
      seedingTooltip: '過去1時間以内にシードしているクライアントの数',
      downloading: 'ダウンロード中',
      downloadingTooltip: '過去1時間以内にダウンロードしているクライアントの数',
      completed: 'ダウンロード完了',
      completedTooltip:
        '完了したダウンロードの総数、24時間ごとに更新、ダウンロード進行状況が100%に達したもののみカウント',
      traffic: '貢献トラフィック',
      totalDownloads: '総ダウンロード数',
    },
  },
  events: {
    controls: {
      deadline: '投稿締切日',
      results: '結果発表',
      eventDate: '開催日',
      reverseOrder: '逆順',
    },
    metadata: {
      date: '日付',
      venue: '開催地',
      website: '公式ウェブサイト',
      h5Index: 'H5インデックス',
      ccfLevel: 'CCFレベル',
    },
    timeline: {
      deadline: '締切',
      deadlineDesc: '学会やイベントに提案や論文を提出する最終期限',
      results: '結果発表',
      resultsDesc: '主催者が投稿者に提案や論文が採択されたかどうかを通知',
      eventStart: '開催開始',
      eventStartDesc: '学会やイベントが正式に開催される期間',
      eventEnd: '開催終了',
      eventEndDesc: '学会やイベントの終了時間',
    },
    visitWebsite: 'ウェブサイトを訪問',
    conference: '会議',
    ongoing: '開催中',
    tbd: '未定',
  },
  sota: {
    pageTitle: 'AI SOTA ベンチマーク',
    pageDescription: '最新のAIモデル性能比較、GPUベンチマーク、および最先端論文',
    categories: {
      title: 'カテゴリ',
      description: 'カテゴリ別にタスクを閲覧',
      taskCount: 'タスク',
    },
    taskNotFound: 'タスクが見つかりません',
    benchmarksList: 'ベンチマークリスト',
    allBenchmarksForTask: 'このタスクに関連するすべてのベンチマーク',
    noBenchmarkData: 'このタスクで利用可能なベンチマークデータがありません',
    metrics: '評価指標',
    results: '評価結果',
    resultsDescription: 'このベンチマークにおける各モデルのパフォーマンス結果',
    comparisonTable: '比較表',
    modelName: 'モデル名',
    noResultsData: 'このベンチマークには詳細な結果データがありません',
    taskList: {
      papers: ' 論文',
      benchmarks: ' ベンチマーク',
      models: ' モデル',
      noData: 'データなし',
      noTaskData: '利用可能なタスクデータがありません',
      searchPlaceholder: 'タスクを検索...',
      noResults: '結果が見つかりません',
      tryDifferentSearch: '別の検索語を試してください',
    },
    modelBenchmark: {
      title: 'AIモデル性能ベンチマーク',
      description: '主流AIモデルの各タスクにおける性能指標比較、最先端技術水準を展示',
    },
    gpuBenchmark: {
      title: 'GPUベンチマーク',
      description: '最新のGPUハードウェアとソフトウェアの性能評価、賢明なハードウェア選択をサポート',
    },
    gpuBenchmarks: {
      softwarePerformance: 'ソフトウェア性能',
      hardwarePerformance: 'ハードウェア性能',
      showChartView: 'チャート表示',
      showTableView: 'テーブル表示',
      performance: '性能',
      requestThroughput: 'リクエストスループット',
      outputTokenThroughput: '出力トークンスループット',
      totalTokenThroughput: '合計トークンスループット',
      noData: 'データなし',
      noGpuData: '利用可能なGPUベンチマークデータがありません',
      noHardwareData: '利用可能なGPUハードウェアベンチマークデータがありません',
      noSoftwareData: '利用可能なGPUソフトウェアベンチマークデータがありません',
      gpuHardwareBenchmark: 'GPUハードウェアベンチマーク',
      manufacturer: 'メーカー',
      unknown: '不明',
      metric: '指標',
      value: '値',
      model: 'モデル',
      environment: '環境',
      selectMetric: '指標を選択',
      allMetrics: 'すべての指標',
      maxContextLength: '最大コンテキスト長',
      maxSequences: '最大シーケンス数',
    },
    benchmarkDetails: {
      viewDetails: '詳細を表示',
      bestModel: '最高モデル',
      searchPlaceholder: 'ベンチマークを検索...',
      noResults: '結果が見つかりません',
      tryDifferentSearch: '別の検索語を試してください',
    },
    papers: {
      title: '最新論文',
      abstract: '要約',
      description: '日々更新される最先端AI研究論文、人工知能の最新動向を把握',
      noData: 'データなし',
      noAvailableData: '利用可能な論文データがありません',
      releaseDate: '公開日',
      retrievalDate: '取得日',
      viewDetails: '論文の詳細を見る',
    },
  },
  sidebar: {
    newsletter: {
      title: '最新情報を購読する',
      description: '北京時間',
      time: '毎週月曜日の午前9時',
      delivery: 'に、その週の最新情報をメールでお届けします',
      emailPlaceholder: 'メールアドレスを入力',
      submitButton: '購読',
      provider: 'メール配信サービスは {{provider}} によって提供されています',
    },
    live: {
      loading: 'ライブ情報を読み込み中…',
      streaming: 'ライブ配信中: ',
      offline: 'Hyper.AI は現在配信していません',
    },
    openbayes: {
      alt: 'OpenBayes - 登録して無料の計算能力を獲得',
    },
    tvm: {
      alt: 'TVM 中国語サイト',
    },
  },
  command: {
    nav: 'ナビゲーション',
    searchResult: '検索結果',
    placeholder: 'キーワードを入力して検索…',
    label: 'サイトを検索…',
  },
  footer: {
    about: {
      title: 'サイトについて',
      aboutUs: '私たちについて',
      datasetHelp: 'データセットヘルプ',
    },
    products: {
      title: 'プロダクト',
    },
    links: {
      title: 'リンク',
    },
  },
  nav: {
    home: {
      title: 'ホーム',
      desc: `${config.siteTitle}のホームページへアクセス`,
    },
    news: {
      title: 'ニュース',
      desc: 'AI関連の最新ニュース',
    },
    tutorials: {
      title: 'チュートリアル',
      desc: '厳選された数百の機械学習チュートリアル',
      longDesc:
        '我々は数百の選択された機械学習チュートリアルを収集し、Jupyterノートブックの形式で整理しました。これらのノートブックは、我々のパートナーである機械学習プラットフォームOpenBayesで無料で実行できます',
    },
    datasets: {
      title: 'データセット',
      desc: '数千のオープンデータセットリソース',
      longDesc:
        '我々は数千のオープンデータセットリソースを収集し、対応するストレージサービスを提供しました。関連する専門家が無料でダウンロードできます',
    },
    events: {
      title: '学会',
      desc: 'AI学術会議の追跡、締め切りを見逃さない',
      longDesc: 'AI学術会議の追跡、締め切りを見逃さない',
    },
    wiki: {
      title: '百科事典',
      desc: '「人工知能」を理解するための数百のAI関連エントリー',
      longDesc: '我々は数百の関連エントリーを収集し、「人工知能」を理解するための数百のAI関連エントリー',
    },
    sota: {
      title: 'SOTA',
      desc: '最新のAIモデル性能指標、GPUベンチマーク、最先端論文',
      longDesc: '最新のAIモデル性能指標、GPUベンチマーク、最先端論文',
    },
    projects: {
      title: 'オープンソース',
      desc: '当サイトが運営するオープンソース翻訳プロジェクト',
      tvm: {
        label: 'TVM 中国語',
        desc: 'Apache TVMはCPU、GPU、および様々な機械学習アクセラレータ向けのエンドツーエンドの深層学習コンパイラフレームワークです',
      },
      triton: {
        label: 'Triton 中国語',
        desc: 'Tritonは現代のGPUハードウェア上で最大スループットで実行できる並列プログラミング用の言語とコンパイラです',
      },
      vllm: {
        label: 'vLLM 中国語',
        desc: 'vLLMは大規模言語モデル（LLM）の推論と展開のために設計された高速で使いやすいライブラリです',
      },
    },
    search: {
      title: '検索',
      desc: 'サイト全体検索',
    },
    llmModels: {
      title: 'LLMモデル',
      desc: '利用可能な大規模言語モデルのランキングを探索する',
    },
    gpuLeaderboard: {
      title: 'GPU ランキング',
      desc: '異なる NVIDIA GPU 間の AI ワークロードのパフォーマンス指標を比較',
      selectGpu: 'GPUを選択',
      architecture: 'アーキテクチャ',
      releaseDate: 'リリース日',
      memory: 'メモリ',
      gpuModel: 'GPUモデル',
      cudaCores: 'CUDAコア',
      tgp: 'TGP',
      powerConsumption: '消費電力',
      fp8Performance: 'FP8性能',
      fp16Performance: 'FP16性能',
      fp32Performance: 'FP32性能',
      fp64Performance: 'FP64性能',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'GPU性能の可視化',
      benchmarkComparison: 'GPUベンチマーク比較',
      filterGpus: 'GPUをフィルター...',
      noResults: '結果がありません。',
      percentLess: '{{percent}}% 低い',
      percentMore: '{{percent}}% 多い',
      percentFaster: '{{percent}}% 高速',
    },
    headlines: {
      title: 'ヘッドライン',
      desc: '様々なソースから収集された最新のAIヘッドラインやニュース',
      viewAll: 'すべてのヘッドラインを見る',
    },
    about: {
      title: 'サイトについて',
      desc: 'このサイトについて',
    },
  },
  search: {
    contentType: 'コンテンツタイプ',
    categories: 'カテゴリー',
    tags: 'タグ',
    datasetCategories: 'データセットカテゴリー',
    organizations: '組織',
    placeholder: 'タイトル、コンテンツ、タグを検索...',
    sortBy: '並べ替え',
    relevance: '関連性',
    newest: '時間（最新）',
    oldest: '時間（最古）',
    section: 'セクション',
    createTime: '作成時間',
    displayTags: 'タグを表示',
    noTitle: 'タイトルなし',
    uncategorized: '未分類',
    last7Days: '過去7日間',
    last7To30Days: '7〜30日前',
    last30To90Days: '30〜90日前',
    last90DaysTo1Year: '90日〜1年前',
    over1Year: '1年以上前',
    noResults: '結果が見つかりません',
    searchError: '検索中にエラーが発生しました',
    loading: '検索中...',
  },
  llmModelsPage: {
    modelColumn: 'モデル',
    inputPrice: '入力価格',
    outputPrice: '出力価格',
    contextLength: 'コンテキスト長',
    features: '機能',
    description: '説明',
    pricePerMillion: '${{price}}/百万トークン',
    searchModels: '名前でモデルを検索',
    noData: '-',
  },
}

export default dictionary
