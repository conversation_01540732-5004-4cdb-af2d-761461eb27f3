import { config } from '@/data/config'

const dictionary = {
  news: {
    label: 'News',
  },
  time: {
    days: 'd',
    hours: 'h',
    minutes: 'm',
    seconds: 's',
    ended: 'Ended',
  },
  timeObj: {
    days: '{{count}}d',
    hours: '{{count}}h',
    minutes: '{{count}}m',
    seconds: '{{count}}s',
  },
  theme: {
    system: 'System',
    dark: 'Dark',
    light: 'Light',
  },
  common: {
    title: 'HyperAI',
    slogan: 'Build the Future of Artificial Intelligence',
    heroButton: 'Search datasets, tutorials…',
    publishedAt: 'Published at',
    moreLinks: {
      news: 'View More News',
      tutorials: 'View More Tutorials',
      datasets: 'View More Datasets',
      events: 'View More Events',
      wiki: 'View More Wiki',
      sota: 'View More SOTA',
      projects: 'View More Projects',
      papers: 'View More Papers',
    },
    relatedContent: 'Related {{type}}',
    contentTypes: {
      news: 'News',
      posts: 'Posts',
      tutorials: 'Tutorials',
      datasets: 'Datasets',
      papers: 'Papers',
      events: 'Events',
      wiki: 'Wiki Entries',
    },
    previous: 'Previous',
    next: 'Next',
    login: 'Login',
    logout: 'Logout',
    accountInfo: 'Account Info',
  },
  tutorials: {
    metadata: {
      date: 'Date',
      size: 'Size',
      author: 'Author',
      tags: 'Tags',
      latestVersion: 'Latest Version',
    },
    runOnline: 'Run this Tutorial Online',
    notebookPreview: 'Notebook Preview',
  },
  datasets: {
    metadata: {
      organization: 'Organization',
      publishUrl: 'Publish URL',
      license: 'License',
      categories: 'Categories',
    },
    download: 'Dataset Download',
    magnetLink: 'Magnet Link',
    noDownload: 'Downloads Not Available',
    downloadHelp: 'Download Help',
    stats: {
      seeding: 'Seeding',
      seedingTooltip: 'Number of peers seeding in the past 1 hour',
      downloading: 'Downloading',
      downloadingTooltip: 'Number of peers downloading in the past 1 hour',
      completed: 'Completed',
      completedTooltip:
        'Total number of completed downloads, updated every 24 hours, only counting downloads that reached 100%',
      traffic: 'Contributed Traffic',
      totalDownloads: 'Total Downloads',
    },
  },
  events: {
    controls: {
      deadline: 'Submission Deadline',
      results: 'Results Announcement',
      eventDate: 'Event Date',
      reverseOrder: 'Reverse',
    },
    metadata: {
      date: 'Date',
      venue: 'Venue',
      website: 'Website',
      h5Index: 'H5 Index',
      ccfLevel: 'CCF Level',
    },
    timeline: {
      deadline: 'Deadline',
      deadlineDesc: 'Final deadline for submitting proposals or papers to the conference or event',
      results: 'Results Announced',
      resultsDesc: 'Organizers notify submitters whether their proposals or papers are accepted',
      eventStart: 'Event Begins',
      eventStartDesc: 'Period when the conference or event officially takes place',
      eventEnd: 'Event Ends',
      eventEndDesc: 'End time of the conference or event',
    },
    visitWebsite: 'Visit Website',
    conference: 'Conference',
    ongoing: 'Ongoing',
    tbd: 'TBD',
  },
  sota: {
    pageTitle: 'AI SOTA Benchmarks',
    pageDescription: 'Latest AI model performance metrics, GPU benchmarks, and cutting-edge papers',
    categories: {
      title: 'Categories',
      description: 'Browse tasks by category',
      taskCount: 'tasks',
    },
    taskNotFound: 'Task not found',
    benchmarksList: 'Benchmark List',
    allBenchmarksForTask: 'All benchmarks related to this task',
    noBenchmarkData: 'No benchmark data available for this task',
    metrics: 'Metrics',
    results: 'Results',
    resultsDescription: 'Performance results of various models on this benchmark',
    comparisonTable: 'Comparison Table',
    modelName: 'Model Name',
    noResultsData: 'No detailed results available for this benchmark',
    taskList: {
      papers: ' papers',
      benchmarks: ' benchmarks',
      models: ' models',
      noData: 'No Data',
      noTaskData: 'No task data available',
      searchPlaceholder: 'Search tasks...',
      noResults: 'No results found',
      tryDifferentSearch: 'Try a different search term',
    },
    modelBenchmark: {
      title: 'AI Model Performance Benchmarks',
      description:
        'Performance metrics of mainstream AI models across various tasks, showcasing the state-of-the-art technology',
    },
    gpuBenchmark: {
      title: 'GPU Benchmarks',
      description:
        'Latest GPU hardware and software performance evaluations to help you make informed hardware choices',
    },
    gpuBenchmarks: {
      softwarePerformance: 'Software Performance',
      hardwarePerformance: 'Hardware Performance',
      showChartView: 'Show chart view',
      showTableView: 'Show table view',
      performance: 'Performance',
      requestThroughput: 'Request Throughput',
      outputTokenThroughput: 'Output Token Throughput',
      totalTokenThroughput: 'Total Token Throughput',
      noData: 'No Data',
      noGpuData: 'No GPU benchmark data available',
      noHardwareData: 'No GPU hardware benchmark data available',
      noSoftwareData: 'No GPU software benchmark data available',
      gpuHardwareBenchmark: 'GPU Hardware Benchmark',
      manufacturer: 'Manufacturer',
      unknown: 'Unknown',
      metric: 'Metric',
      value: 'Value',
      model: 'Model',
      environment: 'Environment',
      selectMetric: 'Select metric',
      allMetrics: 'All metrics',
      maxContextLength: 'Max Context Length',
      maxSequences: 'Max Sequences',
    },
    benchmarkDetails: {
      viewDetails: 'View Details',
      bestModel: 'Best model',
      searchPlaceholder: 'Search benchmarks...',
      noResults: 'No results found',
      tryDifferentSearch: 'Try a different search term',
    },
    papers: {
      title: 'Latest Papers',
      abstract: 'Abstract',
      description: 'Daily updated cutting-edge AI research papers to help you keep up with the latest AI trends',
      noData: 'No Data',
      noAvailableData: 'No paper data available',
      releaseDate: 'Release Date',
      retrievalDate: 'Retrieval Date',
      viewDetails: 'View Paper Details',
    },
  },
  sidebar: {
    newsletter: {
      title: 'Subscribe to our latest updates',
      description: 'We will deliver the latest updates of the week to your inbox at',
      time: "nine o'clock every Monday morning",
      delivery: '',
      emailPlaceholder: 'Enter email address',
      submitButton: 'Subscribe',
      provider: 'Powered by {{provider}}',
    },
    live: {
      loading: 'Loading live info…',
      streaming: 'Now live:',
      offline: 'Hyper.AI is not streaming yet',
    },
    openbayes: {
      alt: 'OpenBayes - Register to get free computing power',
    },
    tvm: {
      alt: 'TVM Chinese Site',
    },
  },
  command: {
    nav: 'Navigation',
    searchResult: 'Search Results',
    placeholder: 'Enter keywords to search…',
    label: 'Search the site…',
  },
  footer: {
    about: {
      title: 'About',
      aboutUs: 'About Us',
      datasetHelp: 'Dataset Help',
    },
    products: {
      title: 'Products',
    },
    links: {
      title: 'Links',
    },
  },
  nav: {
    home: {
      title: 'Home',
      desc: `Visit the ${config.siteTitle} homepage`,
    },
    news: {
      title: 'News',
      desc: 'Latest AI related news',
    },
    tutorials: {
      title: 'Tutorials',
      desc: 'Hundreds of selected machine learning tutorials',
      longDesc:
        'We have collected hundreds of selected machine learning tutorials and organized them in the form of Jupyter notebooks. You can run these notebooks for free on our partner machine learning platform, OpenBayes',
    },
    datasets: {
      title: 'Datasets',
      desc: 'Thousands of open dataset resources',
      longDesc:
        'We have collected thousands of open dataset resources and provided corresponding storage services. Free to download for relevant professionals',
    },
    events: {
      title: 'Events',
      desc: 'Track AI academic conferences, never miss a deadline',
      longDesc: 'Track AI academic conferences, never miss a deadline',
    },
    wiki: {
      title: 'Wiki',
      desc: 'Hundreds of AI-related entries to help you understand artificial intelligence',
      longDesc: 'We have compiled hundreds of related entries to help you understand "artificial intelligence"',
    },
    sota: {
      title: 'SOTA',
      desc: 'Latest AI model performance metrics, GPU benchmarks and cutting-edge papers',
      longDesc: 'Latest AI model performance metrics, GPU benchmarks and cutting-edge papers',
    },
    projects: {
      title: 'Open Source',
      desc: 'Open source translation projects maintained by this site',
      tvm: {
        label: 'TVM Chinese',
        desc: 'Apache TVM is an end-to-end deep learning compiler framework for CPUs, GPUs, and ML accelerators',
      },
      triton: {
        label: 'Triton Chinese',
        desc: 'Triton is a language and compiler for parallel programming that can run at maximum throughput on modern GPU hardware',
      },
      vllm: {
        label: 'vLLM Chinese',
        desc: 'vLLM is a fast and easy-to-use library designed for inference and deployment of large language models (LLMs)',
      },
    },
    search: {
      title: 'Search',
      desc: 'Site-wide search',
    },
    llmModels: {
      title: 'LLM Models',
      desc: 'Explore available large language model leaderboard and rankings',
    },
    gpuLeaderboard: {
      title: 'GPU Leaderboard',
      desc: 'Compare performance metrics for AI workloads across different NVIDIA GPUs',
      selectGpu: 'Select a GPU',
      architecture: 'Architecture',
      releaseDate: 'Release Date',
      memory: 'Memory',
      gpuModel: 'GPU Model',
      cudaCores: 'CUDA Cores',
      tgp: 'TGP',
      powerConsumption: 'Power Consumption',
      fp8Performance: 'FP8 Performance',
      fp16Performance: 'FP16 Performance',
      fp32Performance: 'FP32 Performance',
      fp64Performance: 'FP64 Performance',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'GPU Performance Visualization',
      benchmarkComparison: 'GPU Benchmark Comparison',
      filterGpus: 'Filter GPUs...',
      noResults: 'No results.',
      percentLess: '{{percent}}% less',
      percentMore: '{{percent}}% more',
      percentFaster: '{{percent}}% faster',
    },
    headlines: {
      title: 'Headlines',
      desc: 'Latest AI headlines and news summarized from various sources',
      viewAll: 'View all headlines',
    },
    about: {
      title: 'About',
      desc: 'About this site',
    },
  },
  search: {
    contentType: 'Content Type',
    categories: 'Categories',
    tags: 'Tags',
    datasetCategories: 'Dataset Categories',
    organizations: 'Organizations',
    placeholder: 'Search titles, content, tags...',
    sortBy: 'Sort by',
    relevance: 'Relevance',
    newest: 'Time (Newest)',
    oldest: 'Time (Oldest)',
    section: 'Section',
    createTime: 'Create Time',
    displayTags: 'Display Tags',
    noTitle: 'No Title',
    uncategorized: 'Uncategorized',
    last7Days: 'Last 7 days',
    last7To30Days: '7-30 days ago',
    last30To90Days: '30-90 days ago',
    last90DaysTo1Year: '90 days - 1 year ago',
    over1Year: 'Over 1 year ago',
    noResults: 'No results found',
    searchError: 'An error occurred while searching',
    loading: 'Searching...',
  },
  llmModelsPage: {
    modelColumn: 'Model',
    inputPrice: 'Input Price',
    outputPrice: 'Output Price',
    contextLength: 'Context Length',
    features: 'Features',
    description: 'Description',
    pricePerMillion: '${{price}}/M tokens',
    searchModels: 'Search models by name',
    noData: '-',
  },
}

export default dictionary
