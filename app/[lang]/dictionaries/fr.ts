import { config } from '@/data/config'

const dictionary = {
  news: {
    label: 'Actualités',
  },
  time: {
    days: 'j',
    hours: 'h',
    minutes: 'm',
    seconds: 's',
    ended: 'Terminé',
  },
  timeObj: {
    days: '{{count}}j',
    hours: '{{count}}h',
    minutes: '{{count}}m',
    seconds: '{{count}}s',
  },
  theme: {
    system: 'Système',
    dark: 'Sombre',
    light: 'Clair',
  },
  common: {
    title: 'HyperAI',
    slogan: "Construire l'avenir de l'intelligence artificielle",
    heroButton: 'Rechercher des ensembles de données, tutoriels...',
    publishedAt: 'Publié le',
    moreLinks: {
      news: "Voir plus d'actualités",
      tutorials: 'Voir plus de tutoriels',
      datasets: "Voir plus d'ensembles de données",
      events: "Voir plus d'événements",
      wiki: "Voir plus d'articles Wiki",
      sota: 'Voir plus de SOTA',
      projects: 'Voir plus de projets',
      papers: 'Voir plus de publications scientifiques',
    },
    relatedContent: '{{type}} associés',
    contentTypes: {
      news: 'Actualités',
      posts: 'Publications',
      tutorials: 'Tutoriels',
      datasets: 'Ensembles de données',
      papers: 'Publications scientifiques',
      events: 'Événements',
      wiki: 'Articles Wiki',
    },
    login: 'Connexion',
    logout: 'Déconnexion',
    accountInfo: 'Informations du compte',
    previous: 'Précédent',
    next: 'Suivant',
  },
  tutorials: {
    metadata: {
      date: 'Date',
      size: 'Taille',
      author: 'Auteur',
      tags: 'Tags',
      latestVersion: 'Dernière version',
    },
    runOnline: 'Exécuter ce tutoriel en ligne',
    notebookPreview: 'Aperçu du notebook',
  },
  datasets: {
    metadata: {
      organization: 'Organisation',
      publishUrl: 'URL de publication',
      license: 'Licence',
      categories: 'Catégories',
    },
    download: 'Téléchargement du jeu de données',
    magnetLink: 'Lien Magnet',
    noDownload: 'Téléchargements non disponibles',
    downloadHelp: 'Aide au téléchargement',
    stats: {
      seeding: 'Partage',
      seedingTooltip: 'Nombre de pairs en partage au cours de la dernière heure',
      downloading: 'Téléchargement',
      downloadingTooltip: 'Nombre de pairs en téléchargement au cours de la dernière heure',
      completed: 'Terminés',
      completedTooltip:
        'Nombre total de téléchargements terminés, mis à jour toutes les 24 heures, comptant uniquement les téléchargements ayant atteint 100%',
      traffic: 'Trafic contribué',
      totalDownloads: 'Téléchargements totaux',
    },
  },
  events: {
    controls: {
      deadline: 'Date limite de soumission',
      results: 'Annonce des résultats',
      eventDate: "Date de l'événement",
      reverseOrder: 'Ordre inverse',
    },
    metadata: {
      date: 'Date',
      venue: 'Lieu',
      website: 'Site web',
      h5Index: 'Indice H5',
      ccfLevel: 'Niveau CCF',
    },
    timeline: {
      deadline: 'Date limite',
      deadlineDesc:
        "Date limite finale pour soumettre des propositions ou des articles à la conférence ou à l'événement",
      results: 'Résultats annoncés',
      resultsDesc: 'Les organisateurs informent les soumissionnaires si leurs propositions ou articles sont acceptés',
      eventStart: "Début de l'événement",
      eventStartDesc: "Période pendant laquelle la conférence ou l'événement se déroule officiellement",
      eventEnd: "Fin de l'événement",
      eventEndDesc: "Heure de fin de la conférence ou de l'événement",
    },
    visitWebsite: 'Visiter le site web',
    conference: 'Conférence',
    ongoing: 'En cours',
    tbd: 'À déterminer',
  },
  sota: {
    pageTitle: 'Benchmarks IA SOTA',
    pageDescription:
      'Dernières métriques de performance des modèles IA, benchmarks GPU et articles de recherche de pointe',
    categories: {
      title: 'Catégories',
      description: 'Parcourir les tâches par catégorie',
      taskCount: 'tâches',
    },
    taskNotFound: 'Tâche non trouvée',
    benchmarksList: 'Liste des benchmarks',
    allBenchmarksForTask: 'Tous les benchmarks liés à cette tâche',
    noBenchmarkData: 'Aucune donnée de benchmark disponible pour cette tâche',
    metrics: 'Métriques',
    results: 'Résultats',
    resultsDescription: 'Résultats de performance de divers modèles sur ce benchmark',
    comparisonTable: 'Tableau comparatif',
    modelName: 'Nom du modèle',
    noResultsData: 'Aucun résultat détaillé disponible pour ce benchmark',
    taskList: {
      papers: ' articles',
      benchmarks: ' benchmarks',
      models: ' modèles',
      noData: 'Aucune donnée',
      noTaskData: 'Aucune donnée de tâche disponible',
      searchPlaceholder: 'Rechercher des tâches...',
      noResults: 'Aucun résultat trouvé',
      tryDifferentSearch: 'Essayez un terme de recherche différent',
    },
    modelBenchmark: {
      title: 'Benchmarks de performance des modèles IA',
      description:
        'Métriques de performance des modèles IA grand public sur diverses tâches, mettant en valeur la technologie de pointe',
    },
    gpuBenchmark: {
      title: 'Benchmarks GPU',
      description:
        'Dernières évaluations de performance matérielle et logicielle GPU pour vous aider à faire des choix matériels éclairés',
    },
    gpuBenchmarks: {
      softwarePerformance: 'Performance logicielle',
      hardwarePerformance: 'Performance matérielle',
      showChartView: 'Afficher en graphique',
      showTableView: 'Afficher en tableau',
      performance: 'Performance',
      requestThroughput: 'Débit de requêtes',
      outputTokenThroughput: 'Débit de tokens de sortie',
      totalTokenThroughput: 'Débit total de tokens',
      noData: 'Aucune donnée',
      noGpuData: 'Aucune donnée de benchmark GPU disponible',
      noHardwareData: 'Aucune donnée de benchmark matériel GPU disponible',
      noSoftwareData: 'Aucune donnée de benchmark logiciel GPU disponible',
      gpuHardwareBenchmark: 'Benchmark matériel GPU',
      manufacturer: 'Fabricant',
      unknown: 'Inconnu',
      metric: 'Métrique',
      value: 'Valeur',
      model: 'Modèle',
      environment: 'Environnement',
      selectMetric: 'Sélectionner une métrique',
      allMetrics: 'Toutes les métriques',
      maxContextLength: 'Longueur de contexte maximale',
      maxSequences: 'Séquences maximales',
    },
    benchmarkDetails: {
      viewDetails: 'Voir les détails',
      bestModel: 'Meilleur modèle',
      searchPlaceholder: 'Rechercher des benchmarks...',
      noResults: 'Aucun résultat trouvé',
      tryDifferentSearch: 'Essayez un terme de recherche différent',
    },
    papers: {
      title: 'Articles de recherche récents',
      abstract: 'Résumé',
      description:
        "Articles de recherche sur l'IA mis à jour quotidiennement pour vous aider à suivre les dernières tendances en matière d'IA",
      noData: 'Aucune donnée',
      noAvailableData: "Aucune donnée d'article disponible",
      releaseDate: 'Date de publication',
      retrievalDate: 'Date de récupération',
      viewDetails: "Voir les détails de l'article",
    },
  },
  sidebar: {
    newsletter: {
      title: 'Abonnez-vous à nos dernières mises à jour',
      description: 'Nous vous enverrons les dernières mises à jour de la semaine dans votre boîte de réception à',
      time: 'neuf heures chaque lundi matin',
      delivery: '',
      emailPlaceholder: 'Entrez votre adresse e-mail',
      submitButton: "S'abonner",
      provider: 'Propulsé par {{provider}}',
    },
    live: {
      loading: 'Chargement des informations en direct...',
      streaming: 'En direct:',
      offline: "Hyper.AI n'est pas encore en streaming",
    },
    openbayes: {
      alt: 'OpenBayes - Inscrivez-vous pour obtenir de la puissance de calcul gratuite',
    },
    tvm: {
      alt: 'Site chinois TVM',
    },
  },
  command: {
    nav: 'Navigation',
    searchResult: 'Résultats de recherche',
    placeholder: 'Entrez des mots-clés pour rechercher...',
    label: 'Rechercher sur le site...',
  },
  footer: {
    about: {
      title: 'À propos',
      aboutUs: 'À propos de nous',
      datasetHelp: 'Aide aux ensembles de données',
    },
    products: {
      title: 'Produits',
    },
    links: {
      title: 'Liens',
    },
  },
  nav: {
    home: {
      title: 'Accueil',
      desc: `Visitez la page d'accueil de ${config.siteTitle}`,
    },
    news: {
      title: 'Actualités',
      desc: "Dernières actualités liées à l'IA",
    },
    tutorials: {
      title: 'Tutoriels',
      desc: "Des centaines de tutoriels sélectionnés sur l'apprentissage automatique",
      longDesc:
        "Nous avons rassemblé des centaines de tutoriels sélectionnés sur l'apprentissage automatique et les avons organisés sous forme de notebooks Jupyter. Vous pouvez exécuter ces notebooks gratuitement sur notre plateforme partenaire d'apprentissage automatique, OpenBayes",
    },
    datasets: {
      title: 'Ensembles de données',
      desc: "Des milliers de ressources d'ensembles de données ouvertes",
      longDesc:
        "Nous avons rassemblé des milliers de ressources d'ensembles de données ouvertes et fourni les services de stockage correspondants. Téléchargement gratuit pour les professionnels concernés",
    },
    events: {
      title: 'Événements',
      desc: "Suivez les conférences académiques sur l'IA, ne manquez jamais une date limite",
      longDesc: "Suivez les conférences académiques sur l'IA, ne manquez jamais une date limite",
    },
    wiki: {
      title: 'Wiki',
      desc: "Des centaines d'entrées liées à l'IA pour vous aider à comprendre l'intelligence artificielle",
      longDesc:
        'Nous avons compilé des centaines d\'entrées connexes pour vous aider à comprendre "l\'intelligence artificielle"',
    },
    sota: {
      title: 'SOTA',
      desc: 'Dernières métriques de performance des modèles IA, benchmarks GPU et articles de pointe',
      longDesc: 'Dernières métriques de performance des modèles IA, benchmarks GPU et articles de pointe',
    },
    projects: {
      title: 'Open Source',
      desc: 'Projets de traduction open source maintenus par ce site',
      tvm: {
        label: 'TVM Chinois',
        desc: "Apache TVM est un framework de compilation d'apprentage profond de bout en bout pour les CPU, GPU et accélérateurs ML",
      },
      triton: {
        label: 'Triton Chinois',
        desc: 'Triton est un langage et un compilateur pour la programmation parallèle qui peut fonctionner à débit maximal sur le matériel GPU moderne',
      },
      vllm: {
        label: 'vLLM Chinois',
        desc: "vLLM est une bibliothèque rapide et facile à utiliser conçue pour l'inférence et le déploiement de grands modèles de langage (LLM)",
      },
    },
    search: {
      title: 'Recherche',
      desc: "Recherche sur l'ensemble du site",
    },
    llmModels: {
      title: 'Modèles LLM',
      desc: 'Explorez les classements de modèles de langage disponibles',
    },
    gpuLeaderboard: {
      title: 'Classement GPU',
      desc: 'Comparez les métriques de performance pour les charges de travail IA sur différents GPU NVIDIA',
      selectGpu: 'Sélectionner un GPU',
      architecture: 'Architecture',
      releaseDate: 'Date de sortie',
      memory: 'Mémoire',
      gpuModel: 'Modèle GPU',
      cudaCores: 'Cœurs CUDA',
      tgp: 'TGP',
      powerConsumption: 'Consommation électrique',
      fp8Performance: 'Performance FP8',
      fp16Performance: 'Performance FP16',
      fp32Performance: 'Performance FP32',
      fp64Performance: 'Performance FP64',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'Visualisation des performances GPU',
      benchmarkComparison: 'Comparaison des benchmarks GPU',
      filterGpus: 'Filtrer les GPU...',
      noResults: 'Aucun résultat.',
      percentLess: '{{percent}}% de moins',
      percentMore: '{{percent}}% de plus',
      percentFaster: '{{percent}}% plus rapide',
    },
    headlines: {
      title: 'Gros titres',
      desc: "Derniers gros titres et actualités de l'IA résumés à partir de diverses sources",
      viewAll: 'Voir tous les gros titres',
    },
    about: {
      title: 'À propos',
      desc: 'À propos de ce site',
    },
  },
  search: {
    contentType: 'Type de contenu',
    categories: 'Catégories',
    tags: 'Tags',
    datasetCategories: 'Catégories de jeux de données',
    organizations: 'Organisations',
    placeholder: 'Rechercher des titres, contenus, tags...',
    sortBy: 'Trier par',
    relevance: 'Pertinence',
    newest: 'Temps (Plus récent)',
    oldest: 'Temps (Plus ancien)',
    section: 'Section',
    createTime: 'Heure de création',
    displayTags: 'Afficher les tags',
    noTitle: 'Sans titre',
    uncategorized: 'Non catégorisé',
    last7Days: '7 derniers jours',
    last7To30Days: 'Il y a 7-30 jours',
    last30To90Days: 'Il y a 30-90 jours',
    last90DaysTo1Year: 'Il y a 90 jours - 1 an',
    over1Year: 'Plus d\'1 an',
    noResults: 'Aucun résultat trouvé',
    searchError: 'Une erreur s\'est produite lors de la recherche',
    loading: 'Recherche en cours...',
  },
  llmModelsPage: {
    modelColumn: 'Modèle',
    inputPrice: "Prix d'entrée",
    outputPrice: 'Prix de sortie',
    contextLength: 'Longueur de contexte',
    features: 'Fonctionnalités',
    description: 'Description',
    pricePerMillion: '${{price}}/M tokens',
    searchModels: 'Rechercher des modèles par nom',
    noData: '-',
  },
}

export default dictionary
