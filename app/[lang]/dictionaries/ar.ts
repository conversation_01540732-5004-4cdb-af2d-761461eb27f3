import { config } from '@/data/config'

const dictionary = {
  news: {
    label: 'الأخبار',
  },
  time: {
    days: 'ي',
    hours: 'س',
    minutes: 'د',
    seconds: 'ث',
    ended: 'انتهى',
  },
  timeObj: {
    days: '{{count}}ي',
    hours: '{{count}}س',
    minutes: '{{count}}د',
    seconds: '{{count}}ث',
  },
  theme: {
    system: 'النظام',
    dark: 'داكن',
    light: 'فاتح',
  },
  common: {
    title: 'HyperAI',
    slogan: 'بناء مستقبل الذكاء الاصطناعي',
    heroButton: 'البحث عن مجموعات البيانات والدروس...',
    publishedAt: 'نُشر في',
    moreLinks: {
      news: 'عرض المزيد من الأخبار',
      tutorials: 'عرض المزيد من الدروس',
      datasets: 'عرض المزيد من مجموعات البيانات',
      events: 'عرض المزيد من الفعاليات',
      wiki: 'عرض المزيد من الموسوعة',
      sota: 'عرض المزيد من SOTA',
      projects: 'عرض المزيد من المشاريع',
      papers: 'عرض المزيد من الأوراق البحثية',
    },
    relatedContent: '{{type}} ذات صلة',
    contentTypes: {
      news: 'الأخبار',
      posts: 'المنشورات',
      tutorials: 'الدروس',
      datasets: 'مجموعات البيانات',
      papers: 'الأوراق البحثية',
      events: 'الفعاليات',
      wiki: 'مدخلات الموسوعة',
    },
    login: 'تسجيل الدخول',
    logout: 'تسجيل الخروج',
    accountInfo: 'معلومات الحساب',
    previous: 'السابق',
    next: 'التالي',
  },
  tutorials: {
    metadata: {
      date: 'التاريخ',
      size: 'الحجم',
      author: 'المؤلف',
      tags: 'العلامات',
      latestVersion: 'أحدث إصدار',
    },
    runOnline: 'تشغيل هذا الدرس عبر الإنترنت',
    notebookPreview: 'معاينة الدفتر',
  },
  datasets: {
    metadata: {
      organization: 'المؤسسة',
      publishUrl: 'رابط النشر',
      license: 'الترخيص',
      categories: 'الفئات',
    },
    download: 'تنزيل مجموعة البيانات',
    magnetLink: 'رابط ماغنت',
    noDownload: 'التنزيلات غير متاحة',
    downloadHelp: 'مساعدة التنزيل',
    stats: {
      seeding: 'البذر',
      seedingTooltip: 'عدد الأقران الذين يقومون بالبذر في الساعة الماضية',
      downloading: 'التنزيل',
      downloadingTooltip: 'عدد الأقران الذين يقومون بالتنزيل في الساعة الماضية',
      completed: 'مكتمل',
      completedTooltip:
        'إجمالي عدد التنزيلات المكتملة، يتم تحديثها كل 24 ساعة، مع حساب التنزيلات التي وصلت إلى 100% فقط',
      traffic: 'حركة المرور المساهمة',
      totalDownloads: 'إجمالي التنزيلات',
    },
  },
  events: {
    controls: {
      deadline: 'الموعد النهائي للتقديم',
      results: 'إعلان النتائج',
      eventDate: 'تاريخ الفعالية',
      reverseOrder: 'ترتيب عكسي',
    },
    metadata: {
      date: 'التاريخ',
      venue: 'المكان',
      website: 'الموقع الإلكتروني',
      h5Index: 'مؤشر H5',
      ccfLevel: 'مستوى CCF',
    },
    timeline: {
      deadline: 'الموعد النهائي',
      deadlineDesc: 'الموعد النهائي لتقديم المقترحات أو الأوراق للمؤتمر أو الفعالية',
      results: 'إعلان النتائج',
      resultsDesc: 'يقوم المنظمون بإخطار المقدمين بقبول مقترحاتهم أو أوراقهم',
      eventStart: 'بدء الفعالية',
      eventStartDesc: 'الفترة التي يُعقد فيها المؤتمر أو الفعالية رسميًا',
      eventEnd: 'نهاية الفعالية',
      eventEndDesc: 'وقت انتهاء المؤتمر أو الفعالية',
    },
    visitWebsite: 'زيارة الموقع',
    conference: 'مؤتمر',
    ongoing: 'جاري الانعقاد',
    tbd: 'سيتم تحديده',
  },
  sota: {
    pageTitle: 'اختبارات الأداء للذكاء الاصطناعي',
    pageDescription: 'أحدث مقاييس أداء نماذج الذكاء الاصطناعي، اختبارات وحدات معالجة الرسومات، والأبحاث المتطورة',
    categories: {
      title: 'الفئات',
      description: 'تصفح المهام حسب الفئة',
      taskCount: 'مهام',
    },
    taskNotFound: 'المهمة غير موجودة',
    benchmarksList: 'قائمة المعايير القياسية',
    allBenchmarksForTask: 'جميع المعايير القياسية المتعلقة بهذه المهمة',
    noBenchmarkData: 'لا توجد بيانات معيارية متاحة لهذه المهمة',
    metrics: 'المقاييس',
    results: 'النتائج',
    resultsDescription: 'نتائج أداء النماذج المختلفة على هذا المعيار القياسي',
    comparisonTable: 'جدول المقارنة',
    modelName: 'اسم النموذج',
    noResultsData: 'لا توجد نتائج تفصيلية متاحة لهذا المعيار القياسي',
    taskList: {
      papers: ' ورقة بحثية',
      benchmarks: ' معيار قياسي',
      models: ' نموذج',
      noData: 'لا توجد بيانات',
      noTaskData: 'لا توجد بيانات متاحة للمهمة',
      searchPlaceholder: 'البحث عن المهام...',
      noResults: 'لم يتم العثور على نتائج',
      tryDifferentSearch: 'جرب مصطلح بحث مختلف',
    },
    modelBenchmark: {
      title: 'معايير أداء نماذج الذكاء الاصطناعي',
      description: 'مقاييس أداء نماذج الذكاء الاصطناعي الرئيسية عبر مهام متنوعة، مع عرض أحدث التقنيات',
    },
    gpuBenchmark: {
      title: 'معايير وحدات معالجة الرسومات',
      description:
        'أحدث تقييمات أداء الأجهزة والبرامج لوحدات معالجة الرسومات لمساعدتك في اتخاذ قرارات مستنيرة بشأن الأجهزة',
    },
    gpuBenchmarks: {
      softwarePerformance: 'أداء البرمجيات',
      hardwarePerformance: 'أداء الأجهزة',
      showChartView: 'عرض المخطط البياني',
      showTableView: 'عرض الجدول',
      performance: 'الأداء',
      requestThroughput: 'معدل إنتاجية الطلبات',
      outputTokenThroughput: 'معدل إنتاجية رموز الإخراج',
      totalTokenThroughput: 'إجمالي معدل إنتاجية الرموز',
      noData: 'لا توجد بيانات',
      noGpuData: 'لا توجد بيانات معيارية متاحة لوحدات معالجة الرسومات',
      noHardwareData: 'لا توجد بيانات معيارية متاحة لأجهزة وحدات معالجة الرسومات',
      noSoftwareData: 'لا توجد بيانات معيارية متاحة لبرامج وحدات معالجة الرسومات',
      gpuHardwareBenchmark: 'معيار أجهزة وحدات معالجة الرسومات',
      manufacturer: 'الشركة المصنعة',
      unknown: 'غير معروف',
      metric: 'المقياس',
      value: 'القيمة',
      model: 'النموذج',
      environment: 'البيئة',
      selectMetric: 'اختر المقياس',
      allMetrics: 'جميع المقاييس',
      maxContextLength: 'أقصى طول للسياق',
      maxSequences: 'أقصى عدد للتسلسلات',
    },
    benchmarkDetails: {
      viewDetails: 'عرض التفاصيل',
      bestModel: 'أفضل نموذج',
      searchPlaceholder: 'البحث عن المعايير...',
      noResults: 'لم يتم العثور على نتائج',
      tryDifferentSearch: 'جرب مصطلح بحث مختلف',
    },
    papers: {
      title: 'أحدث الأوراق البحثية',
      abstract: 'الملخص',
      description:
        'أوراق بحثية متطورة في مجال الذكاء الاصطناعي يتم تحديثها يوميًا لمساعدتك على مواكبة أحدث اتجاهات الذكاء الاصطناعي',
      noData: 'لا توجد بيانات',
      noAvailableData: 'لا توجد بيانات متاحة للأوراق البحثية',
      releaseDate: 'تاريخ النشر',
      retrievalDate: 'تاريخ الاسترجاع',
      viewDetails: 'عرض تفاصيل الورقة البحثية',
    },
  },
  sidebar: {
    newsletter: {
      title: 'اشترك في آخر تحديثاتنا',
      description: 'سنرسل لك أحدث التحديثات الأسبوعية إلى بريدك الإلكتروني في',
      time: 'الساعة التاسعة من صباح كل يوم اثنين',
      delivery: '',
      emailPlaceholder: 'أدخل عنوان البريد الإلكتروني',
      submitButton: 'اشتراك',
      provider: 'مدعوم بواسطة {{provider}}',
    },
    live: {
      loading: 'جاري تحميل المعلومات المباشرة...',
      streaming: 'مباشر الآن:',
      offline: 'Hyper.AI غير متصل بالبث المباشر بعد',
    },
    openbayes: {
      alt: 'OpenBayes - سجّل للحصول على قوة حوسبة مجانية',
    },
    tvm: {
      alt: 'موقع TVM باللغة الصينية',
    },
  },
  command: {
    nav: 'التنقل',
    searchResult: 'نتائج البحث',
    placeholder: 'أدخل كلمات مفتاحية للبحث...',
    label: 'البحث في الموقع...',
  },
  footer: {
    about: {
      title: 'حول',
      aboutUs: 'من نحن',
      datasetHelp: 'مساعدة مجموعات البيانات',
    },
    products: {
      title: 'المنتجات',
    },
    links: {
      title: 'روابط',
    },
  },
  nav: {
    home: {
      title: 'الرئيسية',
      desc: `زيارة الصفحة الرئيسية ${config.siteTitle}`,
    },
    news: {
      title: 'الأخبار',
      desc: 'أحدث الأخبار المتعلقة بالذكاء الاصطناعي',
    },
    tutorials: {
      title: 'الدروس',
      desc: 'مئات الدروس المختارة في مجال تعلم الآلة',
      longDesc:
        'لقد جمعنا مئات الدروس المختارة في مجال تعلم الآلة ونظمناها في شكل دفاتر Jupyter. يمكنك تشغيل هذه الدفاتر مجانًا على منصة تعلم الآلة الشريكة لنا، OpenBayes',
    },
    datasets: {
      title: 'مجموعات البيانات',
      desc: 'آلاف موارد مجموعات البيانات المفتوحة',
      longDesc:
        'لقد جمعنا آلاف موارد مجموعات البيانات المفتوحة وقدمنا خدمات التخزين المقابلة. متاحة للتنزيل مجانًا للمتخصصين ذوي الصلة',
    },
    events: {
      title: 'الفعاليات',
      desc: 'تتبع المؤتمرات الأكاديمية للذكاء الاصطناعي، ولا تفوّت أي موعد نهائي',
      longDesc: 'تتبع المؤتمرات الأكاديمية للذكاء الاصطناعي، ولا تفوّت أي موعد نهائي',
    },
    wiki: {
      title: 'الموسوعة',
      desc: 'مئات المدخلات المتعلقة بالذكاء الاصطناعي لمساعدتك على فهم الذكاء الاصطناعي',
      longDesc: 'لقد جمعنا مئات المدخلات ذات الصلة لمساعدتك على فهم "الذكاء الاصطناعي"',
    },
    sota: {
      title: 'SOTA',
      desc: 'أحدث مقاييس أداء نماذج الذكاء الاصطناعي، واختبارات معيارية لوحدات معالجة الرسومات، والأوراق البحثية المتطورة',
      longDesc:
        'أحدث مقاييس أداء نماذج الذكاء الاصطناعي، واختبارات معيارية لوحدات معالجة الرسومات، والأوراق البحثية المتطورة',
    },
    projects: {
      title: 'مفتوح المصدر',
      desc: 'مشاريع الترجمة مفتوحة المصدر التي يديرها هذا الموقع',
      tvm: {
        label: 'TVM باللغة الصينية',
        desc: 'Apache TVM هو إطار عمل للتجميع العميق للتعلم من البداية إلى النهاية لوحدات المعالجة المركزية، ووحدات معالجة الرسومات، ومسرعات تعلم الآلة',
      },
      triton: {
        label: 'Triton باللغة الصينية',
        desc: 'Triton هي لغة ومجمع للبرمجة المتوازية يمكن أن تعمل بأقصى إنتاجية على أجهزة وحدة معالجة الرسومات الحديثة',
      },
      vllm: {
        label: 'vLLM باللغة الصينية',
        desc: 'vLLM هي مكتبة سريعة وسهلة الاستخدام مصممة لاستدلال ونشر نماذج اللغة الكبيرة (LLMs)',
      },
    },
    search: {
      title: 'البحث',
      desc: 'البحث في جميع أنحاء الموقع',
    },
    llmModels: {
      title: 'نماذج LLM',
      desc: 'استكشاف تصنيفات نماذج اللغة الكبيرة المتاحة',
    },
    gpuLeaderboard: {
      title: 'لوحة الأداء GPU',
      desc: 'مقارنة مقاييس الأداء لأعباء عمل الذكاء الاصطناعي عبر وحدات معالجة الرسومات NVIDIA المختلفة',
      selectGpu: 'اختر وحدة معالجة الرسومات',
      architecture: 'البنية',
      releaseDate: 'تاريخ الإصدار',
      memory: 'الذاكرة',
      gpuModel: 'نموذج GPU',
      cudaCores: 'نوى CUDA',
      tgp: 'TGP',
      powerConsumption: 'استهلاك الطاقة',
      fp8Performance: 'أداء FP8',
      fp16Performance: 'أداء FP16',
      fp32Performance: 'أداء FP32',
      fp64Performance: 'أداء FP64',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'تصور أداء GPU',
      benchmarkComparison: 'مقارنة معايير GPU',
      filterGpus: 'تصفية وحدات معالجة الرسومات...',
      noResults: 'لا توجد نتائج.',
      percentLess: 'أقل بنسبة {{percent}}%',
      percentMore: 'أكثر بنسبة {{percent}}%',
      percentFaster: 'أسرع بنسبة {{percent}}%',
    },
    headlines: {
      title: 'العناوين الرئيسية',
      desc: 'أحدث عناوين الذكاء الاصطناعي والأخبار الملخصة من مصادر متنوعة',
      viewAll: 'عرض جميع العناوين الرئيسية',
    },
    about: {
      title: 'حول',
      desc: 'حول هذا الموقع',
    },
  },
  search: {
    contentType: 'نوع المحتوى',
    categories: 'الفئات',
    tags: 'العلامات',
    datasetCategories: 'فئات مجموعات البيانات',
    organizations: 'المؤسسات',
    placeholder: 'البحث في العناوين والمحتوى والعلامات...',
    sortBy: 'ترتيب حسب',
    relevance: 'الصلة',
    newest: 'الوقت (الأحدث)',
    oldest: 'الوقت (الأقدم)',
    section: 'القسم',
    createTime: 'وقت الإنشاء',
    displayTags: 'عرض العلامات',
    noTitle: 'بدون عنوان',
    uncategorized: 'غير مصنف',
    last7Days: 'آخر 7 أيام',
    last7To30Days: 'قبل 7-30 يومًا',
    last30To90Days: 'قبل 30-90 يومًا',
    last90DaysTo1Year: 'قبل 90 يومًا - سنة واحدة',
    over1Year: 'أكثر من سنة',
    noResults: 'لم يتم العثور على نتائج',
    searchError: 'حدث خطأ أثناء البحث',
    loading: 'جاري البحث...',
  },
  llmModelsPage: {
    modelColumn: 'النموذج',
    inputPrice: 'سعر الإدخال',
    outputPrice: 'سعر الإخراج',
    contextLength: 'طول السياق',
    features: 'الميزات',
    description: 'الوصف',
    pricePerMillion: '${{price}}/مليون رمز',
    searchModels: 'البحث عن النماذج حسب الاسم',
    noData: '-',
  },
}

export default dictionary
