import { config } from '@/data/config'

const dictionary = {
  news: {
    label: 'Neuigkeiten',
  },
  time: {
    days: 'T',
    hours: 'Std',
    minutes: 'Min',
    seconds: 'Sek',
    ended: 'Beendet',
  },
  timeObj: {
    days: '{{count}}T',
    hours: '{{count}}Std',
    minutes: '{{count}}Min',
    seconds: '{{count}}Sek',
  },
  theme: {
    system: 'System',
    dark: 'Dunkel',
    light: 'Hell',
  },
  common: {
    title: 'HyperAI',
    slogan: 'Die Zukunft der künstlichen Intelligenz aufbauen',
    heroButton: 'Datensätze, Tutorials durchsuchen…',
    publishedAt: 'Veröffentlicht am',
    moreLinks: {
      news: 'Mehr Neuigkeiten anzeigen',
      tutorials: 'Mehr Tutorials anzeigen',
      datasets: 'Mehr Datensätze anzeigen',
      events: 'Mehr Veranstaltungen anzeigen',
      wiki: 'Mehr Wiki anzeigen',
      sota: 'Mehr SOTA anzeigen',
      projects: 'Mehr Projekte anzeigen',
      papers: 'Mehr Forschungsarbeiten anzeigen',
    },
    relatedContent: 'Verwandte {{type}}',
    contentTypes: {
      news: 'Neuigkeiten',
      posts: 'Beiträge',
      tutorials: 'Tutorials',
      datasets: 'Datensätze',
      papers: 'Forschungsarbeiten',
      events: 'Veranstaltungen',
      wiki: 'Wiki-Einträge',
    },
    login: 'Anmelden',
    logout: 'Abmelden',
    accountInfo: 'Kontoinformationen',
    previous: 'Zurück',
    next: 'Weiter',
  },
  tutorials: {
    metadata: {
      date: 'Datum',
      size: 'Größe',
      author: 'Autor',
      tags: 'Tags',
      latestVersion: 'Neueste Version',
    },
    runOnline: 'Dieses Tutorial online ausführen',
    notebookPreview: 'Notebook-Vorschau',
  },
  datasets: {
    metadata: {
      organization: 'Organisation',
      publishUrl: 'Veröffentlichungs-URL',
      license: 'Lizenz',
      categories: 'Kategorien',
    },
    download: 'Datensatz-Download',
    magnetLink: 'Magnet-Link',
    noDownload: 'Downloads nicht verfügbar',
    downloadHelp: 'Download-Hilfe',
    stats: {
      seeding: 'Seeding',
      seedingTooltip: 'Anzahl der Peers, die in der letzten Stunde seeden',
      downloading: 'Herunterladen',
      downloadingTooltip: 'Anzahl der Peers, die in der letzten Stunde herunterladen',
      completed: 'Abgeschlossen',
      completedTooltip:
        'Gesamtzahl der abgeschlossenen Downloads, aktualisiert alle 24 Stunden, zählt nur Downloads, die 100% erreicht haben',
      traffic: 'Beigesteuerter Datenverkehr',
      totalDownloads: 'Gesamtdownloads',
    },
  },
  events: {
    controls: {
      deadline: 'Einreichungsfrist',
      results: 'Ergebnisbekanntgabe',
      eventDate: 'Veranstaltungsdatum',
      reverseOrder: 'Umkehren',
    },
    metadata: {
      date: 'Datum',
      venue: 'Veranstaltungsort',
      website: 'Website',
      h5Index: 'H5-Index',
      ccfLevel: 'CCF-Stufe',
    },
    timeline: {
      deadline: 'Frist',
      deadlineDesc:
        'Endgültige Frist für die Einreichung von Vorschlägen oder Arbeiten für die Konferenz oder Veranstaltung',
      results: 'Ergebnisse verkündet',
      resultsDesc: 'Organisatoren informieren die Einreicher, ob ihre Vorschläge oder Arbeiten angenommen wurden',
      eventStart: 'Veranstaltungsbeginn',
      eventStartDesc: 'Zeitraum, in dem die Konferenz oder Veranstaltung offiziell stattfindet',
      eventEnd: 'Veranstaltungsende',
      eventEndDesc: 'Endzeit der Konferenz oder Veranstaltung',
    },
    visitWebsite: 'Website besuchen',
    conference: 'Konferenz',
    ongoing: 'Laufend',
    tbd: 'TBD',
  },
  sota: {
    pageTitle: 'AI SOTA Benchmarks',
    pageDescription: 'Neueste KI-Modellleistungskennzahlen, GPU-Benchmarks und hochmoderne Forschungsarbeiten',
    categories: {
      title: 'Kategorien',
      description: 'Aufgaben nach Kategorie durchsuchen',
      taskCount: 'Aufgaben',
    },
    taskNotFound: 'Aufgabe nicht gefunden',
    benchmarksList: 'Benchmark-Liste',
    allBenchmarksForTask: 'Alle Benchmarks für diese Aufgabe',
    noBenchmarkData: 'Keine Benchmark-Daten für diese Aufgabe verfügbar',
    metrics: 'Metriken',
    results: 'Ergebnisse',
    resultsDescription: 'Leistungsergebnisse verschiedener Modelle zu diesem Benchmark',
    comparisonTable: 'Vergleichstabelle',
    modelName: 'Modellname',
    noResultsData: 'Keine detaillierten Ergebnisse für diesen Benchmark verfügbar',
    taskList: {
      papers: ' Forschungsarbeiten',
      benchmarks: ' Benchmarks',
      models: ' Modelle',
      noData: 'Keine Daten',
      noTaskData: 'Keine Aufgabendaten verfügbar',
      searchPlaceholder: 'Aufgaben durchsuchen...',
      noResults: 'Keine Ergebnisse gefunden',
      tryDifferentSearch: 'Versuchen Sie einen anderen Suchbegriff',
    },
    modelBenchmark: {
      title: 'KI-Modell-Leistungs-Benchmarks',
      description:
        'Leistungsmetriken der Mainstream-KI-Modelle bei verschiedenen Aufgaben, die den neuesten Stand der Technik präsentieren',
    },
    gpuBenchmark: {
      title: 'GPU-Benchmarks',
      description:
        'Neueste Evaluationen der GPU-Hardware- und Softwareleistung, um Ihnen bei fundierten Hardware-Entscheidungen zu helfen',
    },
    gpuBenchmarks: {
      softwarePerformance: 'Softwareleistung',
      hardwarePerformance: 'Hardwareleistung',
      showChartView: 'Diagrammansicht anzeigen',
      showTableView: 'Tabellenansicht anzeigen',
      performance: 'Leistung',
      requestThroughput: 'Anfragen-Durchsatz',
      outputTokenThroughput: 'Ausgabe-Token-Durchsatz',
      totalTokenThroughput: 'Gesamt-Token-Durchsatz',
      noData: 'Keine Daten',
      noGpuData: 'Keine GPU-Benchmark-Daten verfügbar',
      noHardwareData: 'Keine GPU-Hardware-Benchmark-Daten verfügbar',
      noSoftwareData: 'Keine GPU-Software-Benchmark-Daten verfügbar',
      gpuHardwareBenchmark: 'GPU-Hardware-Benchmark',
      manufacturer: 'Hersteller',
      unknown: 'Unbekannt',
      metric: 'Metrik',
      value: 'Wert',
      model: 'Modell',
      environment: 'Umgebung',
      selectMetric: 'Metrik auswählen',
      allMetrics: 'Alle Metriken',
      maxContextLength: 'Maximale Kontextlänge',
      maxSequences: 'Maximale Sequenzen',
    },
    benchmarkDetails: {
      viewDetails: 'Details anzeigen',
      bestModel: 'Bestes Modell',
      searchPlaceholder: 'Benchmarks durchsuchen...',
      noResults: 'Keine Ergebnisse gefunden',
      tryDifferentSearch: 'Versuchen Sie einen anderen Suchbegriff',
    },
    papers: {
      title: 'Neueste Forschungsarbeiten',
      abstract: 'Abstract',
      description:
        'Täglich aktualisierte wegweisende KI-Forschungsarbeiten, um mit den neuesten KI-Trends Schritt zu halten',
      noData: 'Keine Daten',
      noAvailableData: 'Keine Daten zu Forschungsarbeiten verfügbar',
      releaseDate: 'Veröffentlichungsdatum',
      retrievalDate: 'Abrufdatum',
      viewDetails: 'Details der Forschungsarbeit anzeigen',
    },
  },
  sidebar: {
    newsletter: {
      title: 'Abonnieren Sie unsere neuesten Updates',
      description: 'Wir werden die neuesten Updates der Woche in Ihren Posteingang liefern um',
      time: 'neun Uhr jeden Montagmorgen',
      delivery: '',
      emailPlaceholder: 'E-Mail-Adresse eingeben',
      submitButton: 'Abonnieren',
      provider: 'Unterstützt von {{provider}}',
    },
    live: {
      loading: 'Live-Informationen werden geladen…',
      streaming: 'Jetzt live:',
      offline: 'Hyper.AI streamt noch nicht',
    },
    openbayes: {
      alt: 'OpenBayes - Registrieren Sie sich für kostenlose Rechenleistung',
    },
    tvm: {
      alt: 'TVM Chinesische Seite',
    },
  },
  command: {
    nav: 'Navigation',
    searchResult: 'Suchergebnisse',
    placeholder: 'Suchbegriffe eingeben…',
    label: 'Seite durchsuchen…',
  },
  footer: {
    about: {
      title: 'Über',
      aboutUs: 'Über uns',
      datasetHelp: 'Datensatz-Hilfe',
    },
    products: {
      title: 'Produkte',
    },
    links: {
      title: 'Links',
    },
  },
  nav: {
    home: {
      title: 'Startseite',
      desc: `Besuchen Sie die ${config.siteTitle} Startseite`,
    },
    news: {
      title: 'Neuigkeiten',
      desc: 'Neueste KI-bezogene Nachrichten',
    },
    tutorials: {
      title: 'Tutorials',
      desc: 'Hunderte ausgewählte Maschinenlern-Tutorials',
      longDesc:
        'Wir haben Hunderte ausgewählte Maschinenlern-Tutorials gesammelt und in Form von Jupyter-Notebooks organisiert. Sie können diese Notebooks kostenlos auf unserer Partner-Maschinenlernplattform OpenBayes ausführen',
    },
    datasets: {
      title: 'Datensätze',
      desc: 'Tausende offene Datensatzressourcen',
      longDesc:
        'Wir haben Tausende von offenen Datensatzressourcen gesammelt und entsprechende Speicherdienste bereitgestellt. Kostenloser Download für relevante Fachleute',
    },
    events: {
      title: 'Veranstaltungen',
      desc: 'Verfolgen Sie KI-akademische Konferenzen, verpassen Sie nie eine Frist',
      longDesc: 'Verfolgen Sie KI-akademische Konferenzen, verpassen Sie nie eine Frist',
    },
    wiki: {
      title: 'Wiki',
      desc: 'Hunderte KI-bezogene Einträge, um Ihnen zu helfen, künstliche Intelligenz zu verstehen',
      longDesc:
        'Wir haben Hunderte von verwandten Einträgen zusammengestellt, um Ihnen zu helfen, "künstliche Intelligenz" zu verstehen',
    },
    sota: {
      title: 'SOTA',
      desc: 'Neueste KI-Modell-Leistungsmetriken, GPU-Benchmarks und wegweisende Forschungsarbeiten',
      longDesc: 'Neueste KI-Modell-Leistungsmetriken, GPU-Benchmarks und wegweisende Forschungsarbeiten',
    },
    projects: {
      title: 'Open Source',
      desc: 'Von dieser Seite gepflegte Open-Source-Übersetzungsprojekte',
      tvm: {
        label: 'TVM Chinesisch',
        desc: 'Apache TVM ist ein End-to-End-Deep-Learning-Compiler-Framework für CPUs, GPUs und ML-Beschleuniger',
      },
      triton: {
        label: 'Triton Chinesisch',
        desc: 'Triton ist eine Sprache und ein Compiler für parallele Programmierung, der mit maximaler Durchsatzleistung auf moderner GPU-Hardware laufen kann',
      },
      vllm: {
        label: 'vLLM Chinesisch',
        desc: 'vLLM ist eine schnelle und benutzerfreundliche Bibliothek, die für Inferenz und Bereitstellung von großen Sprachmodellen (LLMs) konzipiert ist',
      },
    },
    search: {
      title: 'Suche',
      desc: 'Seitenweite Suche',
    },
    llmModels: {
      title: 'LLM-Modelle',
      desc: 'Entdecken Sie verfügbare Ranglisten und Bewertungen großer Sprachmodelle',
    },
    gpuLeaderboard: {
      title: 'GPU-Rangliste',
      desc: 'Vergleichen Sie Leistungsmetriken für KI-Workloads auf verschiedenen NVIDIA-GPUs',
      selectGpu: 'GPU auswählen',
      architecture: 'Architektur',
      releaseDate: 'Veröffentlichungsdatum',
      memory: 'Speicher',
      gpuModel: 'GPU-Modell',
      cudaCores: 'CUDA-Kerne',
      tgp: 'TGP',
      powerConsumption: 'Stromverbrauch',
      fp8Performance: 'FP8-Leistung',
      fp16Performance: 'FP16-Leistung',
      fp32Performance: 'FP32-Leistung',
      fp64Performance: 'FP64-Leistung',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'GPU-Leistungsvisualisierung',
      benchmarkComparison: 'GPU-Benchmark-Vergleich',
      filterGpus: 'GPUs filtern...',
      noResults: 'Keine Ergebnisse.',
      percentLess: '{{percent}}% weniger',
      percentMore: '{{percent}}% mehr',
      percentFaster: '{{percent}}% schneller',
    },
    headlines: {
      title: 'Schlagzeilen',
      desc: 'Neueste KI-Schlagzeilen und Nachrichten aus verschiedenen Quellen zusammengefasst',
      viewAll: 'Alle Schlagzeilen anzeigen',
    },
    about: {
      title: 'Über',
      desc: 'Über diese Seite',
    },
  },
  search: {
    contentType: 'Inhaltstyp',
    categories: 'Kategorien',
    tags: 'Tags',
    datasetCategories: 'Datensatz-Kategorien',
    organizations: 'Organisationen',
    placeholder: 'Titel, Inhalte, Tags durchsuchen...',
    sortBy: 'Sortieren nach',
    relevance: 'Relevanz',
    newest: 'Zeit (Neueste)',
    oldest: 'Zeit (Älteste)',
    section: 'Abschnitt',
    createTime: 'Erstellungszeit',
    displayTags: 'Tags anzeigen',
    noTitle: 'Kein Titel',
    uncategorized: 'Nicht kategorisiert',
    last7Days: 'Letzte 7 Tage',
    last7To30Days: 'Vor 7-30 Tagen',
    last30To90Days: 'Vor 30-90 Tagen',
    last90DaysTo1Year: 'Vor 90 Tagen - 1 Jahr',
    over1Year: 'Über 1 Jahr',
    noResults: 'Keine Ergebnisse gefunden',
    searchError: 'Beim Suchen ist ein Fehler aufgetreten',
    loading: 'Suche läuft...',
  },
  llmModelsPage: {
    modelColumn: 'Modell',
    inputPrice: 'Eingabepreis',
    outputPrice: 'Ausgabepreis',
    contextLength: 'Kontextlänge',
    features: 'Funktionen',
    description: 'Beschreibung',
    pricePerMillion: '${{price}}/M Tokens',
    searchModels: 'Modelle nach Namen suchen',
    noData: '-',
  },
}

export default dictionary
