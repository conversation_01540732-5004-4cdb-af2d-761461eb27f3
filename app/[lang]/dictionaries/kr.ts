import { config } from '@/data/config'

const dictionary = {
  news: {
    label: '뉴스',
  },
  time: {
    days: '일',
    hours: '시간',
    minutes: '분',
    seconds: '초',
    ended: '종료됨',
  },
  timeObj: {
    days: '{{count}} 일',
    hours: '{{count}}시간',
    minutes: '{{count}}분',
    seconds: '{{count}}초',
  },
  theme: {
    system: '시스템 설정',
    dark: '다크 모드',
    light: '라이트 모드',
  },
  common: {
    title: 'HyperAI초신경',
    slogan: '학습, 이해, 실천, 커뮤니티와 함께 인공지능의 미래를 구축하다',
    heroButton: '데이터셋, 튜토리얼 및 AI 사례 검색',
    publishedAt: '게시일',
    moreLinks: {
      news: '더 많은 뉴스 보기',
      tutorials: '더 많은 튜토리얼 보기',
      datasets: '더 많은 데이터셋 보기',
      events: '더 많은 컨퍼런스 보기',
      wiki: '더 많은 백과사전 보기',
      sota: '더 많은 SOTA 보기',
      projects: '더 많은 오픈소스 프로젝트 보기',
      papers: '더 많은 논문 보기',
    },
    relatedContent: '관련 {{type}}',
    contentTypes: {
      news: '뉴스',
      posts: '게시물',
      tutorials: '튜토리얼',
      datasets: '데이터셋',
      papers: '논문',
      events: '컨퍼런스',
      wiki: '백과사전 항목',
    },
    login: '로그인',
    logout: '로그아웃',
    accountInfo: '계정 정보',
    previous: '이전',
    next: '다음',
  },
  tutorials: {
    metadata: {
      date: '날짜',
      size: '크기',
      author: '작성자',
      tags: '태그',
      latestVersion: '최신 버전',
    },
    runOnline: '온라인에서 이 튜토리얼 실행하기',
    notebookPreview: 'Notebook 미리보기',
  },
  datasets: {
    metadata: {
      organization: '기관',
      publishUrl: '발행 주소',
      license: '라이선스',
      categories: '카테고리',
    },
    download: '데이터셋 다운로드',
    magnetLink: '마그넷 링크',
    noDownload: '다운로드 불가',
    downloadHelp: '다운로드 도움말',
    stats: {
      seeding: '시딩',
      seedingTooltip: '최근 1시간 내 시딩 중인 클라이언트 수',
      downloading: '다운로드 중',
      downloadingTooltip: '최근 1시간 내 다운로드 중인 클라이언트 수',
      completed: '완료됨',
      completedTooltip: '총 완료된 다운로드 횟수, 24시간마다 업데이트, 100% 진행된 다운로드만 집계',
      traffic: '기여 트래픽',
      totalDownloads: '총 다운로드 횟수',
    },
  },
  events: {
    controls: {
      deadline: '마감일',
      results: '결과 발표',
      eventDate: '개최일',
      reverseOrder: '역순',
    },
    metadata: {
      date: '날짜',
      venue: '장소',
      website: '웹사이트',
      h5Index: 'h5 지수',
      ccfLevel: 'CCF 등급',
    },
    timeline: {
      deadline: '제출 마감',
      deadlineDesc: '컨퍼런스나 이벤트의 제안서 또는 논문 제출 마감 기한',
      results: '결과 발표',
      resultsDesc: '제출한 제안서나 논문의 채택 여부를 통보받는 시점',
      eventStart: '컨퍼런스 개최',
      eventStartDesc: '컨퍼런스나 이벤트가 공식적으로 열리는 기간',
      eventEnd: '컨퍼런스 종료',
      eventEndDesc: '컨퍼런스나 이벤트가 종료되는 시점',
    },
    visitWebsite: '웹사이트 방문',
    conference: '컨퍼런스',
    ongoing: '진행 중',
    tbd: '미정',
  },
  sota: {
    pageTitle: 'AI SOTA 벤치마크',
    pageDescription: '최신 인공지능 모델 성능 비교, GPU 벤치마크 및 최신 연구 논문',
    categories: {
      title: '카테고리',
      description: '카테고리별 작업 탐색',
      taskCount: '개 작업',
    },
    taskNotFound: '작업을 찾을 수 없음',
    benchmarksList: '벤치마크 목록',
    allBenchmarksForTask: '해당 작업에 관련된 모든 벤치마크 목록',
    noBenchmarkData: '해당 작업에 사용 가능한 벤치마크 데이터가 없음',
    metrics: '평가 지표',
    results: '평가 결과',
    resultsDescription: '이 벤치마크에서 각 모델의 성능 결과',
    comparisonTable: '비교 표',
    modelName: '모델 이름',
    noResultsData: '이 벤치마크에 대한 상세 결과 데이터가 없음',
    taskList: {
      papers: '개 논문',
      benchmarks: '개 벤치마크',
      models: '개 모델',
      noData: '데이터 없음',
      noTaskData: '사용 가능한 작업 데이터 없음',
      searchPlaceholder: '작업 검색...',
      noResults: '결과를 찾을 수 없습니다',
      tryDifferentSearch: '다른 검색어를 시도해보세요',
    },
    modelBenchmark: {
      title: 'AI 모델 성능 벤치마크',
      description: '주요 AI 모델의 다양한 작업에 대한 성능 지표 비교, 최첨단 기술 수준 제시',
    },
    gpuBenchmark: {
      title: 'GPU 벤치마크',
      description: '최신 GPU 하드웨어 및 소프트웨어 성능 평가로 하드웨어 선택에 도움 제공',
    },
    gpuBenchmarks: {
      softwarePerformance: '소프트웨어 성능',
      hardwarePerformance: '하드웨어 성능',
      showChartView: '차트 보기',
      showTableView: '표 보기',
      performance: '성능',
      requestThroughput: '요청 처리량',
      outputTokenThroughput: '출력 토큰 처리량',
      totalTokenThroughput: '전체 토큰 처리량',
      noData: '데이터 없음',
      noGpuData: '사용 가능한 GPU 벤치마크 데이터 없음',
      noHardwareData: '사용 가능한 GPU 하드웨어 벤치마크 데이터 없음',
      noSoftwareData: '사용 가능한 GPU 소프트웨어 벤치마크 데이터 없음',
      gpuHardwareBenchmark: 'GPU 하드웨어 벤치마크',
      manufacturer: '제조사',
      unknown: '알 수 없음',
      metric: '지표',
      value: '값',
      model: '모델',
      environment: '환경',
      selectMetric: '지표 선택',
      allMetrics: '모든 지표',
      maxContextLength: '최대 컨텍스트 길이',
      maxSequences: '최대 시퀀스 수',
    },
    benchmarkDetails: {
      viewDetails: '세부 정보 보기',
      bestModel: '최고 모델',
      searchPlaceholder: '벤치마크 검색...',
      noResults: '결과를 찾을 수 없습니다',
      tryDifferentSearch: '다른 검색어를 시도해보세요',
    },
    papers: {
      title: '최신 연구 논문',
      abstract: '초록',
      description: '매일 업데이트되는 최첨단 AI 연구 논문으로 최신 AI 트렌드를 파악하세요',
      noData: '데이터 없음',
      noAvailableData: '논문 데이터가 없습니다',
      releaseDate: '발행일',
      retrievalDate: '검색일',
      viewDetails: '논문 세부 정보 보기',
    },
  },
  sidebar: {
    newsletter: {
      title: '최신 정보 구독하기',
      description: '한국 시간',
      time: '매주 월요일 오전 9시',
      delivery: '에 이번 주의 최신 업데이트를 메일로 발송합니다',
      emailPlaceholder: '이메일 주소 입력',
      submitButton: '구독하기',
      provider: '이메일 서비스 제공: {{provider}}',
    },
    live: {
      loading: '라이브 정보 로딩 중...',
      streaming: '라이브 중:',
      offline: 'Hyper.AI 방송 중이 아님',
    },
    openbayes: {
      alt: 'OpenBayes 가입하고 무료 컴퓨팅 파워 받기',
    },
    tvm: {
      alt: 'TVM 한국어 사이트',
    },
  },
  command: {
    nav: '탐색',
    searchResult: '검색 결과',
    placeholder: '검색어를 입력하세요...',
    label: '전체 사이트 검색...',
  },
  footer: {
    about: {
      title: '소개',
      aboutUs: '회사 소개',
      datasetHelp: '데이터셋 도움말',
    },
    products: {
      title: '제품',
    },
    links: {
      title: '링크',
    },
  },
  nav: {
    home: {
      title: '홈',
      desc: `${config.siteTitle} 메인 페이지 방문`,
    },
    news: {
      title: '뉴스',
      desc: 'AI 관련 최신 뉴스',
    },
    tutorials: {
      title: '튜토리얼',
      desc: '수백 개의 정선된 기계 학습 관련 튜토리얼',
      longDesc:
        '저희는 수백 개의 정선된 기계 학습 관련 튜토리얼을 수집하여 Jupyter 노트북 형태로 정리했습니다. 저희 협력 기계 학습 플랫폼 OpenBayes에서 무료로 이 노트북을 실행할 수 있습니다',
    },
    datasets: {
      title: '데이터셋',
      desc: '수천 개의 공개 데이터셋 자원',
      longDesc:
        '저희는 수천 개의 공개 데이터셋 자원을 수집하고 정리하여 관련 저장 서비스를 제공합니다. 관련 종사자들에게 무료로 다운로드를 제공합니다',
    },
    events: {
      title: '컨퍼런스',
      desc: '인공지능 학술 컨퍼런스를 한 곳에서 추적하여 마감일을 놓치지 않도록',
      longDesc: '인공지능 학술 컨퍼런스를 한 곳에서 추적하여 마감일을 놓치지 않도록',
    },
    wiki: {
      title: '백과사전',
      desc: "수백 개의 관련 항목을 편집하여 여기서 '인공지능'을 이해할 수 있게",
      longDesc: "수백 개의 관련 항목을 편집하여 여기서 '인공지능'을 이해할 수 있게",
    },
    sota: {
      title: 'SOTA',
      desc: '최신 인공지능 모델 성능 지표, GPU 벤치마크 및 최첨단 논문',
      longDesc: '최신 인공지능 모델 성능 지표, GPU 벤치마크 및 최첨단 논문',
    },
    projects: {
      title: '오픈소스 프로젝트',
      desc: '본 사이트에서 유지 관리하는 오픈소스 번역 프로젝트',
      tvm: {
        label: 'TVM 한국어',
        desc: 'Apache TVM은 CPU, GPU 및 다양한 기계 학습 가속 칩용 엔드투엔드 딥러닝 컴파일 프레임워크입니다',
      },
      triton: {
        label: 'Triton 한국어',
        desc: 'Triton은 병렬 프로그래밍을 위한 언어 및 컴파일러로, 현대적인 GPU 하드웨어에서 최대 처리량으로 실행할 수 있습니다',
      },
      vllm: {
        label: 'vLLM 한국어',
        desc: 'vLLM은 대규모 언어 모델(LLM)의 추론 및 배포를 위해 설계된 빠르고 사용하기 쉬운 라이브러리입니다',
      },
    },
    search: {
      title: '전체 검색',
      desc: '전체 사이트 검색',
    },
    llmModels: {
      title: 'LLM 모델',
      desc: '사용 가능한 대규모 언어 모델 랭킹 탐색',
    },
    gpuLeaderboard: {
      title: 'GPU 랭킹',
      desc: '다양한 NVIDIA GPU에서 AI 워크로드의 성능 지표 비교',
      selectGpu: 'GPU 선택',
      architecture: '아키텍처',
      releaseDate: '출시일',
      memory: '메모리',
      gpuModel: 'GPU 모델',
      cudaCores: 'CUDA 코어',
      tgp: 'TGP',
      powerConsumption: '전력 소비',
      fp8Performance: 'FP8 성능',
      fp16Performance: 'FP16 성능',
      fp32Performance: 'FP32 성능',
      fp64Performance: 'FP64 성능',
      fp8: 'FP8',
      fp16: 'FP16',
      fp32: 'FP32',
      fp64: 'FP64',
      tflops: 'TFLOPS',
      performanceVisualization: 'GPU 성능 시각화',
      benchmarkComparison: 'GPU 벤치마크 비교',
      filterGpus: 'GPU 필터링...',
      noResults: '결과가 없습니다.',
      percentLess: '{{percent}}% 낮음',
      percentMore: '{{percent}}% 많음',
      percentFaster: '{{percent}}% 빠름',
    },
    headlines: {
      title: '헤드라인',
      desc: '다양한 출처에서 요약된 최신 AI 헤드라인 및 뉴스',
      viewAll: '모든 헤드라인 보기',
    },
    about: {
      title: '소개',
      desc: '이 사이트에 대하여',
    },
  },
  search: {
    contentType: '유형',
    categories: '게시물 카테고리',
    tags: '태그',
    datasetCategories: '데이터셋 카테고리',
    organizations: '기관',
    placeholder: '제목, 콘텐츠, 태그 검색...',
    sortBy: '정렬 기준',
    relevance: '관련성',
    newest: '시간 (최신)',
    oldest: '시간 (오래된)',
    section: '섹션',
    createTime: '생성 시간',
    displayTags: '태그 표시',
    noTitle: '제목 없음',
    uncategorized: '미분류',
    last7Days: '최근 7일',
    last7To30Days: '7-30일 전',
    last30To90Days: '30-90일 전',
    last90DaysTo1Year: '90일-1년 전',
    over1Year: '1년 이상',
    noResults: '결과를 찾을 수 없습니다',
    searchError: '검색 중 오류가 발생했습니다',
    loading: '검색 중...',
  },
  llmModelsPage: {
    modelColumn: '모델',
    inputPrice: '입력 가격',
    outputPrice: '출력 가격',
    contextLength: '컨텍스트 길이',
    features: '기능',
    description: '설명',
    pricePerMillion: '${{price}}/백만 토큰',
    searchModels: '이름으로 모델 검색',
    noData: '-',
  },
}

export default dictionary
