import { config } from '@/data/config'

import type { Locale } from '@/types'

import { getPostById } from '@/lib/api'
import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { elementToString } from '@/utils/elementToString'
import { encodeTitle } from '@/utils/encodeTitle'
import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import PageBody from '@/components/page-body'
import RelatedPosts from '@/components/related-posts'
import Sidebar from '@/components/sidebar'

type Params = Promise<{ lang: Locale; id: string }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'wiki', locale: params.lang })
  const dictionary = await getDictionary(params.lang)

  return {
    title: `${encodeTitle(data.title.rendered)}${config.siteTitleSplitter}${dictionary.nav.wiki.title}`,
    description: elementToString(data.excerpt.rendered),
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/wiki/${params.id}`),
  }
}

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params
  const data = await getPostById(Number(params.id), { type: 'wiki', locale: params.lang })
  const t = await getDictionary(params.lang)

  return (
    <div className='flex flex-col gap-8 md:flex-row'>
      <div className='md:w-2/3'>
        <div className='grid gap-4'>
          <h1>{encodeTitle(data.title.rendered)}</h1>

          <PageBody content={data.content.rendered} className='prose max-w-none' />

          <RelatedPosts postId={Number(params.id)} type='wiki' name={t.common.contentTypes.wiki} />
        </div>
      </div>

      <div className='md:w-1/3'>
        <Sidebar />
      </div>
    </div>
  )
}
