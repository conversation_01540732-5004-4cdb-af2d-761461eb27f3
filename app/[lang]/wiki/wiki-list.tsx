'use client'

import { useEffect, useState } from 'react'

import type { HyperApiPost } from '@/types'

import { WP_FIELDS_POST_LIST } from '@/lib/constants'

import { useTranslation } from '@/utils/i18n'
import useRecentPosts from '@/utils/useRecentPosts'

import { Loading } from '@/components/ui/loading'
import { Pagination } from '@/components/ui/pagination'
import { Skeleton } from '@/components/ui/skeleton'
import WikiItem from '@/components/wiki-item'

export default function WikiList() {
  const [posts, setPosts] = useState<HyperApiPost[]>([])
  const [pageIndex, setPageIndex] = useState(1)
  const [pageTotal, setPageTotal] = useState(0)
  const [currentTag] = useState<number | undefined>()

  const { t } = useTranslation()

  const { data, isLoading } = useRecentPosts({
    type: 'wiki',
    page: pageIndex,
    per_page: 24,
    _embed: true,
    tags: currentTag,
    _fields: WP_FIELDS_POST_LIST,
  })

  useEffect(() => {
    if (data?.json && data.json.length > 0) {
      setPosts(data.json)
    }

    if (data?.headers && data.headers.get('X-Wp-Totalpages')) {
      setPageTotal(Number(data.headers.get('X-Wp-Totalpages')))
    }
  }, [data])

  return (
    <>
      <div className='my-20 space-y-4'>
        <h1 className='font-logo leading-none'>{t.nav.wiki.title}</h1>
        <p className='text-fg/60 max-w-160 text-lg text-balance'>{t.nav.wiki.longDesc}</p>
      </div>

      <div className='grid'>
        <div className='relative'>
          {isLoading && (
            <div className='bg-bg/50 absolute top-0 left-0 z-10 flex h-full w-full items-center justify-center'>
              <Loading />
            </div>
          )}

          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 lg:gap-6'>
            {posts && posts.length > 0 && posts.map(post => <WikiItem data={post} key={post.id} />)}
          </div>

          {isLoading && posts.length === 0 && (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 lg:gap-6'>
              {[...new Array(24)].map((_, idx) => (
                <Skeleton key={idx} className='h-[108px] rounded-md' />
              ))}
            </div>
          )}
        </div>

        {pageTotal > 0 && (
          <div className='mt-8 flex justify-center'>
            <Pagination value={pageIndex} onPageChange={setPageIndex} total={pageTotal} disabled={isLoading} />
          </div>
        )}
      </div>
    </>
  )
}
