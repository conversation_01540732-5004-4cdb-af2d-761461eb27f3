import type { Locale } from '@/types'

import { BASE_URL } from '@/lib/constants'
import { getDictionary } from '@/lib/dictionaries-server'

import { generateCanonicalUrl } from '@/utils/generateCanonicalUrl'

import WikiList from './wiki-list'

type Params = Promise<{ lang: Locale }>

// Generate metadata
export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return {
    title: dictionary.nav.wiki.title,
    description: dictionary.nav.wiki.desc,
    metadataBase: new URL(BASE_URL),
    alternates: generateCanonicalUrl(`/wiki`),
  }
}

export default function WikiPage() {
  return (
    <div>
      <WikiList />
    </div>
  )
}
