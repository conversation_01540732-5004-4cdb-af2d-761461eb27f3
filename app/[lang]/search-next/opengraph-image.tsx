import { ImageResponse } from 'next/og'
import { config } from '@/data/config'

export const runtime = 'edge'
export const alt = `全站搜索 - ${config.siteTitle}`
export const contentType = 'image/png'
export const size = {
  width: 1200,
  height: 630,
}

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 64,
          background: '#f4f4f4',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          padding: 32,
        }}
      >
        <div
          style={{
            display: 'flex',
            fontSize: 48,
            fontWeight: 600,
            color: '#222',
            marginBottom: 16,
          }}
        >
          全站搜索
        </div>
        <div
          style={{
            display: 'flex',
            fontSize: 28,
            color: '#666',
          }}
        >
          {config.siteTitle}
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
