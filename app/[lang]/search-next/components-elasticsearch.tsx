'use client'

import { useEffect, useMemo, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  ErrorBoundary,
  Facet,
  Paging,
  PagingInfo,
  Results,
  ResultsPerPage,
  SearchBox,
  SearchProvider,
  Sorting,
  WithSearch,
} from '@elastic/react-search-ui'
import type { ResultViewProps } from '@elastic/react-search-ui-views'
import ElasticsearchAPIConnector from '@elastic/search-ui-elasticsearch-connector'

import type { SearchConfig, SearchDictionary, SearchResult } from '@/types/elasticsearch'

import { ES_CONFIG, getIndexName } from '@/lib/elasticsearch/config'

import { useTranslation } from '@/utils/i18n'
import useParamLang from '@/utils/useParamLang'

import { SearchLayout } from '@/components/elasticsearch/search-layout'
import { SearchResultItem } from '@/components/elasticsearch/search-result'
import { Alert } from '@/components/ui/alert'
import { Card } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

import '@/styles/elasticsearch.css'

// Create Elasticsearch connector
const createConnector = (indexName: string) => {
  return new ElasticsearchAPIConnector({
    host: ES_CONFIG.host,
    index: indexName,
    apiKey: ES_CONFIG.apiKey,
  })
}

// Create search configuration
const createSearchConfig = (indexName: string, dictionary: SearchDictionary): SearchConfig => {
  const connector = createConnector(indexName)

  return {
    searchQuery: {
      search_fields: ES_CONFIG.searchFields,
      result_fields: {
        id: { raw: {} },
        title: { snippet: { size: 100, fallback: true } },
        abstract: { snippet: { size: 200, fallback: true } },
        content: { snippet: { size: 300, fallback: true } },
        section: { raw: {} },
        tags: { raw: {} },
        tags_facts: { raw: {} },
        createtime: { raw: {} },
        createstamp: { raw: {} },
        link: { raw: {} },
      },
      disjunctiveFacets: ['section', 'tags_facts.keyword'],
      facets: {
        'section': {
          type: 'value',
          size: 20,
        },
        'tags_facts.keyword': {
          type: 'value',
          size: 30,
        },
        'createtime': {
          type: 'range',
          ranges: [
            { from: 'now-7d', name: dictionary.search.last7Days },
            { from: 'now-30d', to: 'now-7d', name: dictionary.search.last7To30Days },
            { from: 'now-90d', to: 'now-30d', name: dictionary.search.last30To90Days },
            { from: 'now-1y', to: 'now-90d', name: dictionary.search.last90DaysTo1Year },
            { to: 'now-1y', name: dictionary.search.over1Year },
          ],
        },
      },
    },
    autocompleteQuery: {
      results: {
        resultsPerPage: ES_CONFIG.autocomplete.resultsPerPage,
        search_fields: {
          title: { weight: 3 },
          tags: { weight: 2 },
        },
        result_fields: {
          id: { raw: {} },
          title: { snippet: { size: 100, fallback: true } },
          section: { raw: {} },
          link: { raw: {} },
        },
      },
      suggestions: {
        types: {
          documents: { fields: ['suggest_title'] },
          tags: { fields: ['suggest_tags'] },
        },
        size: 6,
      },
    },
    apiConnector: connector,
    alwaysSearchOnInitialLoad: false,
  }
}

// Custom Results component wrapper
function SearchResults({ dictionary, locale }: { dictionary: SearchDictionary; locale: string }) {
  return (
    <Results
      resultView={(props: ResultViewProps) => (
        <SearchResultItem
          result={props.result as SearchResult}
          locale={locale}
          labels={{
            section: dictionary.search.section,
            createTime: dictionary.search.createTime,
            tags: dictionary.search.tags,
            tagsFacts: dictionary.search.displayTags,
            noTitle: dictionary.search.noTitle,
            uncategorized: dictionary.search.uncategorized,
          }}
        />
      )}
    />
  )
}

// Custom styled search components
const StyledSearchBox = ({ dictionary }: { dictionary: SearchDictionary }) => (
  <div className='w-full'>
    <SearchBox
      autocompleteMinimumCharacters={ES_CONFIG.autocomplete.minChars}
      autocompleteResults={{
        sectionTitle: dictionary.search.searchResult || 'Search Results',
        titleField: 'title',
        urlField: 'link',
        shouldTrackClickThrough: true,
      }}
      autocompleteSuggestions={true}
      debounceLength={ES_CONFIG.autocomplete.debounceLength}
      inputProps={{
        placeholder: dictionary.search.placeholder,
        className: 'w-full px-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary',
      }}
    />
  </div>
)

// Main search app component
function SearchApp({
  dictionary,
  locale,
  indexSuffix,
}: {
  dictionary: SearchDictionary
  locale: string
  indexSuffix: string | null
}) {
  const { lang } = useParamLang()
  const indexName = getIndexName(lang, indexSuffix || undefined)

  // Memoize the config to prevent recreating on every render
  const config = useMemo(() => createSearchConfig(indexName, dictionary), [indexName, dictionary])

  return (
    <SearchProvider config={config}>
      <WithSearch mapContextToProps={({ wasSearched, isLoading, error }) => ({ wasSearched, isLoading, error })}>
        {(props: { wasSearched?: boolean; isLoading?: boolean; error?: string }) => {
          const { wasSearched, isLoading, error } = props
          return (
            <ErrorBoundary>
              <SearchLayout
                header={<StyledSearchBox dictionary={dictionary} />}
                sideContent={
                  wasSearched && (
                    <>
                      <Card className='p-4'>
                        <Sorting
                          label={dictionary.search.sortBy}
                          sortOptions={[
                            { name: dictionary.search.relevance, value: '', direction: '' },
                            { name: dictionary.search.newest, value: 'createstamp', direction: 'desc' },
                            { name: dictionary.search.oldest, value: 'createstamp', direction: 'asc' },
                          ]}
                        />
                      </Card>

                      <Card className='p-4'>
                        <h3 className='mb-3 font-semibold'>{dictionary.search.section}</h3>
                        <Facet field='section' label='' filterType='any' isFilterable={true} />
                      </Card>

                      <Card className='p-4'>
                        <h3 className='mb-3 font-semibold'>{dictionary.search.displayTags}</h3>
                        <Facet field='tags_facts.keyword' label='' filterType='any' isFilterable={true} />
                      </Card>

                      <Card className='p-4'>
                        <h3 className='mb-3 font-semibold'>{dictionary.search.createTime}</h3>
                        <Facet field='createtime' label='' filterType='any' />
                      </Card>
                    </>
                  )
                }
                bodyContent={
                  <>
                    {error && (
                      <Alert tint='danger' className='mb-4'>
                        {dictionary.search.searchError}
                      </Alert>
                    )}
                    {isLoading ? (
                      <div className='space-y-4'>
                        {[...Array(5)].map((_, i) => (
                          <Card key={i} className='p-6'>
                            <Skeleton className='mb-4 h-6 w-3/4' />
                            <Skeleton className='mb-2 h-4 w-full' />
                            <Skeleton className='h-4 w-5/6' />
                          </Card>
                        ))}
                      </div>
                    ) : (
                      wasSearched && <SearchResults dictionary={dictionary} locale={locale} />
                    )}
                  </>
                }
                bodyHeader={
                  wasSearched &&
                  !isLoading && (
                    <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
                      <PagingInfo />
                      <ResultsPerPage options={ES_CONFIG.resultsPerPage.options} />
                    </div>
                  )
                }
                bodyFooter={wasSearched && !isLoading && <Paging />}
              />
            </ErrorBoundary>
          )
        }}
      </WithSearch>
    </SearchProvider>
  )
}

// Main export component
export function ElasticsearchSearch() {
  const searchParams = useSearchParams()
  const { lang } = useParamLang()
  const { t } = useTranslation()
  const [indexSuffix, setIndexSuffix] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    const suffix = searchParams.get('suffix')
    if (suffix) {
      setIndexSuffix(suffix)
    }
  }, [searchParams])

  if (!mounted) {
    return (
      <div className='flex min-h-[400px] items-center justify-center'>
        <div className='text-center'>
          <Skeleton className='mx-auto mb-4 h-8 w-48' />
          <Skeleton className='mx-auto h-4 w-32' />
        </div>
      </div>
    )
  }

  // Check if API key is configured
  if (!ES_CONFIG.apiKey) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Alert tint='danger'>
          Elasticsearch API key is not configured. Please set NEXT_PUBLIC_ES_API_KEY environment variable.
        </Alert>
      </div>
    )
  }

  return (
    <div className='w-full'>
      <SearchApp dictionary={t} locale={lang} indexSuffix={indexSuffix} />

      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && indexSuffix && (
        <div className='bg-muted text-muted-foreground fixed bottom-4 left-4 rounded p-2 text-xs'>
          Index: {getIndexName(lang, indexSuffix)}
        </div>
      )}
    </div>
  )
}
