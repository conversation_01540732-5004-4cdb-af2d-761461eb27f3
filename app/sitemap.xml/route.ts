import { BASE_URL } from '@/lib/constants'

import { generateSitemaps as getSitemapsDatasets } from '../[lang]/datasets/sitemap'
import { generateSitemaps as getSitemapsEvents } from '../[lang]/events/sitemap'
import { generateSitemaps as getSitemapsNews } from '../[lang]/news/sitemap'
import { generateSitemaps as getSitemapsTutorials } from '../[lang]/tutorials/sitemap'
import { generateSitemaps as getSitemapsWiki } from '../[lang]/wiki/sitemap'

// This disables SSG
export const runtime = 'edge'
export const dynamic = 'force-dynamic'
export const revalidate = 3600 // cache for 1 hour

// https://github.com/vercel/next.js/discussions/61025
function getFileName(id?: number) {
  if (id == null) {
    return 'sitemap.xml'
  }
  return `sitemap/${id}.xml`
}

function getSitemap(path: string, id?: number) {
  const loc = `${BASE_URL}/${path}/${getFileName(id)}`

  return /* XML */ `  <sitemap>
    <loc>${loc}</loc>
  </sitemap>
`
}

function getSitemaps(ids: { id: number }[], path: string) {
  return ids.map(({ id }) => getSitemap(path, id)).join('')
}

export async function GET() {
  const xml = /* XML */ `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${getSitemaps(await getSitemapsNews(), 'news')}
${getSitemaps(await getSitemapsDatasets(), 'datasets')}
${getSitemaps(await getSitemapsEvents(), 'events')}
${getSitemaps(await getSitemapsTutorials(), 'tutorials')}
${getSitemaps(await getSitemapsWiki(), 'wiki')}
</sitemapindex>
`

  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  })
}
