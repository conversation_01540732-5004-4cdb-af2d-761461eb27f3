import { NextResponse, type NextFetch<PERSON>vent, type NextRequest } from 'next/server'
import Negotiator from 'negotiator'
import { transformMiddlewareRequest } from '@axiomhq/nextjs'
import { match } from '@formatjs/intl-localematcher'

import { logger } from '@/lib/axiom/server'
import { browserLocales, defaultLocale, LANGUAGE_COOKIE_NAME, localeMapping, routeLocales } from '@/lib/locales'

import { getClientIp } from '@/utils/getClientIp'

// Get the preferred locale from the request headers
function getLocale(request: NextRequest) {
  // First check if there's a cookie with a preferred locale
  const cookieLocale = request.cookies.get(LANGUAGE_COOKIE_NAME)?.value

  // If cookie exists and it's one of our supported locales, use it
  if (cookieLocale && routeLocales.includes(cookieLocale)) {
    return cookieLocale
  }

  // Otherwise, fall back to browser detection
  // Get the accepted languages from the headers
  const acceptedLanguages = request.headers.get('accept-language')
  // console.log(`acceptedLanguages`, acceptedLanguages)

  // Create a new Negotiator instance with the request headers
  let languages = new Negotiator({
    headers: {
      'accept-language': acceptedLanguages || 'zh',
    },
  }).languages()
  // console.log(`languages`, languages)

  // Match the preferred locale using intl-localematcher
  const browserLocale = match(languages, browserLocales, 'zh')
  // Convert to our route locale
  return localeMapping[browserLocale] || defaultLocale
}

// https://nextjs.org/docs/app/building-your-application/routing/internationalization
export function middleware(request: NextRequest, event: NextFetchEvent) {
  // Axiom logger
  // https://axiom.co/docs/send-data/nextjs#use-%40axiomhq%2Fnextjs-library
  const [message, data] = transformMiddlewareRequest(request)
  logger.info(message, {
    ...data,
    headers: {
      'ip': getClientIp(request),
      'x-forwarded-for': request.headers.get('x-forwarded-for'),
      'x-real-ip': request.headers.get('x-real-ip'),
      'cf-connecting-ip': request.headers.get('cf-connecting-ip'),
    },
  })

  // Check if there is any supported locale in the pathname
  const { pathname } = request.nextUrl

  // Check if the pathname has a locale to prevent redirect loop
  const pathnameHasLocale = routeLocales.some(locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`)

  const localeFromPath = pathname.split('/')[1]

  if (pathnameHasLocale && localeFromPath) {
    // If the pathname has a locale, extract it and set the cookie
    // Only return the response if the cookie already exists with the right value
    const existingCookie = request.cookies.get(LANGUAGE_COOKIE_NAME)
    if (existingCookie?.value === localeFromPath) {
      return
    }

    // Otherwise set the cookie and return the response
    const resp = NextResponse.next()
    resp.cookies.set(LANGUAGE_COOKIE_NAME, localeFromPath, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: 'lax',
    })

    return resp
  }

  // Redirect if there is no locale
  const locale = getLocale(request)
  // console.log(`locale`, locale)

  request.nextUrl.pathname = `/${locale}${pathname}`

  // Create the redirect response
  const redirectResp = NextResponse.redirect(request.nextUrl)

  // Also set the cookie on redirect
  redirectResp.cookies.set(LANGUAGE_COOKIE_NAME, locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    sameSite: 'lax',
  })

  // Flush the logger
  event.waitUntil(logger.flush())

  return redirectResp
}

export const config = {
  matcher: [
    // Skip all internal paths (_next)
    '/((?!_next|api|static|ai-providers|favicon.ico|logo.svg|robots.txt|sitemap.xml).*)',
    // Optional: only run on root (/) URL
    // '/'
  ],
}
