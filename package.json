{"name": "hyperai-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "analyze": "ANALYZE=true next build", "generate:gpu-data": "bun scripts/convert-gpu-leaderboard.ts"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@algolia/autocomplete-core": "^1.19.2", "@algolia/autocomplete-js": "^1.19.2", "@algolia/autocomplete-plugin-query-suggestions": "^1.19.2", "@algolia/autocomplete-plugin-recent-searches": "^1.19.2", "@algolia/autocomplete-shared": "^1.19.2", "@algolia/autocomplete-theme-classic": "^1.19.2", "@apollo/client": "^3.13.9", "@axiomhq/js": "^1.3.1", "@axiomhq/logging": "^0.1.4", "@axiomhq/nextjs": "^0.1.4", "@axiomhq/react": "^0.1.4", "@bprogress/next": "^3.2.12", "@elastic/react-search-ui": "^1.24.2", "@elastic/react-search-ui-views": "^1.24.2", "@elastic/search-ui-elasticsearch-connector": "^1.24.2", "@formatjs/intl-localematcher": "^0.6.1", "@logto/next": "^4.2.5", "@next/third-parties": "^15.4.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@statsig/react-bindings": "^3.21.1", "@statsig/session-replay": "^3.21.1", "@statsig/web-analytics": "^3.21.1", "@tabler/icons-react": "^3.34.1", "@tanstack/react-table": "^8.21.3", "@wordpress/html-entities": "^4.28.0", "ai": "^4.3.19", "algoliasearch": "^5.35.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-flag-icons": "^1.5.19", "dayjs": "^1.11.13", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "entities": "^6.0.1", "graphql-request": "^7.2.0", "html-react-parser": "^5.2.6", "instantsearch.js": "^4.79.2", "kladenets": "1.1.3", "motion": "^12.23.12", "negotiator": "^1.0.0", "next": "^15.4.6", "next-plausible": "^3.12.4", "next-themes": "^0.4.6", "pigeon-maps": "^0.22.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-flip-toolkit": "^7.2.4", "react-instantsearch": "^7.16.2", "react-instantsearch-nextjs": "^0.4.9", "react-instantsearch-router-nextjs": "^7.16.2", "react-ipynb-renderer": "2.2.4", "recharts": "^2.15.4", "server-only": "^0.0.1", "sharp": "^0.34.3", "sonner": "^2.0.7", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "uniqolor": "^1.1.1", "zustand": "^5.0.7"}, "devDependencies": {"@elastic/elasticsearch": "^9.1.0", "@ianvs/prettier-plugin-sort-imports": "^4.6.0", "@sparanoid/types": "^1.1.94", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@types/gtag.js": "^0.0.20", "@types/negotiator": "^0.6.4", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.4.6", "jsdom": "^26.1.0", "next-bundle-analyzer": "^0.6.8", "openapi-typescript": "^7.8.0", "postcss": "^8.5.6", "postcss-simple-vars": "^7.0.1", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "wp-types": "^4.68.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}