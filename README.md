# Hyper.AI Next

Hyper.AI running on Next.js

## Getting Started

```bash
pnpm install
pnpm run dev
```

## Update Scraper API Schema

```bash
pnpx openapi-typescript https://web-api.hyper.ai/openapi.json -o ./types/scraper.ts
```

## Data Processing Utilities

### Generate GPU Benchmark Data

To convert GPU benchmark data from CSV to TypeScript:

```bash
pnpm generate:gpu-data
```

This generates a structured TypeScript file with GPU benchmark data for use in the application.

# HyperAI Tags Utility

This repository contains utilities for managing tags for the HyperAI website.

## Scripts

### Find Missing Tags

The `find-missing-tags.ts` script compares tags from the JSON file with those in the TypeScript file and generates a new file with the missing tags.

#### Usage

```bash
# Make the script executable
chmod +x scripts/find-missing-tags.ts

# Run the script for datasets (default if no type is specified)
./scripts/find-missing-tags.ts

# Or specify a tag type
./scripts/find-missing-tags.ts datasets
./scripts/find-missing-tags.ts events
./scripts/find-missing-tags.ts tutorials
```

### Process All Tag Types at Once

The `get-all-missing-tags.ts` script runs the find-missing-tags script for all tag types (datasets, events, tutorials).

```bash
# Make the script executable
chmod +x scripts/get-all-missing-tags.ts

# Run the script
./scripts/get-all-missing-tags.ts
```

This will process all tag types and generate corresponding missing tags files:

- `data/missing-tags-datasets.ts`
- `data/missing-tags-events.ts`
- `data/missing-tags-tutorials.ts`

#### How it Works

The scripts:

1. Read tags from JSON files (`data/tags-*.json`) and TypeScript files (`data/tags-*.ts`)
2. Find tags that exist in JSON but not in TS
3. Generate new files with the missing tags in the correct format

The output format matches the existing tags format in the original TS files, with empty translation fields ready to be filled in.

#### Output Example

```typescript
// Generated missing tags for tutorials from data/tags-tutorials.json
import { TranslatedTag } from '@/types'

export const missingTagsTutorials: TranslatedTag[] = [
  {
    LangChain: {
      cn: 'LangChain',
      en: '',
      ja: '',
      kr: '',
      ar: '',
      fr: '',
      de: '',
    },
  },
  // ... more tags
]
```

### GPU Leaderboard Converter

The `convert-gpu-leaderboard.ts` script converts GPU leaderboard data from CSV format to a structured TypeScript file using the xlsx library.

#### Purpose

This script takes the raw GPU leaderboard data from `data/gpu-leaderboard.csv` and converts it into a well-structured TypeScript object in `data/gpu-leaderboard.ts`. The conversion process:

1. Reads the CSV file from the data directory using SheetJS (xlsx)
2. Parses the CSV data into a JavaScript object structure
3. Cleans and normalizes field names and values to handle special characters and formatting issues
4. Automatically generates a TypeScript interface based on the actual data structure
5. Creates a type-safe array of GPU leaderboard objects

#### Dependencies

- xlsx (SheetJS): Used for robust CSV parsing

#### Usage

```bash
# Make the script executable
chmod +x scripts/convert-gpu-leaderboard.ts

# Run the script directly
./scripts/convert-gpu-leaderboard.ts

# Or use the npm script
pnpm generate:gpu-data
```

#### Output

The script generates `data/gpu-leaderboard.ts` with the following structure:

```typescript
// Generated from data/gpu-benchmark.csv
export interface GPULeaderboard {
  'GPU': string | null
  'Release Date': string | null
  'Architecture': string | null
  'CUDA Cores': string | null
  'GPU memory': string | null
  'Total Graphics Power': string | null
  'FP8': string | null
  'FP16': string | null
  'FP32': string | null
  'FP64': string | null
}

export const gpuLeaderboard: GPULeaderboard[] = [
  {
    'GPU': 'NVIDIA Tesla V100 PCIe 16 GB',
    'Release Date': '2017.06',
    'Architecture': 'NVIDIA Volta',
    // ... other properties
  },
  // ... other GPU entries
]
```

## Development

After identifying missing tags, you should:

1. Add appropriate translations for each tag
2. Merge the content from missing tags files into their respective main tag files
