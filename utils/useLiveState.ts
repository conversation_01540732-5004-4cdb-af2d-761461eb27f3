import useSWR, { type Fetcher } from 'swr'
import type { BilibiliInternal } from '@sparanoid/types'

import { WORKERS_BASE } from '@/lib/constants'

import convertBilibiliUid from '@/utils/convertBilibiliUid'

const fetcher: Fetcher<BilibiliInternal.HTTPS.Prod.GetInfoByRoom> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching live status')
  }
  return response.json()
}

function useLiveState(uid: number | string) {
  const convertedUid = convertBilibiliUid(uid)

  const { data, error } = useSWR(
    // convertedUid ? `/api/live`: null,
    convertedUid ? `${WORKERS_BASE}/bilibili/room-info/${convertedUid}` : null,
    fetcher,
    { refreshInterval: 180 * 1000 }
  )

  return {
    data: data,
    isLoading: !error && !data,
    isError: error,
  }
}

export default useLiveState
