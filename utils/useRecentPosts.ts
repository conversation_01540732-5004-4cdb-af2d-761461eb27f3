import { useParams } from 'next/navigation'
import useSWR from 'swr'

import type { HyperApiPost, RecentPostsProps } from '@/types'

import { API_PROXY_BASE } from '@/lib/constants'
import { defaultLocale } from '@/lib/locales'

import fetcher from '@/utils/swrFetcherWithHeaders'

function useRecentPosts<T = HyperApiPost[]>(options: RecentPostsProps & Record<string, any>) {
  const params = useParams<{ lang: string }>()
  const resolvedApiPrefix = params?.lang && params.lang !== defaultLocale ? `/${params.lang}` : ''

  const opts = { ...options }
  delete opts.type

  const filteredParams: { [key: string]: any } = {}

  // Filter out keys with undefined values
  for (const key in opts) {
    if (opts[key] !== undefined) {
      filteredParams[key] = opts[key]
    }
  }

  // Create a URLSearchParams instance
  const queries = new URLSearchParams(filteredParams)

  // Convert parameters to a query string
  const queryString = queries.toString()

  const url = `${API_PROXY_BASE}${resolvedApiPrefix}/wp-json/wp/v2/${options.type}?${queryString}`

  const { data, error, isLoading, isValidating } = useSWR<{
    headers: Headers
    json: T
  }>(url, fetcher)

  return {
    data,
    isLoading,
    isValidating,
    isError: error,
  }
}

export default useRecentPosts
