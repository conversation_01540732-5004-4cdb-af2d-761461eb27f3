/**
 * Titlizes and capitalizes words in a string according to title case rules.
 *
 * Title case rules:
 * - First and last words are always capitalized
 * - Articles (a, an, the), coordinating conjunctions (and, but, for, etc.),
 *   and prepositions shorter than 5 letters (in, to, of, etc.) are lowercase
 *   unless they are the first or last word
 * - All other words are capitalized
 * - Words that were originally capitalized or are specific names (like NVIDIA) preserve their original capitalization
 *
 * @param input - The string to titlize
 * @returns The titlized string
 */
export function titlize(input: string): string {
  // Treat words with non-alphabetic tech markers as technical identifiers and
  // preserve their original capitalization (e.g., llama.cpp+open-webui, gpt-oss-20b)
  const isTechnicalTerm = (word: string): boolean => {
    if (!word) return false
    // Contains digits or common technical separators/symbols
    if (/[0-9]/.test(word)) return true
    if (/[\.+_\/@#]/.test(word)) return true
    // Preserve lowercase kebab-case identifiers (e.g., vite-plugin-react)
    if (/^[a-z0-9]+(?:-[a-z0-9]+)+$/.test(word)) return true
    // Keep hyphenated tokens only if they also contain digits (e.g., gpt-oss-20b)
    if (/-/.test(word) && /[0-9]/.test(word)) return true
    return false
  }

  // List of words that should not be capitalized (unless they are first or last)
  const lowercaseWords = [
    'a',
    'an',
    'the',
    'and',
    'but',
    'or',
    'nor',
    'for',
    'yet',
    'so',
    'at',
    'by',
    'for',
    'in',
    'of',
    'off',
    'on',
    'out',
    'to',
    'up',
    'via',
  ]

  // Split the input into words without lowercasing first to preserve original capitalization
  const words = input.split(' ')
  const originalWords = [...words] // Keep track of original words

  // If there are no words, return empty string
  if (words.length === 0) return ''

  return words
    .map((word, index) => {
      const originalWord = originalWords[index] ?? word

      // Check if the word has non-standard capitalization (like vLLM, iPhone, etc.)
      const hasCustomCapitalization =
        originalWord &&
        (originalWord.charAt(0) === originalWord.charAt(0).toUpperCase() || // First letter capital
          originalWord === originalWord.toUpperCase() || // All uppercase
          /[a-z][A-Z]/.test(originalWord) || // Contains lowercase followed by uppercase (like vLLM, iPhone)
          /[A-Z][A-Z].*[a-z]/.test(originalWord)) // Contains multiple uppercase letters with lowercase (like SQLite)

      // Preserve words that look like technical identifiers or have custom capitalization
      if (isTechnicalTerm(originalWord) || hasCustomCapitalization) {
        return originalWord
      }

      const lowercasedWord = word.toLowerCase()

      // Always capitalize first and last words
      if (index === 0 || index === words.length - 1) {
        return capitalizeFirstLetter(lowercasedWord)
      }

      // Keep lowercase words lowercase
      if (lowercaseWords.includes(lowercasedWord)) {
        return lowercasedWord
      }

      // Capitalize all other words
      return capitalizeFirstLetter(lowercasedWord)
    })
    .join(' ')
}

/**
 * Helper function to capitalize the first letter of a word
 *
 * @param word - The word to capitalize
 * @returns The word with first letter capitalized
 */
export function capitalizeFirstLetter(word: string): string {
  if (word.length === 0) return ''
  return word.charAt(0).toUpperCase() + word.slice(1)
}

// Example usage
// const title = "the lord of the rings: the return of the king";
// console.log(titlize(title)); // "The Lord of the Rings: The Return of the King"
