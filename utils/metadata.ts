// https://github.com/fuma-nama/fumadocs/blob/a86feedcfbba0ce4b357f274f3b7776f303e17d2/apps/docs/utils/metadata.ts

import type { Metadata } from 'next/types'

import type { Dictionary } from '@/lib/dictionaries-server'

export function createMetadata(override: Metadata, dictionary?: Dictionary): Metadata {
  const siteName = dictionary?.common.title ?? 'HyperAI'

  return {
    ...override,
    openGraph: {
      title: override.title ?? undefined,
      description: override.description ?? undefined,
      url: 'https://hyper.ai',
      images: '/banner.jpg',
      siteName,
      ...override.openGraph,
    },
    twitter: {
      card: 'summary_large_image',
      creator: '@HyperAI_News',
      title: override.title ?? undefined,
      description: override.description ?? undefined,
      images: '/banner.jpg',
      ...override.twitter,
    },
  }
}

export const baseUrl =
  process.env['NODE_ENV'] === 'development' || !process.env['VERCEL_URL']
    ? new URL('http://localhost:3000')
    : new URL(`https://${process.env['VERCEL_URL']}`)
