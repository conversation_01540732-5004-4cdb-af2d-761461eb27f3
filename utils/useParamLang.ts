'use client'

import { useParams } from 'next/navigation'

type UseParamLangReturn = {
  lang: string
  locale: string
}

export default function useParamLang(): UseParamLangReturn {
  const params = useParams<{ lang: string }>()
  const lang = params?.lang === 'cn' ? 'zh' : params?.lang || 'en'

  // Map language code to locale
  const localeMap: Record<string, string> = {
    en: 'en-US',
    zh: 'zh-CN',
    ja: 'ja-JP',
    kr: 'ko-KR',
    ar: 'ar-AE',
    fr: 'fr-FR',
    de: 'de-DE',
    // Add more languages as needed
  }

  const locale = localeMap[lang] || 'en-US'

  return {
    lang,
    locale,
  }
}
