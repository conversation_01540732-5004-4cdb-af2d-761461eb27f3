'use client'

import { useParams } from 'next/navigation'
import useS<PERSON> from 'swr'

import type { HyperApiPost } from '@/types'

import { API_PROXY_BASE } from '@/lib/constants'
import { defaultLocale } from '@/lib/locales'

import fetcher from '@/utils/swrFetcherWithHeaders'

function useRelatedPosts(id: number | string, limit = 9) {
  const params = useParams<{ lang: string }>()
  const resolvedApiPrefix = params?.lang && params.lang !== defaultLocale ? `/${params.lang}` : ''

  const postId = typeof id === 'string' ? Number(id) : id

  const url = `${API_PROXY_BASE}${resolvedApiPrefix}/wp-json/yarpp/v1/related/${postId}?limit=${limit}&_embed`

  const { data, error, isLoading, isValidating } = useSWR<{
    headers: Headers
    json: HyperApiPost[]
  }>(url, fetcher)

  return {
    data,
    isLoading,
    isValidating,
    isError: error,
  }
}

export default useRelatedPosts
