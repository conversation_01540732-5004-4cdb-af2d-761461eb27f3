import type { Fetcher } from 'swr'

// const fetcher = (...args) => fetch(...args).then(res => res.json())
const fetcher: Fetcher<any> = async (...args: Parameters<typeof fetch>) => {
  const response = await fetch(...args)
  if (!response.ok) {
    throw new Error('An error occurred while fetching the data')
  }
  return {
    headers: response.headers,
    json: await response.json(),
  }
}

export default fetcher
