import { describe, expect, it } from 'vitest'

import { capitalizeFirstLetter, titlize } from './titlize'

describe('capitalizeFirstLetter', () => {
  it('should capitalize the first letter of a word', () => {
    expect(capitalizeFirstLetter('hello')).toBe('Hello')
    expect(capitalizeFirstLetter('world')).toBe('World')
  })

  it('should handle single letter words', () => {
    expect(capitalizeFirstLetter('a')).toBe('A')
    expect(capitalizeFirstLetter('i')).toBe('I')
  })

  it('should handle empty strings', () => {
    expect(capitalizeFirstLetter('')).toBe('')
  })

  it('should handle words that are already capitalized', () => {
    expect(capitalizeFirstLetter('Hello')).toBe('Hello')
    expect(capitalizeFirstLetter('WORLD')).toBe('WORLD')
  })

  it('should handle words with special characters', () => {
    expect(capitalizeFirstLetter('123abc')).toBe('123abc')
    expect(capitalizeFirstLetter('!hello')).toBe('!hello')
  })
})

describe('titlize', () => {
  describe('basic title case rules', () => {
    it('should capitalize first and last words', () => {
      expect(titlize('the quick brown fox jumps over the lazy dog')).toBe('The Quick Brown Fox Jumps Over the Lazy Dog')
      expect(titlize('of mice and men')).toBe('Of Mice and Men')
      expect(titlize('gone with the wind')).toBe('Gone With the Wind')
    })

    it('should lowercase articles in the middle', () => {
      expect(titlize('a tale of two cities')).toBe('A Tale of Two Cities')
      expect(titlize('the lord of the rings')).toBe('The Lord of the Rings')
      expect(titlize('an unexpected journey')).toBe('An Unexpected Journey')
    })

    it('should lowercase coordinating conjunctions in the middle', () => {
      expect(titlize('romeo and juliet')).toBe('Romeo and Juliet')
      expect(titlize('pride and prejudice')).toBe('Pride and Prejudice')
      expect(titlize('war and peace')).toBe('War and Peace')
      expect(titlize('sense and sensibility')).toBe('Sense and Sensibility')
    })

    it('should lowercase short prepositions in the middle', () => {
      expect(titlize('lord of the flies')).toBe('Lord of the Flies')
      expect(titlize('catcher in the rye')).toBe('Catcher in the Rye')
      expect(titlize('to kill a mockingbird')).toBe('To Kill a Mockingbird')
      expect(titlize('alice in wonderland')).toBe('Alice in Wonderland')
    })

    it('should capitalize longer prepositions', () => {
      expect(titlize('journey through the mountains')).toBe('Journey Through the Mountains')
      expect(titlize('beneath the surface')).toBe('Beneath the Surface')
      expect(titlize('beyond the horizon')).toBe('Beyond the Horizon')
    })
  })

  describe('custom capitalization preservation', () => {
    it('should preserve acronyms and all-caps words', () => {
      expect(titlize('working with NVIDIA GPUs')).toBe('Working With NVIDIA GPUs')
      expect(titlize('IBM and AWS partnership')).toBe('IBM and AWS Partnership')
      expect(titlize('the AI revolution')).toBe('The AI Revolution')
    })

    it('should preserve camelCase and mixed case words', () => {
      expect(titlize('using vLLM for inference')).toBe('Using vLLM for Inference')
      expect(titlize('iPhone and iPad development')).toBe('iPhone and iPad Development')
      expect(titlize('SQLite database management')).toBe('SQLite Database Management')
      expect(titlize('PyTorch and TensorFlow')).toBe('PyTorch and TensorFlow')
    })

    it('should preserve words with internal capitals', () => {
      expect(titlize('OpenAI announces ChatGPT')).toBe('OpenAI Announces ChatGPT')
      expect(titlize('DeepMind research paper')).toBe('DeepMind Research Paper')
      expect(titlize('JavaScript and TypeScript')).toBe('JavaScript and TypeScript')
    })
  })

  describe('edge cases', () => {
    it('should handle empty strings', () => {
      expect(titlize('')).toBe('')
    })

    it('should handle single word strings', () => {
      expect(titlize('hello')).toBe('Hello')
      expect(titlize('WORLD')).toBe('WORLD')
      expect(titlize('iPhone')).toBe('iPhone')
    })

    it('should handle strings with multiple spaces', () => {
      expect(titlize('the  quick  brown  fox')).toBe('The  Quick  Brown  Fox')
    })

    it('should handle strings with only lowercase words', () => {
      expect(titlize('the and of')).toBe('The and Of')
    })

    it('should handle strings with mixed punctuation', () => {
      expect(titlize('the lord of the rings: the return of the king')).toBe(
        'The Lord of the Rings: the Return of the King'
      )
    })

    it('should preserve technical identifiers containing symbols/digits', () => {
      expect(titlize('llama.cpp+open-webui deploy gpt-oss-20b')).toBe('llama.cpp+open-webui Deploy gpt-oss-20b')
      expect(titlize('llama.cpp + open-webui deploy gpt-oss-20b')).toBe('llama.cpp + open-webui Deploy gpt-oss-20b')
      expect(titlize('vite-plugin-react v3.0.0 released')).toBe('vite-plugin-react v3.0.0 Released')
      expect(titlize('node.js v22 adds --watch')).toBe('node.js v22 Adds --watch')
    })
  })

  describe('comprehensive examples', () => {
    it('should handle book titles correctly', () => {
      expect(titlize('the great gatsby')).toBe('The Great Gatsby')
      expect(titlize('one hundred years of solitude')).toBe('One Hundred Years of Solitude')
      expect(titlize('the catcher in the rye')).toBe('The Catcher in the Rye')
      expect(titlize('to kill a mockingbird')).toBe('To Kill a Mockingbird')
    })

    it('should handle technical titles correctly', () => {
      expect(titlize('introduction to machine learning with PyTorch')).toBe(
        'Introduction to Machine Learning With PyTorch'
      )
      expect(titlize('building REST APIs with Node.js')).toBe('Building REST APIs With Node.js')
      expect(titlize('AWS cloud architecture for beginners')).toBe('AWS Cloud Architecture for Beginners')
    })

    it('should handle article titles correctly', () => {
      expect(titlize('the rise of artificial intelligence in healthcare')).toBe(
        'The Rise of Artificial Intelligence in Healthcare'
      )
      expect(titlize('how to build scalable web applications')).toBe('How to Build Scalable Web Applications')
      expect(titlize('understanding GPU acceleration for deep learning')).toBe(
        'Understanding GPU Acceleration for Deep Learning'
      )
    })
  })

  describe('lowercase words list', () => {
    it('should lowercase all specified articles', () => {
      expect(titlize('give me a break')).toBe('Give Me a Break')
      expect(titlize('once upon a time')).toBe('Once Upon a Time')
      expect(titlize('under the bridge')).toBe('Under the Bridge')
    })

    it('should lowercase all specified conjunctions', () => {
      expect(titlize('rock and roll')).toBe('Rock and Roll')
      expect(titlize('now or never')).toBe('Now or Never')
      expect(titlize('slow but steady')).toBe('Slow but Steady')
      expect(titlize('young yet wise')).toBe('Young yet Wise')
    })

    it('should lowercase all specified prepositions', () => {
      expect(titlize('life at the top')).toBe('Life at the Top')
      expect(titlize('done by hand')).toBe('Done by Hand')
      expect(titlize('made for you')).toBe('Made for You')
      expect(titlize('lost in translation')).toBe('Lost in Translation')
      expect(titlize('north of the border')).toBe('North of the Border')
      expect(titlize('jumping off the cliff')).toBe('Jumping off the Cliff')
      expect(titlize('dancing on the floor')).toBe('Dancing on the Floor')
      expect(titlize('heading out now')).toBe('Heading out Now')
      expect(titlize('going to town')).toBe('Going to Town')
      expect(titlize('climbing up high')).toBe('Climbing up High')
      expect(titlize('traveling via train')).toBe('Traveling via Train')
    })
  })
})
