import { describe, expect, it } from 'vitest'

import { formatTokenContext } from './formatTokenContext'

describe('formatTokenContext', () => {
  describe('numbers less than 1000', () => {
    it('should return exact number as string', () => {
      expect(formatTokenContext(0)).toBe('0')
      expect(formatTokenContext(1)).toBe('1')
      expect(formatTokenContext(100)).toBe('100')
      expect(formatTokenContext(999)).toBe('999')
    })
  })

  describe('thousands (k)', () => {
    it('should format whole thousands without decimals', () => {
      expect(formatTokenContext(1000)).toBe('1k')
      expect(formatTokenContext(2000)).toBe('2k')
      expect(formatTokenContext(15000)).toBe('15k')
      expect(formatTokenContext(999000)).toBe('999k')
    })

    it('should format non-whole thousands with decimals', () => {
      expect(formatTokenContext(1500)).toBe('1.5k')
      expect(formatTokenContext(2500)).toBe('2.5k')
      expect(formatTokenContext(1250)).toBe('1.25k')
      expect(formatTokenContext(1234)).toBe('1.23k')
    })

    it('should remove trailing zeros from decimals', () => {
      expect(formatTokenContext(1100)).toBe('1.1k')
      expect(formatTokenContext(1200)).toBe('1.2k')
      expect(formatTokenContext(1010)).toBe('1.01k')
    })

    it('should handle edge cases near boundaries', () => {
      expect(formatTokenContext(999)).toBe('999')
      expect(formatTokenContext(1000)).toBe('1k')
      expect(formatTokenContext(999999)).toBe('1000k')
      expect(formatTokenContext(1000000)).toBe('1m')
    })
  })

  describe('millions (m)', () => {
    it('should format whole millions without decimals', () => {
      expect(formatTokenContext(1000000)).toBe('1m')
      expect(formatTokenContext(5000000)).toBe('5m')
      expect(formatTokenContext(128000000)).toBe('128m')
      expect(formatTokenContext(999000000)).toBe('999m')
    })

    it('should format non-whole millions with decimals', () => {
      expect(formatTokenContext(1500000)).toBe('1.5m')
      expect(formatTokenContext(2750000)).toBe('2.75m')
      expect(formatTokenContext(1280000)).toBe('1.28m')
      expect(formatTokenContext(1234567)).toBe('1.23m')
    })

    it('should remove trailing zeros from decimals', () => {
      expect(formatTokenContext(1100000)).toBe('1.1m')
      expect(formatTokenContext(1200000)).toBe('1.2m')
      expect(formatTokenContext(1010000)).toBe('1.01m')
    })

    it('should handle large millions', () => {
      expect(formatTokenContext(500000000)).toBe('500m')
      expect(formatTokenContext(999999999)).toBe('1000m')
    })
  })

  describe('billions (b)', () => {
    it('should format whole billions without decimals', () => {
      expect(formatTokenContext(1000000000)).toBe('1b')
      expect(formatTokenContext(5000000000)).toBe('5b')
      expect(formatTokenContext(20000000000)).toBe('20b')
    })

    it('should format non-whole billions with decimals', () => {
      expect(formatTokenContext(1500000000)).toBe('1.5b')
      expect(formatTokenContext(2750000000)).toBe('2.75b')
      expect(formatTokenContext(1234567890)).toBe('1.23b')
    })

    it('should remove trailing zeros from decimals', () => {
      expect(formatTokenContext(1100000000)).toBe('1.1b')
      expect(formatTokenContext(1200000000)).toBe('1.2b')
      expect(formatTokenContext(1010000000)).toBe('1.01b')
    })

    it('should handle very large numbers', () => {
      expect(formatTokenContext(100000000000)).toBe('100b')
      expect(formatTokenContext(999000000000)).toBe('999b')
      expect(formatTokenContext(1500000000000)).toBe('1500b')
    })
  })

  describe('rounding behavior', () => {
    it('should round to 2 decimal places', () => {
      expect(formatTokenContext(1234)).toBe('1.23k')
      expect(formatTokenContext(1235)).toBe('1.24k')
      expect(formatTokenContext(1999)).toBe('2k')
    })

    it('should handle very precise decimal values', () => {
      expect(formatTokenContext(1111)).toBe('1.11k')
      expect(formatTokenContext(1115)).toBe('1.11k') // toFixed(2) rounds down
      expect(formatTokenContext(1994)).toBe('1.99k')
      expect(formatTokenContext(1995)).toBe('2k')
    })
  })

  describe('edge cases', () => {
    it('should handle zero', () => {
      expect(formatTokenContext(0)).toBe('0')
    })

    it('should handle negative numbers', () => {
      // The function treats negative numbers as less than 1000
      expect(formatTokenContext(-1000)).toBe('-1000')
      expect(formatTokenContext(-1500000)).toBe('-1500000')
    })

    it('should handle decimal inputs', () => {
      expect(formatTokenContext(1500.5)).toBe('1.5k')
      expect(formatTokenContext(999.9)).toBe('999.9') // Less than 1000, returns as is
    })
  })

  describe('real-world examples', () => {
    it('should format common token context sizes', () => {
      expect(formatTokenContext(4096)).toBe('4.1k')
      expect(formatTokenContext(8192)).toBe('8.19k')
      expect(formatTokenContext(16384)).toBe('16.38k')
      expect(formatTokenContext(32768)).toBe('32.77k')
      expect(formatTokenContext(65536)).toBe('65.54k')
      expect(formatTokenContext(131072)).toBe('131.07k')
      expect(formatTokenContext(1048576)).toBe('1.05m')
    })
  })
})
