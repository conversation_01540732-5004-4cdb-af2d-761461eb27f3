import useSWR from 'swr'
import type { WP_Term } from 'wp-types'

import type { HyperPost } from '@/types'

import fetcher from '@/utils/swrFetcher'

import { parseOpenBayesUrl } from './parseOpenBayesUrl'

interface CategoryProps {
  term_id: number
  name: string
  slug: string
  term_group: number
  term_taxonomy_id: number
  taxonomy: string
  description: string
  parent: number
  count: number
  filter: string
}

export interface HyperPostDataset extends HyperPost {
  terms: WP_Term[]
}

export interface DatasetsList {
  category: CategoryProps
  datasets: HyperPostDataset[]
}

function useOpenBayesBridge(url: string) {
  const parsedUrl = parseOpenBayesUrl(url)
  const { base, username, reqType, job } = parsedUrl
  const validUrl = !!(base && username && reqType && job)

  const { data, error, isLoading } = useSWR<any>(
    validUrl ? `/api/openbayes/graphql-bridge/${base}/${username}/${reqType}/${job}` : null,
    fetcher
  )

  return {
    data,
    isLoading,
    isError: error,
  }
}

export default useOpenBayesBridge
