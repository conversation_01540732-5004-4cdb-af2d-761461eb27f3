'use client'

import { useEffect, useState } from 'react'

import { useTranslation } from '@/utils/i18n'

export function timeCountdown(time: number, placeholder?: string) {
  const [timeLeft, setTimeLeft] = useState<string>('…')
  const { t, translate } = useTranslation()

  useEffect(() => {
    const interval = setInterval(() => {
      updateTime(time)
    }, 1000)

    return () => clearInterval(interval)
  }, [time])

  const updateTime = (time: number) => {
    const target = new Date(time)
    const difference = target.getTime() - Date.now()

    if (difference <= 0) {
      setTimeLeft(placeholder || t.time.ended)
    } else {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft(
        `${days > 0 ? `${translate('timeObj.days', { count: days })} ` : ''}${hours > 0 ? `${translate('timeObj.hours', { count: hours })} ` : ''}${minutes > 0 ? `${translate('timeObj.minutes', { count: minutes })} ` : ''}${translate('timeObj.seconds', { count: seconds })}`
      )
    }
  }

  return timeLeft
}
