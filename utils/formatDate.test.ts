import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import { formatDate } from './formatDate'

describe('formatDate', () => {
  beforeEach(() => {
    // Mock the system timezone to ensure consistent test results
    vi.stubGlobal('Intl', {
      DateTimeFormat: vi.fn().mockImplementation((locale, options) => {
        return {
          format: (date: Date) => {
            // Return a predictable format for testing
            if (locale === 'zh-CN') {
              return '2024年01月15日 周一'
            }
            return 'Mon, 01/15/2024'
          },
          resolvedOptions: () => ({
            timeZone: 'Asia/Shanghai',
          }),
        }
      }),
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should format date with default options', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    const result = formatDate(date)
    expect(result).toBe('2024年01月15日 周一')
  })

  it('should accept string timestamp', () => {
    const result = formatDate('2024-01-15T10:30:00Z')
    expect(result).toBe('2024年01月15日 周一')
  })

  it('should use current date when no timestamp provided', () => {
    const result = formatDate()
    expect(result).toBeTruthy()
  })

  it('should respect locale option', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    const result = formatDate(date, { locale: 'en-US' })
    expect(result).toBe('Mon, 01/15/2024')
  })

  it('should handle localTime option', () => {
    const date = new Date('2024-01-15T10:30:00Z')

    // With localTime true (default)
    const localResult = formatDate(date, { localTime: true })
    expect(localResult).toBeTruthy()

    // With localTime false
    const defaultTimezoneResult = formatDate(date, { localTime: false })
    expect(defaultTimezoneResult).toBeTruthy()
  })

  it('should use custom defaultTimezone when localTime is false', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    const result = formatDate(date, {
      localTime: false,
      defaultTimezone: 'America/New_York',
    })
    expect(result).toBeTruthy()
  })

  it('should accept custom format options', () => {
    const date = new Date('2024-01-15T10:30:00Z')
    const customFormat: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }
    const result = formatDate(date, { format: customFormat })
    expect(result).toBeTruthy()
  })
})

describe('formatDate - removeMinute00 option', () => {
  beforeEach(() => {
    // Mock DateTimeFormat for these specific tests
    const mockDateTimeFormat = vi.fn().mockImplementation((locale, options) => ({
      format: (date: Date) => '2024年01月15日 10:00',
      resolvedOptions: () => ({
        timeZone: 'Asia/Shanghai',
      }),
    }))

    vi.stubGlobal('Intl', {
      DateTimeFormat: mockDateTimeFormat,
    })
  })

  it('should remove minute when it is 00 and removeMinute00 is true', () => {
    const dateWith00Minutes = new Date('2024-01-15T10:00:00Z')
    const dateWith30Minutes = new Date('2024-01-15T10:30:00Z')

    const formatOptions = {
      format: {
        hour: '2-digit',
        minute: '2-digit',
      } satisfies Intl.DateTimeFormatOptions,
      removeMinute00: true,
    }

    // This test verifies the logic, though the actual formatting
    // depends on Intl.DateTimeFormat implementation
    const result1 = formatDate(dateWith00Minutes, formatOptions)
    const result2 = formatDate(dateWith30Minutes, formatOptions)

    expect(result1).toBeTruthy()
    expect(result2).toBeTruthy()
  })

  it('should keep minute when removeMinute00 is false', () => {
    const date = new Date('2024-01-15T10:00:00Z')
    const result = formatDate(date, {
      format: {
        hour: '2-digit',
        minute: '2-digit',
      },
      removeMinute00: false,
    })
    expect(result).toBeTruthy()
  })
})
