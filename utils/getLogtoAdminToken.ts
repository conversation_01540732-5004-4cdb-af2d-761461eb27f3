/**
 * Fetch token from Logto authentication service
 *
 * @link https://openapi.logto.io/authentication
 */

export async function getLogtoAdminToken(): Promise<any> {
  const url = `${process.env['LOGTO_ENDPOINT']}/oidc/token`

  if (
    !process.env['LOGOTO_ADMIN_CLIENT_ID'] ||
    !process.env['LOGOTO_ADMIN_CLIENT_SECRET'] ||
    !process.env['LOGOTO_ADMIN_RESOURCE'] ||
    !process.env['LOGOTO_ADMIN_SCOPE']
  ) {
    throw new Error('Missing required environment variables')
  }

  const params = new URLSearchParams()
  params.append('grant_type', 'client_credentials')
  params.append('client_id', process.env['LOGOTO_ADMIN_CLIENT_ID']!)
  params.append('client_secret', process.env['LOGOTO_ADMIN_CLIENT_SECRET']!)
  params.append('resource', process.env['LOGOTO_ADMIN_RESOURCE']!)
  params.append('scope', process.env['LOGOTO_ADMIN_SCOPE']!)

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching token:', error)
    throw error
  }
}
