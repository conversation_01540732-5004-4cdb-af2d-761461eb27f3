// Utility functions for AI model-related operations

export function generateModelSlug(modelId: string): string {
  return modelId
    .toLowerCase()
    .replace(/\//g, '-') // Replace forward slashes with hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '') // Remove special characters
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

export function findModelBySlug(slug: string, modelList: Array<{ id: string }>): any {
  return modelList.find(model => {
    if (!model.id) return false
    return generateModelSlug(model.id) === slug
  })
}

// Extract provider name from model ID
export function extractProviderFromId(modelId: string): string {
  const parts = modelId.split('/')
  return parts[0] || ''
}

// Extract model name from model ID
export function extractModelNameFromId(modelId: string): string {
  const parts = modelId.split('/')
  return parts[1] || modelId
}
