import { describe, expect, it } from 'vitest'

import { formatBytes } from './formatBytes'

describe('formatBytes', () => {
  it('should return "0 Bytes" for 0 bytes', () => {
    expect(formatBytes(0)).toBe('0 Bytes')
  })

  it('should format bytes correctly without decimals specified', () => {
    expect(formatBytes(1)).toBe('1 字节')
    expect(formatBytes(1023)).toBe('1023 字节')
    expect(formatBytes(1024)).toBe('1 KB')
    expect(formatBytes(1536)).toBe('1.5 KB')
  })

  it('should format different size units correctly', () => {
    expect(formatBytes(1024)).toBe('1 KB')
    expect(formatBytes(1048576)).toBe('1 MB') // 1024 * 1024
    expect(formatBytes(1073741824)).toBe('1 GB') // 1024^3
    expect(formatBytes(1099511627776)).toBe('1 TB') // 1024^4
    expect(formatBytes(1125899906842624)).toBe('1 PB') // 1024^5
  })

  it('should respect decimal places parameter', () => {
    expect(formatBytes(1536, 0)).toBe('1.5 KB') // Uses default dm = 2 when decimals = 0
    expect(formatBytes(1536, 1)).toBe('1.5 KB')
    expect(formatBytes(1536, 2)).toBe('1.5 KB')
    expect(formatBytes(1536, 3)).toBe('1.5 KB')
    expect(formatBytes(1555, 3)).toBe('1.519 KB')
  })

  it('should handle large numbers correctly', () => {
    const largeNumber = 5.5 * Math.pow(1024, 5) // 5.5 PB
    expect(formatBytes(largeNumber)).toBe('5.5 PB')
    expect(formatBytes(largeNumber, 1)).toBe('5.5 PB')
    expect(formatBytes(largeNumber, 0)).toBe('5.5 PB') // decimals = 0 uses default dm = 2
  })

  it('should handle very small kilobyte values', () => {
    expect(formatBytes(1025)).toBe('1 KB')
    expect(formatBytes(1124)).toBe('1.1 KB')
    expect(formatBytes(1224)).toBe('1.2 KB')
  })

  it('should handle edge cases between units', () => {
    expect(formatBytes(1023)).toBe('1023 字节')
    expect(formatBytes(1024)).toBe('1 KB')
    expect(formatBytes(1048575)).toBe('1024 KB')
    expect(formatBytes(1048576)).toBe('1 MB')
  })

  it('should display Chinese unit names', () => {
    expect(formatBytes(100)).toBe('100 字节')
    expect(formatBytes(2048)).toBe('2 KB')
    expect(formatBytes(2097152)).toBe('2 MB')
  })
})
