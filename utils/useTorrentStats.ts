import useSWR from 'swr'

import { API_PROXY_BASE } from '@/lib/constants'

import fetcher from '@/utils/swrFetcher'

export interface TorrentStats {
  seeding: number
  downloading: number
  completed: number
  traffic: string
}

function useTorrentStats() {
  const url = `${API_PROXY_BASE}/wp-json/hyperai/v1/datasets/stats`
  const { data, error, isLoading } = useSWR<TorrentStats>(url, fetcher)

  return {
    data,
    isLoading,
    isError: error,
  }
}

export default useTorrentStats
