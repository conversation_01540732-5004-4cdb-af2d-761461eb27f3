// https://codesandbox.io/s/github/algolia/autocomplete/tree/next/examples/instantsearch?file=/src/debounce.ts
export function debounce<TParams>(fn: (...params: TParams[]) => void, time: number) {
  let timerId: ReturnType<typeof setTimeout> | undefined = undefined

  return function (...args: TParams[]) {
    if (timerId) {
      clearTimeout(timerId)
    }

    timerId = setTimeout(() => fn(...args), time)
  }
}
