'use client'

import { useParams } from 'next/navigation'
import useS<PERSON> from 'swr'

import type { HyperCustomTag, PostType } from '@/types'

import { API_PROXY_BASE } from '@/lib/constants'
import { defaultLocale } from '@/lib/locales'

import fetcher from '@/utils/swrFetcher'

function useTags({
  type,
  perPage = 100,
  page = 1,
}: {
  // TODO: type will not filter the result actually, need a custom hook for it.
  type: PostType
  perPage?: number
  page?: number
}) {
  const params = useParams<{ lang: string }>()
  const resolvedApiPrefix = params?.lang && params.lang !== defaultLocale ? `/${params.lang}` : ''

  // const url = `${API_PROXY_BASE}${resolvedApiPrefix}/wp-json/wp/v2/tags?post_type=${type}&per_page=${perPage}&hide_empty=1&orderby=count&order=desc&_fields=id,count,name,slug`
  const url = `${API_PROXY_BASE}${resolvedApiPrefix}/wp-json/hyperai/v1/tags?post_type=${type}&per_page=${perPage}`

  const { data, error, isLoading } = useSWR<HyperCustomTag[]>(url, fetcher)

  return {
    data,
    isLoading,
    isError: error,
  }
}

export default useTags
