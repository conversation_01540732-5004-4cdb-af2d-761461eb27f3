// client side i18n helpers

'use client'

import { createContext, Fragment, useContext, useMemo } from 'react'

import type { Locale } from '@/types'

import defaultDict from '@/app/[lang]/dictionaries/en'

// Define dictionary type based on the structure in dictionary files
// Import this type to ensure consistent typing across the app
export type Dictionary = typeof defaultDict

type I18nContextType = {
  dictionary: Dictionary
  locale: Locale
}

const I18nContext = createContext<I18nContextType | null>(null)

export function I18nProvider({
  children,
  dictionary,
  locale,
}: {
  children: React.ReactNode
  dictionary: Dictionary
  locale: Locale
}) {
  return <I18nContext.Provider value={{ dictionary, locale }}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const context = useContext(I18nContext)

  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider')
  }

  return context
}

/**
 * Replace placeholders in a template string with values
 * Placeholders are in the format {{key}}
 * @example interpolate("Hello {{name}}", { name: "World" }) => "Hello World"
 * @example interpolate("Hello {{name}}", { name: <strong>World</strong> }) => "Hello <strong>World</strong>"
 */
export function interpolate(template: string, values?: Record<string, React.ReactNode>): React.ReactNode {
  if (!values || !template.includes('{{')) return template

  // Check if we need React node processing or just string replacement
  const hasReactNodes = Object.values(values).some(value => value !== null && typeof value === 'object')

  if (!hasReactNodes) {
    // Simple string replacement for better performance
    return template.replace(/\{\{(\w+)\}\}/g, (_, key) => (key in values ? String(values[key]) : `{{${key}}}`))
  }

  // Handle complex React nodes by splitting template
  const parts = template.split(/(\{\{\w+\}\})/g)
  if (parts.length === 1) return template

  return parts.map((part, i) => {
    const match = part.match(/\{\{(\w+)\}\}/)
    if (!match || !match[1]) return part || null

    const key = match[1]
    return key in values ? <Fragment key={i}>{values[key]}</Fragment> : part
  })
}

/**
 * Hook that automatically gets the current locale from URL params
 * and returns the appropriate dictionary, with template string support
 */
export function useTranslation() {
  const { dictionary, locale } = useI18n()

  /**
   * Access a value from the dictionary using a dot-notation path
   */
  const getPath = (path: string): unknown => {
    const keys = path.split('.')
    let value: any = dictionary

    for (const key of keys) {
      if (value === undefined || value === null) return undefined
      value = value[key]
    }

    return value
  }

  /**
   * Translates a key or returns fallback. Supports template values.
   * @example translate('common.hello', { name: 'World' }) -> "Hello World"
   * @example translate('common.hello', { name: <strong>World</strong> }) -> React nodes with formatted content
   */
  const translate = useMemo(() => {
    return (key: string, values?: Record<string, React.ReactNode>, fallback?: string): React.ReactNode => {
      const result = getPath(key)

      if (typeof result === 'string') {
        return interpolate(result, values)
      }

      return fallback || key
    }
  }, [dictionary])

  return {
    /** The translation dictionary */
    t: dictionary,
    /** The current locale */
    locale,
    /** Enhanced translation function with template and React node support */
    translate,
  }
}
