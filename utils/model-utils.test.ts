import { describe, expect, it } from 'vitest'

import { extractModelNameFromId, extractProviderFromId, findModelBySlug, generateModelSlug } from './model-utils'

describe('generateModelSlug', () => {
  it('should convert model ID to lowercase', () => {
    expect(generateModelSlug('OpenAI/GPT-4')).toBe('openai-gpt-4')
    expect(generateModelSlug('ANTHROPIC/Claude-3')).toBe('anthropic-claude-3')
  })

  it('should replace forward slashes with hyphens', () => {
    expect(generateModelSlug('provider/model')).toBe('provider-model')
    expect(generateModelSlug('provider/sub/model')).toBe('provider-sub-model')
  })

  it('should replace spaces with hyphens', () => {
    expect(generateModelSlug('Open AI/GPT 4')).toBe('open-ai-gpt-4')
    expect(generateModelSlug('Model with spaces')).toBe('model-with-spaces')
  })

  it('should remove special characters', () => {
    expect(generateModelSlug('model@#$%/test!123')).toBe('model-test123')
    expect(generateModelSlug('provider/model_v2.0')).toBe('provider-modelv20')
  })

  it('should replace multiple consecutive hyphens with single hyphen', () => {
    expect(generateModelSlug('model---test')).toBe('model-test')
    expect(generateModelSlug('provider//model')).toBe('provider-model')
    expect(generateModelSlug('test   multiple   spaces')).toBe('test-multiple-spaces')
  })

  it('should handle edge cases', () => {
    expect(generateModelSlug('')).toBe('')
    expect(generateModelSlug('   ')).toBe('-') // Spaces become hyphens, then trimmed
    expect(generateModelSlug('---')).toBe('-') // Multiple hyphens become single
    expect(generateModelSlug('123')).toBe('123')
  })

  it('should trim the result', () => {
    expect(generateModelSlug(' model/test ')).toBe('-model-test-') // trim() only removes whitespace, not hyphens
    expect(generateModelSlug('-model-')).toBe('-model-') // trim() doesn't remove hyphens
  })
})

describe('findModelBySlug', () => {
  const modelList: { id: string; name: string }[] = [
    { id: 'OpenAI/GPT-4', name: 'GPT-4' },
    { id: 'Anthropic/Claude-3', name: 'Claude 3' },
    { id: 'Google/PaLM-2', name: 'PaLM 2' },
    { id: '', name: 'Empty ID Model' },
  ]

  it('should find model by exact slug match', () => {
    const result = findModelBySlug('openai-gpt-4', modelList)
    expect(result).toEqual({ id: 'OpenAI/GPT-4', name: 'GPT-4' })
  })

  it('should find model with different casing', () => {
    const result = findModelBySlug('anthropic-claude-3', modelList)
    expect(result).toEqual({ id: 'Anthropic/Claude-3', name: 'Claude 3' })
  })

  it('should return undefined for non-existent slug', () => {
    const result = findModelBySlug('non-existent-model', modelList)
    expect(result).toBeUndefined()
  })

  it('should handle models without ID', () => {
    const result = findModelBySlug('invalid-model', modelList)
    expect(result).toBeUndefined()
  })

  it('should handle empty model list', () => {
    const result = findModelBySlug('any-slug', [])
    expect(result).toBeUndefined()
  })

  it('should handle empty slug', () => {
    const result = findModelBySlug('', modelList)
    expect(result).not.toEqual({ id: '', name: 'Empty ID Model' })
  })
})

describe('extractProviderFromId', () => {
  it('should extract provider from standard model ID', () => {
    expect(extractProviderFromId('OpenAI/GPT-4')).toBe('OpenAI')
    expect(extractProviderFromId('Anthropic/Claude-3')).toBe('Anthropic')
    expect(extractProviderFromId('Google/PaLM-2')).toBe('Google')
  })

  it('should handle IDs with multiple slashes', () => {
    expect(extractProviderFromId('Provider/Sub/Model')).toBe('Provider')
    expect(extractProviderFromId('A/B/C/D')).toBe('A')
  })

  it('should return empty string for IDs without slash', () => {
    expect(extractProviderFromId('ModelWithoutProvider')).toBe('ModelWithoutProvider') // Returns first part even without slash
    expect(extractProviderFromId('single-name')).toBe('single-name') // Returns first part even without slash
  })

  it('should handle edge cases', () => {
    expect(extractProviderFromId('')).toBe('')
    expect(extractProviderFromId('/')).toBe('')
    expect(extractProviderFromId('/Model')).toBe('')
    expect(extractProviderFromId('Provider/')).toBe('Provider')
  })
})

describe('extractModelNameFromId', () => {
  it('should extract model name from standard model ID', () => {
    expect(extractModelNameFromId('OpenAI/GPT-4')).toBe('GPT-4')
    expect(extractModelNameFromId('Anthropic/Claude-3')).toBe('Claude-3')
    expect(extractModelNameFromId('Google/PaLM-2')).toBe('PaLM-2')
  })

  it('should return full ID when no slash present', () => {
    expect(extractModelNameFromId('ModelWithoutProvider')).toBe('ModelWithoutProvider')
    expect(extractModelNameFromId('single-name')).toBe('single-name')
  })

  it('should handle IDs with multiple slashes', () => {
    expect(extractModelNameFromId('Provider/Sub/Model')).toBe('Sub')
    expect(extractModelNameFromId('A/B/C/D')).toBe('B')
  })

  it('should handle edge cases', () => {
    expect(extractModelNameFromId('')).toBe('')
    expect(extractModelNameFromId('/')).toBe('/') // parts[1] || modelId returns modelId when parts[1] is undefined
    expect(extractModelNameFromId('/Model')).toBe('Model')
    expect(extractModelNameFromId('Provider/')).toBe('Provider/') // parts[1] is '' but || returns modelId
  })
})
