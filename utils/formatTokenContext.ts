/**
 * Formats a token count into a human-readable shorthand string
 * @param tokenCount - The number of tokens to format
 * @returns A formatted string representation (e.g., "4k", "1.28m")
 */
export function formatTokenContext(tokenCount: number): string {
  // For counts less than 1000, return the exact number
  if (tokenCount < 1000) {
    return tokenCount.toString()
  }

  // For counts between 1,000 and 999,999, format as "Xk"
  if (tokenCount < 1000000) {
    const inK = tokenCount / 1000
    // If it's a whole number, return without decimal places
    if (Math.floor(inK) === inK) {
      return `${inK}k`
    }
    // Otherwise, format with up to 2 decimal places, removing trailing zeros
    return `${inK.toFixed(2).replace(/\.?0+$/, '')}k`
  }

  // For counts between 1,000,000 and 999,999,999, format as "Xm"
  if (tokenCount < 1000000000) {
    const inM = tokenCount / 1000000
    // If it's a whole number, return without decimal places
    if (Math.floor(inM) === inM) {
      return `${inM}m`
    }
    // Otherwise, format with up to 2 decimal places, removing trailing zeros
    return `${inM.toFixed(2).replace(/\.?0+$/, '')}m`
  }

  // For counts of 1,000,000,000 or more, format as "Xb"
  const inB = tokenCount / 1000000000
  // If it's a whole number, return without decimal places
  if (Math.floor(inB) === inB) {
    return `${inB}b`
  }
  // Otherwise, format with up to 2 decimal places, removing trailing zeros
  return `${inB.toFixed(2).replace(/\.?0+$/, '')}b`
}
