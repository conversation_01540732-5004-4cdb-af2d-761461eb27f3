'use client'

import { useParams } from 'next/navigation'
import useS<PERSON> from 'swr'

import type { HyperCustomTag, PostType } from '@/types'

import { defaultLocale } from '@/lib/locales'

import fetcher from '@/utils/swrFetcher'

function useTags({
  type,
  perPage = 100,
  page = 1,
}: {
  // TODO: type will not filter the result actually, need a custom hook for it.
  type: PostType
  perPage?: number
  page?: number
}) {
  const params = useParams<{ lang: string }>()
  const lang = params?.lang || defaultLocale

  // Use our new API route instead of directly calling the origin server
  const { data, error, isLoading } = useSWR<HyperCustomTag[]>(
    `/api/tags?post_type=${type}&per_page=${perPage}&lang=${lang}`,
    fetcher
  )

  return {
    data,
    isLoading,
    isError: error,
  }
}

export default useTags
