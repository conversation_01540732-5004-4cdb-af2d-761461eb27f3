// copied from lapalce-workers

import type { Model<PERSON>ithMeta, ModelWithPartialMeta } from '@/types/ai'

/**
 * Extract provider name and model name from <provider>/<model> based model id
 */
export function getModelInfo(model: ModelWithMeta | ModelWithPartialMeta): {
  /**
   * Model provider which hosts the model, used to determine which model provider to use
   *
   * The provider string is parsed from the first part of `model.id`, will be fallback to `model.hosted_by` or
   * `model.owned_by` if not found in the `model.id` part
   */
  modelProvider: string
  /**
   * Model name that can be passed to the model provider
   *
   * It can be fallback to `model.provider_id` if it exists, which should not be exposed in the UI
   * When use this value in client side it will always equal to `modelNameUI` because `provider_id` is not exposed in the UI
   */
  modelName: string
  /**
   * Model name that can be exposed in the UI
   */
  modelNameUI: string
} {
  const modelParts = model.id.split('/')
  const modelProvider = modelParts[0] || model.hosted_by || model.owned_by
  const modelName =
    'provider_id' in model && model.provider_id
      ? model.provider_id
      : modelParts.length > 1
        ? modelParts.slice(1).join('/')
        : model.id

  const modelNameUI = modelParts.length > 1 ? modelParts.slice(1).join('/') : model.id

  return { modelProvider, modelName, modelNameUI }
}
