export type ParsedOpenBayesUrl = {
  base: string
  username: string
  reqType: string
  job: string
}

export function parseOpenBayesUrl(url: string): ParsedOpenBayesUrl {
  if (url) {
    const parsedUrl = new URL(url)
    const segments = parsedUrl.pathname.split('/').filter(seg => seg.length > 0)

    return {
      base: segments[0]!,
      username: segments[1]!,
      reqType: segments[2]!,
      job: segments[3]!,
    }
  } else {
    return {
      base: '',
      username: '',
      reqType: '',
      job: '',
    }
  }
}
