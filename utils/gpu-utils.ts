// Utility functions for GPU-related operations

export function generateGPUSlug(gpuName: string): string {
  return gpuName
    .toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '') // Remove special characters
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

export function findGPUBySlug(slug: string, gpuList: Array<{ GPU: string | null }>): any {
  return gpuList.find(gpu => {
    if (!gpu.GPU) return false
    return generateGPUSlug(gpu.GPU) === slug
  })
}
