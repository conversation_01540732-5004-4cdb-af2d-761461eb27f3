import type { NextRequest } from 'next/server'

export function getClientIp(request: NextRequest) {
  // Check various headers for the real IP address
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')

  // x-forwarded-for can contain multiple IPs, take the first one
  if (forwardedFor) {
    return forwardedFor.split(',')[0]?.trim()
  }

  // Check other headers
  if (realIp) return realIp
  if (cfConnectingIp) return cfConnectingIp

  // Fallback to request.ip if available
  if ('ip' in request && typeof request.ip === 'string') {
    return request.ip
  }

  return ''
}
