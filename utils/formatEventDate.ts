'use client'

import { formatDate } from '@/utils/formatDate'
import useParamLang from '@/utils/useParamLang'

export function formatEventDate(date: string) {
  const { locale } = useParamLang()

  return formatDate(new Date(date), {
    locale,
    format: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    },
  })
}
