import { describe, expect, it } from 'vitest'

import { parseOpenBayesUrl } from './parseOpenBayesUrl'

describe('parseOpenBayesUrl', () => {
  it('should parse a valid OpenBayes URL correctly', () => {
    const url = 'https://openbayes.com/console/johndoe/projects/my-project'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'johndo<PERSON>',
      reqType: 'projects',
      job: 'my-project',
    })
  })

  it('should parse URLs with different paths', () => {
    const url = 'https://openbayes.com/api/v1/users/datasets/dataset-123'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'api',
      username: 'v1',
      reqType: 'users',
      job: 'datasets',
    })
  })

  it('should handle URLs with trailing slashes', () => {
    const url = 'https://openbayes.com/workspace/alice/notebooks/notebook-1/'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'workspace',
      username: 'alice',
      reqType: 'notebooks',
      job: 'notebook-1',
    })
  })

  it('should handle URLs with query parameters', () => {
    const url = 'https://openbayes.com/console/bob/jobs/training?status=running'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'bob',
      reqType: 'jobs',
      job: 'training',
    })
  })

  it('should handle URLs with hash fragments', () => {
    const url = 'https://openbayes.com/docs/user/guide/quickstart#installation'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'docs',
      username: 'user',
      reqType: 'guide',
      job: 'quickstart',
    })
  })

  it('should return empty strings when URL is empty', () => {
    const result = parseOpenBayesUrl('')

    expect(result).toEqual({
      base: '',
      username: '',
      reqType: '',
      job: '',
    })
  })

  it('should handle URLs with encoded characters', () => {
    const url = 'https://openbayes.com/console/user%20name/projects/my%2Dproject'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'user%20name',
      reqType: 'projects',
      job: 'my%2Dproject',
    })
  })

  it('should handle URLs with multiple consecutive slashes', () => {
    const url = 'https://openbayes.com//console//user//projects//project1'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'user',
      reqType: 'projects',
      job: 'project1',
    })
  })

  it('should handle URLs with fewer than 4 path segments', () => {
    const url = 'https://openbayes.com/console/user'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'user',
      reqType: undefined,
      job: undefined,
    })
  })

  it('should handle URLs with more than 4 path segments', () => {
    const url = 'https://openbayes.com/console/user/projects/project1/files/data.csv'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'user',
      reqType: 'projects',
      job: 'project1',
    })
  })

  it('should handle different protocols', () => {
    const url = 'http://openbayes.com/console/user/projects/project1'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'console',
      username: 'user',
      reqType: 'projects',
      job: 'project1',
    })
  })

  it('should handle subdomain URLs', () => {
    const url = 'https://api.openbayes.com/v2/users/datasets/data'
    const result = parseOpenBayesUrl(url)

    expect(result).toEqual({
      base: 'v2',
      username: 'users',
      reqType: 'datasets',
      job: 'data',
    })
  })
})
